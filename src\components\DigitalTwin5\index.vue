<template>
  <div class="ue4map" ref="Twin" v-loading="homeLoading" element-loading-text="演进数据加载中"
       element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.1)">
    <div id="player"></div>
    <i class="el-icon-setting" @click="() => {showFuncBox = !showFuncBox;}
    "style="position: fixed; top: 70px; right: 15px; color: #fff; z-index: 9999">
    </i>
    <div class="funcBtnBox" v-show="showFuncBox">
      <el-collapse accordion>
        <el-collapse-item title="WDP5功能测试">
          <el-button @click="getCameraInfo()">获取当前镜头信息</el-button>
          <el-button @click="GetCameraLimit()">获取相机Limit值</el-button>
        </el-collapse-item>
        <!--        <el-collapse-item title="地图标点">-->
        <!--          <el-button @click="startGetCoord(undefined, undefined, 2)">开启地图标点</el-button>-->
        <!--          <el-button @click="endGetCoord()">关闭地图标点</el-button>-->
        <!--        </el-collapse-item>-->
        <!--        <el-collapse-item title="外设监控">-->
        <!--          <el-button @click="startKeyboard()">注册键盘事件</el-button>-->
        <!--          <el-button @click="removeKeyboard()">删除键盘事件</el-button>-->
        <!--        </el-collapse-item>-->
        <el-collapse-item title="镜头动作">
        <!--          <el-button @click="sceneRoam()">开始镜头漫游</el-button>-->
              <el-button @click="startCameraRoam()">开始镜头漫游</el-button>
        <!--          <el-button @click="setCameraRoamState('pause')">暂停镜头漫游</el-button>-->
        <!--          <el-button @click="setCameraRoamState('continue')">继续镜头漫游</el-button>-->
        <!--          <el-button @click="setCameraRoamState('stop')">结束镜头漫游</el-button>-->
        <!--          <el-button @click="setCameraRotate(50, 'clockwise')">镜头绕场景中心旋转开始</el-button>-->
        <!--          <el-button @click="setCameraRotate(50, 'stop')">镜头绕场景中心旋转结束</el-button>-->
        <!--          <el-button @click="getCameraParams()">获取当前镜头信息</el-button>-->
        <!--          <el-button @click="resetCameraSpace('default')">边界限制</el-button>-->
        <!--          <el-button @click="resetCameraSpace('free')">开发边界限制</el-button>-->
        </el-collapse-item>
        <!--        <el-collapse-item title="场景渲染">-->
        <!--          <el-button @click="initMap()">场景渲染</el-button>-->
        <!--          <el-button @click="stopRenderCloud()">停止渲染</el-button>-->
        <!--          <el-button @click="showHideUEFrameRate">显示/隐藏场景帧率</el-button>-->
        <!--          <el-button @click="setRenderQuality('low')">低场景渲染</el-button>-->
        <!--          <el-button @click="setRenderQuality('medium')">中场景渲染</el-button>-->
        <!--          <el-button @click="setRenderQuality('high')">高场景渲染</el-button>-->
        <!--          <el-button @click="setRenderQuality('epic')">超高场景渲染</el-button>-->
        <!--        </el-collapse-item>-->
        <!--        <el-collapse-item title="覆盖物">-->
        <!--          <el-button @click="addRoadHeat()">添加路径热力图</el-button>-->
        <!--          <el-button @click="updateRoadHeat()">更新路径热力图</el-button>-->
        <!--          <el-button @click="getFullSceenCoveringId()">获取屏幕内所有覆盖物ID</el-button>-->
        <!--          <el-button @click="updateCustomPOILabel()">更新label</el-button>-->
        <!--        </el-collapse-item>-->
        <el-collapse-item title="WDP5-洪水演进">
          <el-button @click="createRibbon()">1、创建热力色带</el-button>
          <el-button @click="createMaterial()">2、创建热力材质</el-button>
          <!--          <el-button @click="loadFloodData()">3、加载洪水演进数据</el-button>-->
          <!--          <el-button @click="playFloodEfficacy()">4、播放洪水效果</el-button>-->
          <el-button @click="loadHeatData('1915586328423682048')">3、加载热力数据</el-button>
          <el-button @click="playHeatEfficacy()">4、播放热力效果</el-button>
          <el-button @click="showHeatEfficacy(false)">5、隐藏热力效果</el-button>
          <el-button @click="drawvillages(33)">绘制村庄</el-button>
          <el-button @click="drawvillageszZyline()">转移路线</el-button>
        </el-collapse-item>
        <el-collapse-item title="WDP5-水面">
          <el-button @click="showSKwater(true)">显示水库水体</el-button>
          <el-button @click="showSKwater(false)">隐藏水库水体</el-button>
          <el-input placeholder="输入水位" v-model="testWaterH"></el-input>
          <el-button @click="waterGoup(testWaterH)">调整水位</el-button>
        </el-collapse-item>
        <el-collapse-item title="WDP5-闸门/水花（项目定制）">
          <el-button @click="openFloodgates(1)">闸门-开</el-button>
          <el-button @click="openFloodgates(-0.5)">闸门-关</el-button>
          <el-button @click="showSpray(true)">显示水花</el-button>
          <el-button @click="showSpray(false)">隐藏水花</el-button>
        </el-collapse-item>
        <el-collapse-item title="测量工具">
          <el-button @click="measureTool()">测量工具</el-button>
          <el-button @click="measureAngle()">测量角度工具</el-button>
          <el-button @click="measureLength()">测量长度工具</el-button>
          <el-button @click="measureArea()">测量面积工具</el-button>
          <el-button @click="showHideCompass(true)">显示指南针</el-button>
          <el-button @click="showHideCompass(false)">隐藏指南针</el-button>
        </el-collapse-item>

        <el-collapse-item title="POI点">
          <el-row v-for="item in digitalCon.videoPoints" :key="item.id">
            <el-col :span="24" style="padding-left: 10px">
              <el-button @click="showHidePOI(item.id, 'poi', true)">显示{{ item.label }}</el-button>
            </el-col>
            <el-col :span="24" style="padding-left: 10px">
              <el-button @click="showHidePOI(item.id, 'poi', false)">隐藏{{ item.label }}</el-button>
            </el-col>
          </el-row>
        </el-collapse-item>
        <el-collapse-item title="天气">
          <el-button @click="switchWeather('Sunny')">晴天</el-button>
          <el-button @click="switchWeather('Overcast')">阴天</el-button>
          <el-button @click="switchWeather('LightRain')">小雨</el-button>
          <el-button @click="switchWeather('LightSnow')">小雪</el-button>
        </el-collapse-item>
        <el-collapse-item title="时间">
          <el-button v-for="v in 24" :key="v" @click="switchDate((v === 24 ? 0 : v) + ':00')">
            {{ v + ":00" }}
          </el-button>
        </el-collapse-item>
      </el-collapse>
    </div>
    <div v-if="showTree&&!this.$parent.boolShowMap"
         style="right: 22%;top: 100px;transition: 0.8s;z-index: 100;position: fixed;">
      <layer-select :data="treeData" ref="layerSelectRef" :expandedKeys="treeExpandedKeys"
                    :checkedKeys="treeCheckedKeys" @treeChangeSelect="treeChange" @treeNodeClick="treeNodeClick"
                    @changeMapType="changeMapType"></layer-select>
    </div>
    <div class="dt-title" v-if="showDtTitle">
      <div>数字孪生</div>
    </div>
    <div class="bj" v-if="loginAnimate">
      <div style="
          display: flex;
          flex-flow: column;
          align-items: center;
          justify-content: center;
          top: calc(50% - 135px);
          position: absolute;
          left: calc(50% - 130px);
          width: 260px;
        ">
        <div class="move">
          <img src="@/assets/images/digitalTwin/loading1.png" alt=""/>
        </div>
        <div style="color: #fff; font-size: 1rem; width: 100%">
          <div class="loadingText">加载中...</div>
          <div class="loading">
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>
    <el-dialog :title="reconnectInfo.title" :visible.sync="reconnectInfo.open" width="450px" top="100px" append-to-body
               :close-on-click-modal="false">
      <el-form inline class="reconnect-class">
        <el-form-item>
          <label>{{ reconnectInfo.label }}</label>
        </el-form-item>
        <el-form-item label="服务地址">
          <el-select v-model="reconnectInfo.url">
            <el-option v-for="(u, index) in cloudUrls" :key="index" :label="u.label" :value="u.value"/>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="
          () => {
            reconnectInfo.open = false;
          }
        ">取 消
        </el-button>
        <el-button type="primary" @click="reconnectRender">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import WdpApi from "wdpapi";
import { digitalTwinApi } from "@/components/DigitalTwin5/digitalTwinUtils.js"
import * as constants from "constants";
import LayerSelect from "@/views/map/components/mapViewer/LayerSelect.vue";
import commonDialogBox from "@/components/commonDialogBox/commonDialogBox.vue";
import {
  getMapPointListByUrl,
  getDownStation,
  getDigitalPoiUrlList,
  getMapScatterInfo,
  getViewList
} from "@/api/digitalTwin/digitalTwin";
import {getRenderList, getDataByUrl, getDataByUrlAndQuery} from "@/views/map/api/map/dt/common.js"
import {getMapElementListTwin, getHomePageJson} from "@/views/map/api/map/params";
import {getHjqqData} from '@/views/map/api/delimit.js'
import cache from '@/plugins/cache'
import {color} from 'echarts';
import {flood_routing_data} from "../../views/map/utils/mapScene/zhyHSYJ/api";
import {digitalTwinInfo} from "./digitalTwinInfo";

export default {
  name: "",
  components: {
    LayerSelect,
    commonDialogBox,
  },
  // mixins: [screenCommon],
  data() {
    return {
      testWaterH:'',// 开发工具中的水位
      homeLoading: false,
      scendZhenNum: 0,
      firstZhenNum: 0,
      controlManage: false,
      treeData: [],
      treeExpandedKeys: [],
      treeCheckedKeys: [],
      //视频弹窗
      videoOpen: false,
      //设备id
      deviceId: "",
      //通道id
      id: "",
      cloudRenderTimer: null, // 场景重新渲染定时器
      cloudRerenderSum: 0, // 重新渲染总次数 超过次数后不再重新渲染
      cloudRerenderCount: 0, // 单次重新渲染时请求次数 默认每次重连可请求三次
      cloudRender: null, //渲染对象
      shadeIss: true, //遮罩显示
      loginAnimate: true,
      percentage: 0,
      customColor: "#409eff",
      timer: "",
      showFuncBox: false,
      unInitPoiList: [],
      poiList: [],
      spdList: [],
      dmList: [],
      controlData: true,
      // 和风天气map
      weatherMap: {
        晴: "Sunny",
        晴天: "Sunny",
        多云: "Cloudy",
        少云: "PartlyCloudy",
        阴天: "Overcast",
        阴: "Overcast",
        小雨: "LightRain",
        中雨: "ModerateRain",
        大雨: "HeavyRain",
        雨夹雪: "LightSnow",
        小雪: "LightSnow",
        中雪: "ModerateSnow",
        大雪: "HeavySnow",
        雾天: "Foggy",
        雾: "Foggy",
        扬尘: "Sand",
        雾霾: "Haze",
      },
      // 和风天气城市名称
      weatherCityName: null,
      // 和风天气城市编码
      weatherCityCode: null,

      sliderVal: 0,
      oldSliderVal: 0,
      //管道控制
      value: [],
      poiId: [],
      options: {
        monitorsPoints: null,
      },
      visible: false,
      //控制放水洞显示
      state: false,
      holeShowFade: "放水洞隐藏",
      digitalCon: {},
      cloudUrls: [
        {label: "云服务", value: "https://vizservice-paas.51aes.com"},
        {label: "公司服务", value: "https://twin.zhywater.com:28889"}
      ],
      reconnectInfo: {
        open: false,
        title: null,
        label: null,
        url: null,
      },
      pi: cache.session.getJSON('projectInfo'),
      singleEncd: cache.session.getJSON("singleEncd"),
      pathList: [],
      //WDP5相关参数
      colorPointEId: null,// 色带eid
      materialEId: null,//材质eid
      floodEId: null,//洪水演进eid
    };
  },
  props: {
    digitalProps: {
      type: Object,
      default: function () {
        return {};
      },
    },
    mapCon: {
      type: Object,
      default: function () {
        return {};
      },
    },
    showTree: {
      type: Boolean,
      default: true,
    },
    showDtTitle: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    // mapCon(value) {
    //   if (value && value.treeCon && value.treeCon.treeData) {
    //     let list = [];
    //     value.treeCon.treeData.forEach((item) => {
    //       if (item.id == "ssjk") {
    //         let children = item.children;
    //         if (children) {
    //           // 去掉 实时监控中的 水库水文站、水质站、流量站 选项
    //           // 添加 水位站
    //           children = children.filter(
    //             (ci) => ci.id != "ssjk-skz" && ci.id != "ssjk-szz"
    //           );
    //           children.push({
    //             tParentId: 36,
    //             showIndex: false,
    //             showPicture: false,
    //             id: "ssjk-swz",
    //             label: "水位站",
    //             type: "2",
    //             tId: 999,
    //           });
    //           item.children = children;
    //         }
    //         list.push(item);
    //       }
    //       if (item.id == "fybz") {
    //         list.push(item);
    //       }
    //       // console.log(list, 'list');
    //     });
    //     this.treeData = list;
    //     console.log(this.treeData, 'this.treeData');
    //   }
    // },
    "$store.getters.select51": {
      handler(newVal) {
        let activeSelect = JSON.parse(newVal);
        if (activeSelect.obj.id == "ssjk") {
          if (activeSelect.obj.children.length != 0) {
            for (let x = 0; x < activeSelect.obj.children.length; x++) {
              if (activeSelect.obj.children[x].id == "ssjk-spd") {
                for (let i = 0; i < this.spdList.length; i++) {
                }
              } else if (activeSelect.obj.children[x].id == "ssjk-dmd") {
                for (let i = 0; i < this.dmList.length; i++) {
                }
              }
            }
          }
        }
        if (activeSelect.obj.id == "ssjk-spd") {
          for (let i = 0; i < this.spdList.length; i++) {
          }
        } else if (activeSelect.obj.id == "ssjk-dmd") {
          for (let i = 0; i < this.dmList.length; i++) {
          }
        }
      },
      deep: true,
      immediate: false,
    },
    '$route.name': {
      handler(newVal) {
        this.setCameraPose(newVal)
      },
    }
  },
  created() {
    this.digitalCon = this.digitalProps;

    this.getTreeMap()
    //更新渲染地址列表
    getRenderList().then((res) => {
      if (res.code == 200) {
        console.log(res.rows, 'res.rows');
        this.cloudUrls = res.rows;
      }
    });
  },
  methods: {
    //图层树
    getTreeMap() {
      const param = {
        projectCode: this.pi?.data?.projectCode
      }
      getHomePageJson(param).then(res => {
        if (res.code === 200 && res.data) {
          let mapCon = res.data;
          getMapElementListTwin(this.pi?.data?.projectCode).then(res => {
            if (res.code === 200 && res.data) {
              const dataTree = res.data;
              //转换为tree结构
              let treeCon = {
                treeData: undefined,
                checkedKeys: undefined,
                expandedKeys: undefined
              };
              treeCon.treeData = this.handleTree(dataTree, "tId", "tParentId");
              let check = [];
              for (let i = 0; i < dataTree.length; i++) {
                if (dataTree[i].showIndex) {
                  check.push(dataTree[i].id);
                }
              }
              treeCon.checkedKeys = check;
              treeCon.expandedKeys = this.expandedKeys;
              this.treeData = treeCon.treeData
              this.treeExpandedKeys = treeCon.expandedKeys
              this.treeCheckedKeys = treeCon.checkedKeys;
            }
          })
        }
      })
    },
    // 改变地图类型
    changeMapType() {
      this.$parent.changeMap2D3D('2D')
    },
    //工具集--重新设置渲染窗口大小
    resetVideoSize() {
    },
    //配置化--图层树点击开关显隐poi
    treeChange(data, flag) {
      console.log("treeChange", data, flag);
      let types = [];
      if (data.children != undefined) {
        data.children.forEach((item) => {
          types.push({id: item.id, type: item.type});
        });
      } else {
        types = [{id: data.id, type: data.type}];
      }
      console.log(types, 'types');
      if (types.length > 0) {
        this.controlPoi(this.poiList, types, flag);
      }
    },
    //控制点显隐
    controlPoi(poiArr, optionsArray, flag) {
      for (let i = 0; i < poiArr.length; i++) {
        let poi = poiArr[i];
        for (let j = 0; j < optionsArray.length; j++) {
          if (poi.entityName === optionsArray[j].id) {
            //控制点线
            poi.SetVisible(flag)
          }

        }
      }
    },
    //配置化--图层树点击节点
    treeNodeClick(data, node) {
    },
    //配置化--弹窗重连渲染
    reconnectRender() {
    },


    //配置化--进度条进行更新，用于假的进度标识
    changePercentage() {
    },
    // 配置化--初始化渲染51场景
    async initMap() {
      console.log("孪生配置：", this.digitalCon)
      // console.log(this.digitalCon.cloudUrl, this.digitalCon.orderID)
      //  orderID = '36a47E82'; //渲染口令, 在云渲染客户端上获得 (Project ID, obtained on the cloud rendering client)
      if (this.digitalCon.cloudUrl && this.digitalCon.orderID) {
        window.cloudRender = new WdpApi({
          id: "player", // 渲染容器dom id
          url: this.digitalCon.cloudUrl, // [可选] 渲染服务地址
          order: this.digitalCon.orderID, // [可选] 渲染口令
          resolution: [window.innerWidth, window.innerHeight], // [可选] 场景输出分辨率;
          debugMode: "normal", // [可选] none:不打印日志, normal:普通日志
          keyboard: { // [可选]
            normal: false, // [可选]  键盘事件(wasd方向)开启关闭
            func: false // [可选]  浏览器F1~F12功能键开启关闭
          }
        });
        try {
          //启动云渲染
          const res = await window.cloudRender.Renderer.Start()
          if (res.success) {
            //register event
            this.registerEvent()
            // Register scene events
            await this.handlerSceneRegisterEvent()
          }
        } catch (error) {
          console.error('Error during initialization:', error);
        }
      } else {
        this.$modal.msgWarning("渲染信息不能为空！");
      }
    },
    // 云渲染事件
    registerEvent() {
      //云渲染视频流监听
      window.cloudRender.Renderer.RegisterEvent([
        {
          name: 'onVideoReady', func: (res) => {
            // 视频流链接成功
            console.log('云服务连接成功, 视频流链接成功');

            window.cloudRender.Debug.SetLogMode("none");
          }
        },
        {
          name: 'onStopedRenderCloud', func: (res) => {
            // 渲染服务中断
            console.log('云渲染关闭或通信息中断, 渲染服务中断');
          }
        }
      ]);
    },
    //场景事件注册
    async handlerSceneRegisterEvent() {
      let that = this
      window.cloudRender.Renderer.RegisterSceneEvent([
        {
          name: 'OnWdpSceneIsReady', func: async (res) => {
            // { "event_name": "OnWdpSceneIsReady", "result": { "progress": 100 } }
            if (res.result.progress === 100) {
              // 场景加载完成
              console.log('场景加载成功');
              window.cloudRender.Debug.SetLogMode("normal");//设置日志级别
              window.cloudRender.CameraControl.ResetCameraLimit('Free');  // 移除视角限制
              this.createRibbon() // 初始化洪水演进 色带
              this.createMaterial() // 初始化洪水演进 材质

              this.setCameraPose("home")
              this.loginAnimate = false
              //   // this.panelShow = true
              this.$modal.msgSuccess('场景加载完成')
              this.initMapPointByUrl()
              this.rangePolyline(1, this.singleEncd)
              this.rangePolyline(2, this.singleEncd)
              await this.addText3D(
                [
                  {
                    lon: 119.081229,
                    lat: 36.619809,
                    height: 19,
                    text: "溢洪闸",
                    pitch: 0,
                    yaw: 90,
                    roll: 0,
                    color: "#E8E8E8"
                  },
                  {
                    lon: 119.0818505220358,
                    lat: 36.62161198724779,
                    height: 5,
                    text: "溢洪道",
                    pitch: -80,
                    yaw: 30,
                    roll: 0,
                    color: "#E8E8E8"
                  }
                ]
              )
            }
          }
        },
        {
          name: 'OnWdpSceneChanged', func: async (res) => {
            // 实体对象操作后回调；
            // res.result --> {added[object]，updated[object]，removed[object]}
          }
        },
        {
          name: 'OnMouseEnterEntity', func: async (res) => {

          }
        },
        {
          name: 'OnMouseOutEntity', func: async (res) => {
            // 鼠标滑出实体事件回调; 包含数据信息与实体对象
          }
        },
        {
          name: 'OnEntityClicked', func: async (res) => {
            // 覆盖物被点击事件回调; 包含数据信息与实体对象
            console.log(res, 'res OnEntityClicked');
            // console.log(JSON.parse(res.result.object.customData), 'JSON.parse(res.result.object.customData)');
            const returnData = {pickPropty: JSON.parse(JSON.parse(res.result.object.customData).data)}
            this.$emit("digitalPoiClick", returnData)
          }
        },
        {
          name: 'OnWebJSEvent', func: async (res) => {
            // 接收widnow内嵌页面发送的数据
            // { "event_name": "OnWebJSEvent", "result": { "name": "自定义name", "args": "自定义数据" }}
          }
        },
        {
          name: 'MeasureResult', func: async (res) => {
            // 测量工具数据回调
          }
        },
        {
          name: 'OnMoveAlongPathEndEvent', func: (res) => {
            // 覆盖物移动结束信息回调
          }
        },
        {
          name: 'OnCameraMotionStartEvent', func: async (res) => {
            // 相机运动开始信息回调
          }
        },
        {
          name: 'OnCameraMotionEndEvent', func: async (res) => {
            // 相机运动结束信息回调

          }
        },
        {
          name: 'PickPointEvent', func: async function (res) {
            // 取点工具取点数据回调
            console.log('地图标点', res);
            // this.$emit()
          }
        },
        {
          name: 'OnEntitySelectionChanged', func: async function (res) {
            // 实体被选取[框选]、数据回调
          }
        },
        {
          name: 'OnEntityNodeSelectionChanged', func: async function (res) {
            // 模型node选择状态变化数据回调
          }
        },
        {
          name: 'OnEntityReady', func: async function (res) {
            // 3DTilesEntity，WMSEntity，WMTSEntity 加载完成;
            // {success: true, message: '', result: { object: 对象, progress: 100 }}
          }
        },
        {
          name: 'OnCreateGeoLayerEvent', func: async function (res) {
            // 用于GisApi； WMS,WMTS 添加 报错回调
          }
        },
        {
          name: 'OnGeoLayerFeatureClicked', func: async function (res) {
            // 用于GisApi；点击实体回调
          }
        },
        {
          name: 'OnWimFileDownLoadSuccess', func: async function (res) {
            // 洪水演进数据加载成功
            if (res.success) {
              this.floodEId = res.result.EID
              console.log("孪生WDP5-加载洪水演进数据", res);
              that.$modal.msgSuccess('演进数据加载完成')
            } else {
              console.log("孪生WDP5-加载洪水演进数据失败！")
              that.$modal.msgWarning("演进数据加载失败！");
            }
          }
        }
      ]);
      // 场景事件注册/获取/删除
      await window.cloudRender.Renderer.GetRegisterSceneEvents()
    },
    // 创建色带
    async createRibbon() {
      const jsondata = {
        "apiClassName": "CustomColorCardAPIRegister",
        "apiFuncName": "CreateCard",
        "args":
          {
            "name": "MyCard5",
            "customId": "",
            "color":
              [
                [30, 123, 255],
                [240, 26, 27]
              ],
            "anchor":
              [
                {
                  "name": "test1",
                  "position": 0.167,
                  "weight": 0.5,
                  "color": [16, 255, 187]
                },
                {
                  "name": "test2",
                  "position": 0.33,
                  "weight": 0.5,
                  "color": [82, 251, 29]
                },
                {
                  "name": "test3",
                  "position": 0.5,
                  "weight": 0.5,
                  "color": [243, 251, 9]
                },
                {
                  "name": "test4",
                  "position": 0.67,
                  "weight": 0.5,
                  "color": [255, 215, 13]
                },
                {
                  "name": "test5",
                  "position": 0.83,
                  "weight": 0.5,
                  "color": [255, 113, 32]
                }
              ]
          }
      };
      const res = await window.cloudRender.Customize.RunCustomizeApi(jsondata);
      if (res.success) {
        this.colorPointEId = res.result.eid
        console.log("孪生WDP5-创建色带", res.result.eid);
      } else {
        console.log("孪生WDP5-创建色带失败！")
      }
    },
    // 创建材质
    async createMaterial() {
      const jsondata = {
        "apiClassName": "FloodAPI",
        "apiFuncName": "CreateMaterial",
        "args":
          {
            "mId": "testM2",
            "customId": "",
            "matBlur": "0.004",
            "dataMax": "10",
            "dataMin": "0.1",
            "opacity": "0.8",
            "heatMapMatIndex": "0"
          }
      }
      const res = await window.cloudRender.Customize.RunCustomizeApi(jsondata);
      if (res.success) {
        this.materialEId = res.result.eid
        console.log("孪生WDP5-创建材质", res.result.eid);
      } else {
        console.log("孪生WDP5-创建材质失败！")
      }
    },
    // 加载洪水演进数据
    async loadFloodData() {
      let that = this
      if (this.digitalCon.floodParams) {
        this.floodParams = this.digitalCon.floodParams;
        console.log('后端预加载参数', this.floodParams);
        for (let i = 0; i < this.floodParams.length; i++) {
          // if (this.floodParams[i].isPreLoad) {
          //   this.waterFloodFileLoadAndChangeGrid(
          //     this.floodParams[i].preLoadItems[0].floodId,
          //     this.floodParams[i].preLoadItems[0]
          //   );
          //   this.floodParams[i].preLoadItems[0].loaded = true;
          // }
        }
        let flooddata = []
        flood_routing_data('1915586328423682048').then((res => {
          if (res.code === 200) {
            console.log("水深数据:", res)
            for (let i = 0; i < res.data.length; i++) {
              let shdata = res.data[i].data
              let valueArray = []
              let gridIdArray = []
              // 如果数据为空时，应该加入一个值。
              if (shdata.length == 0) {
                gridIdArray.push(22)
                valueArray.push(0)
              }
              for (let j = 0; j < shdata.length; j++) {
                let gridId = shdata[j].split('_')[0]
                gridIdArray.push(gridId)
                let value = shdata[j].split('_')[1]
                valueArray.push(value)
              }
              flooddata.push({
                "valueArray": valueArray,
                "gridIdArray": gridIdArray
              })
            }

          }
          console.log("水深数据", res)
        })).then(async () => {
          const jsondata = {
            "apiClassName": "FloodAPI",
            "apiFuncName": "CreateAlgorithm",
            "args":
              {
                "algorithmType": "FloodForShp",
                "customId": "test01",
                "customData": "",
                "file":
                  {
                    "tifURL": "http://matrix.zhywater.com:18000/prod-api/file/static/DigitalTwin5/DEM51.tif",
                    "shpURL": "http://matrix.zhywater.com:18000/prod-api/file/static/DigitalTwin5/blhwanggge84.shp",
                    "shxURL": "http://matrix.zhywater.com:18000/prod-api/file/static/DigitalTwin5/blhwanggge84.shx",
                    "prjURL": "http://matrix.zhywater.com:18000/prod-api/file/static/DigitalTwin5/blhwanggge84.prj",
                    "dbfURL": "http://matrix.zhywater.com:18000/prod-api/file/static/DigitalTwin5/blhwanggge84.dbf",
                    "override": "true"
                  },
                "data": flooddata
              }
          }
          const res = await window.cloudRender.Customize.RunCustomizeApi(jsondata);
          // if (res.success) {
          //   this.floodEId = res.result.eid
          //   console.log("孪生WDP5-加载洪水演进数据", res);
          // } else {
          //   console.log("孪生WDP5-加载洪水演进数据失败！")
          // }
        })


      }
    },
    // 播放洪水效果
    async playFloodEfficacy() {
      const jsondata = {
        "apiClassName": "FloodAPI",
        "apiFuncName": "RunAlgorithm",
        "args":
          {
            "offset": [0, 0, 0],
            "scale": [1, 1],
            "rotation": 0,
            "materialEId": this.materialEId, //创建材质时返回的eid
            "colorPointEId": '',//this.colorPointEId, //创建色卡时返回的eid
            "index": "1",
            "minIndex": "6",
            "maxIndex": "6",
            "speed": "6",
            "status": "true",
            "reset": "false",
            "eid": this.floodEId //创建算法时返回的eid
          }
      }
      const res = await window.cloudRender.Customize.RunCustomizeApi(jsondata);
      if (res.success) {
        // this.floodEId = res.result.eid
        console.log("孪生WDP5-播放热力演进数据", res);
      } else {
        console.log("孪生WDP5-播放热力演进失败！")
      }
    },
    // 加载热力演进数据
    async loadHeatData(pid) {
      let that = this //返回结果 OnWimFileDownLoadSuccess
      let floodBasicData = {}
      if (this.digitalCon.floodParams) {
        this.floodParams = this.digitalCon.floodParams;
        // console.log('后端预加载参数', this.floodParams);
        for (let i = 0; i < this.floodParams.length; i++) {
          if (this.floodParams[i].PreLoad) {
            floodBasicData = this.floodParams[i].preLoadItems[0].floodBasicData
          }
        }
        let flooddata = []
        this.villageDatas=[]
        flood_routing_data(pid).then((res => {
          if (res.code === 200) {
            // console.log("水深数据:", res)
            for (let i = 0; i < res.data.length; i++) {
              let shdata = res.data[i].data
              let villageData=res.data[i].addition  //获取村庄数据
              this.villageDatas.push(villageData)

              let valueArray = []
              let gridIdArray = []
              // 如果数据为空时，应该加入一个值。
              if (shdata.length == 0) {
                gridIdArray.push(22)
                valueArray.push(0)
              }
              for (let j = 0; j < shdata.length; j++) {
                let gridId = shdata[j].split('_')[0]
                gridIdArray.push(gridId)
                let value = shdata[j].split('_')[1]
                valueArray.push(value)
              }
              flooddata.push({
                "valueArray": valueArray,
                "gridIdArray": gridIdArray
              })
            }

          }
          // console.log("水深数据", res)
        })).then(async () => {
          const jsondata = {
            "apiClassName": "FloodAPI",
            "apiFuncName": "CreateAlgorithm",
            "args":
              {
                "algorithmType": "Heatmap",
                "customId": "test02",
                "customData": "",
                "file":
                  {
                    "tifURL": floodBasicData.tifURL,
                    "shpURL": floodBasicData.shpURL,
                    "shxURL": floodBasicData.shxURL,
                    "prjURL": floodBasicData.prjURL,
                    "dbfURL": floodBasicData.dbfURL,
                    "override": "true"
                  },
                "data": flooddata
              }
          }
          const res = await window.cloudRender.Customize.RunCustomizeApi(jsondata);
          if (res.success) {
            this.floodEId = res.result.eid
            // this.$modal.msgSuccess('洪水演进加载完成')
            console.log("villageDatas",this.villageDatas)
            console.log("孪生WDP5-加载热力演进数据", res);
            that.$modal.closeLoading()
          } else {
            console.log("孪生WDP5-加载热力演进数据失败！")
            that.$modal.closeLoading()
          }
        })


      }
    },
    /**
     * 播放热力效果
     * @param time
     * @returns {Promise<void>}
     */
    async playHeatEfficacy(time = 20) {
      const jsondata = {
        "apiClassName": "FloodAPI",
        "apiFuncName": "RunAlgorithm",
        "args":
          {
            "offset": [0, 0, 0],
            "scale": [1, 1],
            "rotation": 0,
            "materialEId": this.materialEId, //创建材质时返回的eid
            "colorPointEId": this.colorPointEId,//, //创建色卡时返回的eid
            "index": "1",
            "minIndex": time || '',
            "maxIndex": time || '',
            "speed": "2",
            "status": "true",
            "reset": "false",
            "eid": this.floodEId //创建算法时返回的eid
          }
      }
      const res = await window.cloudRender.Customize.RunCustomizeApi(jsondata);
      if (res.success) {
        // this.floodEId = res.result.eid
        console.log("孪生WDP5-播放热力效果", res);
      } else {
        console.log("孪生WDP5-播放热力效果失败！")
      }
      console.log("村庄信息",this.villageDatas[time].protectObjects)
      this.villageDatas[time].protectObjects.forEach((protectObject) => {
        console.log("保护村庄111",protectObject,protectObject.geometry)
      })
    },

    /**
     * 显示隐藏洪水演进-热力效果
     * @param show
     * @returns {Promise<void>}
     */
    async showHeatEfficacy(show) {
      let jsondata = {
        "apiClassName": "FloodAPI",
        "apiFuncName": "ShowAlgorithm",
        "args":
          {
            "guid": "",
            "isVisible": show,
            "eid": this.floodEId,
          }
      }
      const res = await window.cloudRender.Customize.RunCustomizeApi(jsondata);
      if (res.success) {
        console.log("孪生WDP5-显隐热力效果", res);
      } else {
        console.log("孪生WDP5-显隐热力效果失败！")
      }

    },
    // 绘制村庄
    async drawvillages(index) {
      console.log('villageDatas', this.villageDatas)
      let protectObjects=this.villageDatas[index].protectObjects
      console.log('protectObjects', protectObjects)
      for (const village of protectObjects) {
        console.log('village', village)
        const entityObj = new window.cloudRender.Range({
        "polygon2D": {
          "coordinates": JSON.parse(village.geometry).coordinates[0]
        },
        "rangeStyle": {
          "type": "box_wave", //类型
          "fillAreaType": "solid", //底部区域填充类型
          "height": 30, //围栏高度(单位:米)
          "strokeWeight": 20, //底部轮廓线宽度(单位:米; 注: 区域中含有内环"innerLoops"时无效)
          "color": "FF0000" //HEXA或rgba(0,0,0,0.8)
        },
        "entityName": "myName",
        "customId": "myId1",
        "customData": {
          "data": "myCustomData"
        }
      })

      // 向场景中添加实体
      const res = await window.cloudRender.Scene.Add(entityObj, {
        calculateCoordZ: {
          coordZRef: "surface", //surface:表面;ground:地面;altitude:海拔
          coordZOffset: 5 //高度(单位:米)
        }
      });
      }
    },
    //绘制淹没村庄poi点

    //绘制村庄转移路线
    async drawvillageszZyline() {
      const path = new window.cloudRender.Path({
        "polyline": {
          "coordinates": [
            [114.764149, 28.898838],
            [114.76414, 28.89889],
            [114.76713, 28.89896],
            [114.768848, 28.894648]
          ]
        },
        "pathStyle": {
          "type": "brimless_arrow",
          "width": 100,
          "color": "FF0000", //HEXA或rgba(0,0,0,0.8)
          "passColor": "ffb3deff"
        },
        "bVisible": true,
        "entityName": "myName",
        "customId": "myId1",
        "customData": {
          "data": "myCustomData"
        }
      });

      const res = await window.cloudRender.Scene.Add(path, {
        calculateCoordZ: {
          coordZRef: "surface", //surface:表面;ground:地面;altitude:海拔
          coordZOffset: 5 //高度(单位:米)
        }
      });
    },

    // 获取当前视角
    getCameraInfo() {
      let cameraInfo = window.cloudRender.CameraControl.GetCameraInfo()
      console.log("获取当前视角:", cameraInfo)
    },
    // 获取相机Limit值
    async GetCameraLimit() {
      const res = await window.cloudRender.CameraControl.GetCameraLimit();
      console.log("获取相机Limit值:", res)
    },
    // 设置相机固定Limit值
    async SetCameraLimit() {
      const jsondata = {
        "locationLimit": [],
        "pitchLimit": [-80, 0], //俯仰角; 取值范围[-90~0]
        "yawLimit": [-100, 100], //偏航角; 取值范围[-180~180]
        "viewDistanceLimit": [1, 200000] //相机距离实体距离范围
      }

      const res = await window.cloudRender.CameraControl.SetCameraLockLimit(jsondata);
      console.log("设置相机固定Limit值", res);
    },

    //镜头跳转
    async setCameraPose(type) {
      const res = await getViewList(window.projectCode)
      if (res?.data?.dtOptions) {
        const dtOptions = res.data.dtOptions;
        const dtOption = dtOptions[type]
        await window.cloudRender.CameraControl.SetCameraPose(dtOption.view)
      }
    },
    //配置化--链接渲染
    GetUrlStartRenderCloud(cloudurl, orderID) {
    },
    //配置化--刷新渲染
    refreshCloudRender(state) {
    },
    //配置化--设置场景视角
    setDefaultView(jsondata) {
    },
    //配置化--根据配置的各个视角的code值进行场景视角设置
    resetDtViewer(code) {
    },
    //获取场景相机参数
    getCameraParams() {
    },
    // 配置化-初始化poi数据(复用地图展点接口)
    async initMapPointByUrl() {
      const res = await getDigitalPoiUrlList({
        status: 0,
        projectCode: this.pi?.data?.projectCode,
        pageSize: 999,
        pageNum: 1
      })
      if (res.rows.length > 0) {
        const data = res.rows;
        for (let i = 0; i < data.length; i++) {
          const poiList = await getMapScatterInfo(data[i].httpAddress)
          if (poiList && poiList.data.length > 0) {
            for (let j = 0; j < poiList.data.length; j++) {
              const poiInfo = poiList.data[j];
              console.log(data[i], 'data[i]');
              this.addDigitalPoi(
                data[i],
                // "http://wdpapi.51aes.com/doc-static/images/static/markerNormal.png",
                // "http://wdpapi.51aes.com/doc-static/images/static/markerNormal.png",
                // "http://wdpapi.51aes.com/doc-static/images/static/LabelBg.png",
                poiInfo,
                "surface"
              )
            }
          }
        }
      }
    },
    async addDigitalPoi(poiStyle, poiInfo, coordZRef) {
      const {
        markerNormalUrl,
        markerActivateUrl,
        labelBgImageUrl,
        markerSize,
      } = poiStyle;
      let jsonData = new window.cloudRender.Poi({
        location: [poiInfo.lon, poiInfo.lat],
        poiStyle: {
          markerVisible: true,
          markerNormalUrl: markerNormalUrl,
          // "http://wdpapi.51aes.com/doc-static/images/static/markerNormal.png", //monitorsPoint.markerNormalUrl,
          markerActivateUrl: markerActivateUrl,
          // "http://wdpapi.51aes.com/doc-static/images/static/markerNormal.png", // monitorsPoint.markerActivateUrl,
          markerSize: [50, 50],
          labelBgImageUrl: labelBgImageUrl, //"http://wdpapi.51aes.com/doc-static/images/static/LabelBg.png", //monitorsPoint.labelBgImageUrl,
          labelBgSize: [poiInfo.label.length * 15 + 10, 30],
          labelBgOffset: [25, 50], // label可以向上下左右偏移；当[0,0]时，label切图的左上角对齐location (x,y 单位:像素)
          labelContent: [poiInfo.label, "E8E8E8", "12"],
          scrollSpeed: 0, // 文本滚动速度(0:不滚动)
          textBoxWidth: poiInfo.label.length * 15 + 10, // 文本框宽度(默认100)
          labelContentOffset: [5, 5], // labeContent可以向上下左右偏移; 当[0,0]时，labelContent的左上角对齐label的左上角 (x,y 单位:像素)
          labelTop: true, //label是否置于marker顶层
          labelContentJustification: "Left", //文本内容对齐方式: Left, Center, Right
          labelContentAutoWrap: true, //label内容是否自动换行
          scrollPolicy: "default", //文本长度超过文本框时滚动; always: 总是滚动
        },
        entityName: poiInfo.type,
        customId: poiInfo.id,
        customData: {
          data: JSON.stringify(poiInfo),
        },
        bVisible: false,
        visible2D: {
          camera: {
            hideDistance: 5000, //定义实体隐藏的距离(单位:米),相机超过此距离时,实体会被隐藏
            hideType: "none", //实体超出显示距离(none:不显示; default:圆圈显示)
            scaleMode: "2D", //是否受相机的透视影响(2D:不影响; 3D:影响)；透视包括近大远小、overlapOrder生效
          },
          interaction: {
            //被"交互"影响的可见性(POI有效)
            clickTop: true, //当发生点击(优先级高于滑过)时,需要显示在最上层
            hoverTop: true, //当发生滑过时，需要显示在最上层
          },
          entity: {
            overlapOrder: 1, // 跨2D覆盖物类型的层级设置，当scaleMode为2D时生效; 数值越大越在最上层；范围[1~10]
          },
        },
      });
      this.poiList.push(jsonData);
      const res = await window.cloudRender.Scene.Add(jsonData, {
        calculateCoordZ: {
          coordZRef: "surface", //surface:表面;ground:地面;altitude:海拔
          coordZOffset: 0, //高度(单位:米)；该设置会覆盖location中的z值，若想用location中的值，请删除此行
        },
      });
    },
    //添加管理/保护范围线
    async rangePolyline(type, encd) {
      let file = await getHjqqData({
        encd: encd
      })
      let res = file.data
      const typeUrl = {
        1: `${res.controlLineUrl}`,
        2: `${res.protectionLineUrl}`,
        3: `${res.projectContolLineUrl}`,
        4: `${res.projectProtectionLineUrl}`
      }
      this.getPolygon(type, typeUrl[type])
    },
    async getPolygon(type, jsonData) {
      console.log('管理保护范围线', type, jsonData)
      let colorType = {
        '1': '#ff5926',
        '2': '#ffff6b'
      }
      const fileName = jsonData.split('/')[jsonData.split('/').length - 1].split('_')[0]
      const fileData = process.env.VUE_APP_BASE_API + '/file/static' + jsonData
      const res = await fetch(fileData)
      const data = await res.json()
      const features = data.features;
      const pathStyle = {
        type: "fit_solid",
        width: 5,
        color: colorType[type],
      }
      for (let i = 0; i < features.length; i++) {
        const coordinates = features[i].geometry.coordinates
        if (features[i].geometry.type === 'MultiLineString') {
          for (let j = 0; j < coordinates.length; j++) {
            await this.addCustomPath(coordinates[j], pathStyle, false, fileName, fileName + `_${j}`, "altitude")
          }
        } else if (features[i].geometry.type === 'LineString') {
          await this.addCustomPath(coordinates, pathStyle, false, fileName, fileName + `_${i}`, "altitude")
        }

      }
    },
    //添加路径
    async addCustomPath(coordinates, pathStyle, boolShow, pathName, pathId, coordZRef) {
      const path = new window.cloudRender.Path({
        "polyline": {
          "coordinates": coordinates
        },
        "pathStyle": pathStyle,
        "bVisible": boolShow,
        "entityName": pathName,
        "customId": pathId,
      });

      const res = await window.cloudRender.Scene.Add(path, {
        calculateCoordZ: {
          coordZRef: coordZRef, //surface:表面;ground:地面;altitude:海拔
          // coordZOffset: 50 //高度(单位:米)
        }
      });
      this.poiList.push(path)
      console.log(res, 'res polyline');
    },
    //添加3d文字
    async addText3D(poiArr) {
      for (let i = 0; i < poiArr.length; i++) {
        const poi = poiArr[i]
        const text3d = new window.cloudRender.Text3D({
          "location": [poi.lon, poi.lat, poi.height],
          "rotator": {
            "pitch": poi.pitch, //俯仰角, 参考(-180~180)
            "yaw": poi.yaw, //偏航角, 参考(-180~180)
            "roll": poi.roll //翻滚角, 参考(-180~180)
          },
          "scale3d": [60, 15, 15],
          "text3DStyle": {
            "text": poi.text,
            "color": poi.color, //HEX或rgba(0,0,0)
            "type": "plain", //样式(plain; reflection; metal)
            "outline": 0.4, //轮廓(单位:百分比), 取值范围[0~1]
            "portrait": false, //纵向(true/false)
            "space": 0.1 //间距(单位:米)
          },
          "bVisible": true,
          "entityName": "myName",
          "customId": poi.id,
          "customData": {
            "data": "myCustomData"
          }
        });

        const res = await window.cloudRender.Scene.Add(text3d, {
          calculateCoordZ: {
            coordZRef: "surface", //surface:表面;ground:地面;altitude:海拔
            coordZOffset: poi.height //高度(单位:米)
          }
        });
      }
    },
    //工具集--镜头自动旋转
    cameraRotate() {
    },
    // 工具集--指南针显示隐藏
    showHideCompass(show) {
    },
    // 工具集--获取屏幕内覆盖物ID
    getFullSceenCoveringId(covering_type) {
    },
    // 工具集--修改场景天气
    switchWeather(weather) {
    },
    // 工具集--修改场景时间
    switchDate(date) {
    },
    // 工具集--限制场景镜头视界
    setCameraSpace() {
    },
    // 工具集--解除场景镜头视界限制
    resetCameraSpace(state) {
    },
    // 工具集--开启地图标点  coordType: 坐标类型(0:经纬度坐标, 1:cad坐标)
    startGetCoord(coordType, cadMapKey, coordZType, coordinateShow, iconShow) {
    },
    // 工具集--结束地图标点
    endGetCoord() {
    },
    // 工具集--设置场景渲染质量 low medium high epic
    setRenderQuality(quality) {
    },
    // 工具集--设置镜头绕场景中心旋转 time 相机绕一周时间(秒) direction clockwise顺时针  anticlockwise:逆时针  stop:停止旋转
    setCameraRotate(time, direction) {
    },
    // 工具集--显示隐藏场景帧率
    showHideUEFrameRate() {
    },
    // 工具集--注册键盘事件
    async startKeyboard() {
      // 键盘事件是否开启（默认关闭）
      await window.cloudRender.System.SetDefaultKeyboard(true); // true, false
    },
    // 工具集--删除键盘事件
    removeKeyboard() {
    },
    //工具集--镜头移动
    moveCamera() {
    },
    //工具集--场景漫游
    sceneRoam() {
    },
    // 工具集--镜头漫游
    async startCameraRoam() {
      const entityObj = new window.cloudRender.CameraRoam({
        frames: [
          {
            location: [114.77267240830309, 28.918864184925752, 452],
            rotation: {
              pitch: -32.566371917724609, //俯仰角, 参考(-90~0)
              yaw: 123.93430328369141, //偏航角, 参考(-180~180; 0:东; 90:南; -90:北)
            },
            time: 10, //镜头到下一帧的时间(单位:秒)
          },
          {
            location: [114.76818423116066,28.91389190614651,452.02434642025003],
            rotation: {
              pitch: -32.566371917724609,
              yaw: 123.93430328369141,
            },
            time: 7,
          },
          {
            location: [114.7626378957771,28.910771351934073,457.01588227935372],
            rotation: {
              pitch: -33.468120574951172,
              yaw: 160.19627380371094,
            },
            time: 15,
          },
          {
            location: [114.76298188065611,28.905581150247269,445.80867803904329],
            rotation: {
              pitch: -31.484096527099609,
              yaw: 38.065155029296875,
            },
            time: 7,
          },
          {
            location: [114.76554750554206,28.902414121485172,478.17260656254848],
            rotation: {
              pitch: -37.436332702636719,
              yaw: 102.28757476806641,
            },
            time: 9,
          },
          {
            location: [114.7608649254371,28.900106368719651,501.8436168066853],
            rotation: {
              pitch: -42.126617431640625,
              yaw: 173.1849365234375,
            },
            time: 9,
          },
          {
            location: [114.7527369623823,28.902412239964516,520.28269584865416],
            rotation: {
              pitch: -45.914920806884766,
              yaw: -139.55000305175781,
            },
            time: 15,
          },
          {
            location: [114.74006694850368,28.899468765461574,533.35763539486481],
            rotation: {
              // 最后一帧 镜头停止后的姿态
              pitch: -43.569843292236328,
              yaw: 147.74858093261719,
            }
          }
        ],
      });
      const res = await window.cloudRender.Scene.Add(entityObj);

      // 开启相机漫游
      const args = {
        progressRatio: 0, //镜头位置切换到整体漫游比例,范围[0,1]
        speedRatio: 1, //相机漫游移动倍率
        bReverse: false, //是否反向
      };

      await window.cloudRender.CameraControl.PlayRoam(entityObj, args);
    },
    //继续漫游
    async continueCameraRoam() {
      const res = await window.cloudRender.CameraControl.PlayRoam();
      console.log(res);
    },
    //暂停漫游
    async pauseCameraRoam() {
      const res = await window.cloudRender.CameraControl.PauseRoam({
        bEnableRotatingOnPause: false,
        bEnableZoomingOnPause: false
      });
      console.log(res);
    },
    //结束漫游
    async stopCameraRoam() {
      const res = await window.cloudRender.CameraControl.StopRoam();
      console.log(res);
    },
    // 工具集--修改镜头漫游状态
    setCameraRoamState(state) {
    },
    // 工具集--显示隐藏POI点
    showHidePOI(id, type, show) {
    },
    //工具集--关闭渲染
    stopRenderCloud() {
    },
    //工具集--获取天气信息
    getWeatherInfo(code) {
    },
    //工具集--获取AES实体EID
    getAESObjects() {
    },
    // 工具集--测量工具
    measureTool() {
    },
    // 工具集--测量角度工具
    measureAngle() {
    },
    // 工具集--测量长度工具
    measureLength() {
    },
    // 工具集--测量面积工具
    measureArea() {
    },
    // 工具集--开启测量工具
    openMeasureTool() {
    },
    /**
     *     隐藏水库水面
     * @param isshow
     * @returns {Promise<void>}
     */
    async showSKwater(isshow) {
      let skWaterEID = digitalTwinInfo.skWaterEID
      const res = await window.cloudRender.Scene.GetByEids([skWaterEID]);
      const res2 = await res.result[0].SetbVisible(isshow);
      // console.log("隐藏水库水面",isshow,res2)
    },
    getSurfaceWater(){
      let option={
        "surfaceWaterEID":digitalTwinInfo.surfaceWaterEID,
      }
      digitalTwinApi.getSurfaceWater(option)
    },
    //水面上升下降调取的方法
    waterGoup(h) {
      console.log("调整水位到",h)
      let option={
        "surfaceWaterEID":digitalTwinInfo.surfaceWaterEID,
        "targetWaterHeight":h-15.59    // 根据不同水库调整不同的误差值
      }
      digitalTwinApi.getWaterFaceInfoAndMove(option)
    },

    /**
     * 水库出库流量--控制闸门开度+水花
     * @param outflow
     */
    reservoirOutflow(outflow) {
        if (outflow>0) {
          digitalTwinApi.openFloodgates(1)
          digitalTwinApi.showSpray(true)
        }else {
          digitalTwinApi.openFloodgates(-0.5)
          digitalTwinApi.showSpray(false)
        }
    },
    /**
     * 闸门开度（0-1）
     * @param openrange
     */
    openFloodgates(openrange) {
      digitalTwinApi.openFloodgates(openrange)
    },
    /**
     *
     * @param isShow
     */
    //水花
    showSpray(isShow) {
      digitalTwinApi.showSpray(isShow)
    },
    // 工具集--geojson区域轮廓
    geoRange(val) {
    },
    //工具集--模型剖切
    modalClipe() {
    },
    //工具集--按钮禁用事件
    buttongDisabled(id) {
    },
    //工具集--场景镜头移动状态(停止移动, 释放焦点, 删除路径)
    cameraMoveState() {
    },
    //加载数字流场
    addDigitalFlood() {
    },
    //移除流场
    removeDigitalFlood() {
    },
    //工具集--添加路径热力图
    addRoadHeat() {
    },
    //工具集--更新路径热力图
    updateRoadHeat() {
    },
    // 淹没演进工具集  1.配置文件下载
    waterFloodFileLoadAndChangeGrid(name, option) {
      let that = this;
      let jsondata = {
        GridID: name, //网格id，自主命名
        TifURL: option.floodBasicData.tifURL + ".tif", //本地或在线地址
        ShpURL: option.floodBasicData.shpURL + ".shp", //本地或在线地址
        ShxURL: option.floodBasicData.shxURL + ".shx", //本地或在线地址
        PrjURL: option.floodBasicData.prjURL + ".prj", //本地或在线地址
        DbfURL: option.floodBasicData.dbfURL + ".dbf", //本地或在线地址
        isLocalPath: option.isLocalfloodBasicData, //是否本地路径，true为本地，false为非本地，上述地址两种方式只取一种
      };
      console.log("文件下载", jsondata);
    },
    // 淹没演进工具集  2.淹没格网转置
    waterFloodChangeGrid(data, name) {
    },
    changeLoading() {
      this.homeLoading = false;
    },
    // 淹没演进工具集 3.网格数据加载 走接口
    async waterFloodDataUpload() {
    },
    // 淹没演进工具集 4.淹没效果设置
    waterFloodEffectSet(option, name) {
    },
    // 淹没演进工具集 5.网格偏移设置
    waterFloodGridChangeSet(name) {
    },
    // 淹没演进工具集 一帧一帧跑
    waterFloodPlayOne(control, data, name) {
    },
    // 淹没演进工具集 6.淹没过程顺序播放
    waterFloodPlay(name) {
    },
    // 淹没演进工具集 7.指定帧率播放
    waterFloodChoosePlayFrame(PlayIndex, name) {
    },
    // 淹没演进工具集 8.淹没效果清除
    waterFloodClear(name) {
    },
    // 淹没演进工具集 9.删除转置网格
    waterFloodDelateGrid(name) {
    },
    //工具集--迁移箭头
    addMigrationArrows(date1, date2) {
    },
    //工具集--修改迁徙箭头
    updateMigrationArrows() {
    },
    //工具集--删除迁徙图
    deleteMigrationArrows() {
    },
    //工具集--水面变化
    waterChange(height, time) {
    },
    //淹没演进工具集 添加淹没村落点
    addVillage(list) {
    },
    //淹没演进工具集 添加淹没村落点poi方法
    addVillagePoi(list) {
    },
    //淹没演进工具集 移除所有淹没村点
    removeAllVillage() {
    },
    //淹没演进工具集 添加淹没村落点label
    addVillageLabel(data) {
    },
    //淹没演进工具集 添加淹没村落点label的方法
    addVillageLabelPoi(data) {
    },
    //淹没演进工具集 移除所有淹没村点label
    removeAllVillageLabel() {
    },
    //淹没演进工具集 添加淹没村落迁移轨迹
    addMovePath(id, list) {
    },
    //淹没演进工具集 添加淹没村落迁移轨迹方法
    addMovePathPoi(id, list) {
    },
    //淹没演进工具集 移除淹没村落迁移轨迹
    deleteAllMovePath() {
    },
    //淹没演进工具集 添加淹没村落的转移目的地点
    addVillageEnd(data) {
    },
    //淹没演进工具集 添加淹没村落的转移目的地点方法
    addVillageEndPoi(list) {
    },
    //淹没演进工具集 移除淹没村落的转移目的地点
    removeAllEndVillage() {
    },
    //闸门水花工具集--水流开关
    waterFlowSwitch(gear) {
    },
    //闸门水花工具集--关闭其他水花
    closeOtherWaterFlowSwitch(opennum) {
    },
    //闸门水花工具集--关闭水流开关
    closeWaterFlowSwitch() {
    },
    //闸门水花工具集--闸门开关
    gateSwitch(gateArray, angleArray) {
    },
    //闸门水花工具集--闸门开启/关闭并放水/停水
    gateSwitchAndWaterFlowSwitch(boolOpen, flowArray) {
    },
    //添加汛限水位线
    addWaterPolyline() {
    },
    //各类视角--待合并
    multiplePerspectives(num) {
    },
    //工具集--删除所有poi点
    deleteAllPoint() {
    },
  },
  mounted() {
    this.timer = setInterval(this.changePercentage, 500);
  },
  destroyed() {
  },
  beforeDestroy() {
  },
};
</script>

<style lang="less" scoped>
@text-color: #00ffff;
@time: 2.5s;

.ue4map {
  width: 100%;
  height: 100%;
  position: absolute;

  // transform-origin: 0 0;
  .bj {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background-image: url("~@/assets/images/digitalTwin/beijing.png") !important;
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
  }

  #player {
    width: 100% !important;
    height: 100% !important;
    bottom: 0 !important;
    left: 0 !important;

    video {
      height: 100%;
      width: 100%;
    }

    .close {
      position: absolute;
      right: 4vh;
      top: 7vh;
      z-index: 12;
      color: #fff;
      cursor: pointer;
      background-color: rgba(0, 0, 0, 0.5) !important;
      border-color: #aad4c1 !important;
    }

    /deep/ .el-button + .el-button {
      margin: 0;
    }

    .close:hover {
      opacity: 0.7;
    }

    > img {
      position: absolute;
      top: 6vw;
      right: 1vw;
      z-index: 11;
      width: 20.6vw;
    }

    //.byq{
    //    width: 25.6vw;
    //    //height: 36.4vh;
    //}
    //.zhdq{
    //    width: 25.6vw;
    //    //height: 36.4vh;
    //}
    .left {
      left: 1vw;
      top: 6vw;
      position: absolute;
      width: 20.6vw;
      height: 80vh;
      display: flex;
      flex-direction: column;

      img {
        width: 100%;
        margin-bottom: 2vh;
      }
    }

    .right {
      right: 1vw;
      top: 6vw;
      position: absolute;
      width: 20.6vw;
      height: 80vh;
      display: flex;
      flex-direction: column;

      img {
        width: 100%;
        margin-bottom: 2vh;
      }
    }
  }

  .shade {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background: #000000;
    //z-index: 1;
  }

  .move {
    animation: mymove 5s linear infinite;
    -moz-animation: mymove 5s linear infinite;
    -o-animation: mymove 5s linear infinite;
    -webkit-animation: mymove 5s linear infinite;
  }

  @keyframes mymove {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  .loadingText {
    font-size: x-large;
    margin: 10% 0 10% 30%;
    font-weight: normal;
    font-stretch: normal;
    color: @text-color;
  }
}

::v-deep .el-progress__text {
  color: @text-color;
}

.loading {
  width: 260px;
  height: 11px;
  margin: 0 auto;
  text-align: center;
  background-color: #000000;
  border: solid 1px #46fcd5;
  transform: skew(315deg);
  display: flex;

  span {
    display: inline-block;
    width: 10px;
    height: 100%;
    margin-right: 5px;
    opacity: 0;
    background: #00ffff;
    animation: load 7s ease infinite;

    &:last-child {
      margin-right: 0;
    }

    &:nth-child(1) {
      -webkit-animation-delay: @time;
    }

    &:nth-child(2) {
      -webkit-animation-delay: @time * 2;
    }

    &:nth-child(3) {
      -webkit-animation-delay: @time * 3;
    }

    &:nth-child(4) {
      -webkit-animation-delay: @time * 4;
    }

    &:nth-child(5) {
      -webkit-animation-delay: @time * 5;
    }

    &:nth-child(6) {
      -webkit-animation-delay: @time * 6;
    }

    &:nth-child(7) {
      -webkit-animation-delay: @time * 7;
    }

    &:nth-child(8) {
      -webkit-animation-delay: @time * 8;
    }

    &:nth-child(9) {
      -webkit-animation-delay: @time * 9;
    }

    &:nth-child(10) {
      -webkit-animation-delay: @time * 10;
    }

    &:nth-child(11) {
      -webkit-animation-delay: @time * 11;
    }

    &:nth-child(12) {
      -webkit-animation-delay: @time * 12;
    }

    &:nth-child(13) {
      -webkit-animation-delay: @time * 13;
    }

    &:nth-child(14) {
      -webkit-animation-delay: @time * 14;
    }

    &:nth-child(15) {
      -webkit-animation-delay: @time * 15;
    }
  }
}

@-webkit-keyframes load {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 1;
  }
}

::v-deep .el-collapse-item__header {
  background: #4d6565;
  opacity: 0.8;
  color: aliceblue;
  padding-left: 5px;
}

::v-deep .el-collapse-item__wrap {
  background: #4d6565;
  opacity: 0.8;
}

::v-deep .el-collapse-item__content {
  padding: 5px 5px 5px 0;
  color: #fff;
}

::v-deep .el-button--medium:first-child {
  margin-left: 10px;
}

#selectStyle {
  width: 150px !important;
}

/deep/ .el-input--small {
  width: 150px !important;
}

/deep/ .el-switch__label * {
  font-size: 16px;
}

/deep/ .el-switch {
  height: 35px;
}

.block {
  display: flex !important;
  justify-content: space-between;
  margin: 0;
  padding: 0;
  align-items: center;
  // width: 210px;
}

/deep/ .popper__arrow {
  left: 30%;
}

/deep/ .el-input__inner {
  height: 42px;
  color: #00ffff;
  font-size: 18px;
}

.ue4map .el-loading-spinner .el-loading-text {
  color: #409eff;
  margin: 3px 0;
  font-size: 20px;
}

.ue4map .el-loading-spinner i {
  font-size: 20px;
}

.funcBtnBox {
  width: 300px;
  max-height: 800px;
  overflow-x: auto;
  overflow-y: auto;
  position: fixed;
  top: 85px;
  right: 35px;
  background: #4d6565;
  border-radius: 5px;
  opacity: 0.8;
  padding: 5px;
  z-index: 10;
}

.dt-title {
  background: url(~@/assets/images/titleBG.png) no-repeat;
  background-size: 100% 100%;
  width: 175px;
  height: 30px;
  z-index: 3;
  position: absolute;
}

.dt-title > div {
  font-size: 20px;
  font-weight: 600;
  background-image: -webkit-linear-gradient(top,
  var(--gradientFrColor),
  var(--gradientToColor));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
</style>
