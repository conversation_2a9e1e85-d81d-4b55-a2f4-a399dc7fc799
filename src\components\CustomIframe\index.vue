<template>
  <div v-loading="loading" :style="'height: ' + height + 'px;'">
    <iframe
      ref="iframe"
      title="iframe"
      :src="src"
      style="width: 100%; height: 100%; border: 0;"
    />
  </div>
</template>

<script>
export default {
  name: "CustomIframe",
  props: {
    src: {
      type: String,
      required: true
    },
  },
  data() {
    return {
      height: 300,
      loading: true,
      url: this.src
    };
  },
  mounted: function () {
    const iframe = this.$refs.iframe;
    iframe.onload = () => {
      this.loading = false;
    }
    iframe.onerror = () => {
      this.loading = false;
    }

    this.resetHeight();

    window.onresize = () => {
      this.resetHeight();
    };
  },
  methods: {
    resetHeight() {
      const offsetTop = this.$refs.iframe.getBoundingClientRect().top + window.scrollY;
      this.height = window.innerHeight - offsetTop - 25;
      console.log(window.innerHeight, offsetTop, this.height)
    }
  }
};
</script>
