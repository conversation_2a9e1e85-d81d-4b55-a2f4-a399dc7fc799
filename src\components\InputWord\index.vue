<!--
@File    index.vue
@Desc    自定义单词输入组件.
<AUTHOR> href="mailto:<EMAIL>">xiaoQQya</a>
@Date    2023/10/10
-->
<template>
  <el-input
    v-if="maxlength"
    v-bind:value="value"
    @input="input"
    :type="type"
    :rows="rows"
    :placeholder="placeholder"
    :maxlength="maxlength"
    show-word-limit
    :disabled="disabled"
    :clearable="clearable"
    @blur="e => blur(e.target.value)"
    @change="change"
  />
  <el-input
    v-else
    v-bind:value="value"
    @input="input"
    :type="type"
    :rows="rows"
    :placeholder="placeholder"
    :disabled="disabled"
    :clearable="clearable"
    @blur="e => blur(e.target.value)"
    @change="change"
  />
</template>

<script>
import {strTrim} from "@/utils/zhy";

export default {
  name: "InputWord",
  props: {
    value: {
      type: String,
      default: undefined
    },
    placeholder: {
      type: String,
      default: null
    },
    maxlength: {
      type: Number,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    },
    type: {
      default: "text"
    },
    rows: {
      type: Number,
      default: null
    },
    clearable: {
      type: <PERSON><PERSON>an,
      default: false
    }
  },
  methods: {
    input(value) {
      this.$emit("input", value);
    },
    blur(value) {
      this.$emit("input", strTrim(value));
    },
    change(value) {
      this.$emit("change", value);
    }
  }
}
</script>

<style scoped>
::v-deep .el-input__inner {
  padding: 0 55px 0 15px;
}

::v-deep .el-textarea__inner {
  padding: 5px 55px 5px 15px;
}

::v-deep .el-input__count {
  background-color: transparent;
}
</style>
