<template>
  <div id="largeScreen">
    <el-dialog
      :visible.sync="visible"
      :width="width"
      :top="top"
      ref="elDialog"
      :fullscreen="fullFlag"
      :append-to-body="true"
      :close-on-click-modal="false"

      :before-close="closeBtn"
      :class="{
        isfull: !showdialogFull,
        screenPopupYouche: isShow,
        screenPopup: !isShow,
      }"
    >
      <!-- <div class="plate_info_angle_1"></div>
        <div class="plate_info_angle_2"></div>
        <div class="plate_info_angle_3"></div>
        <div class="plate_info_angle_4"></div> -->

      <template slot="title">
        <div style="display: inline-flex">
          <div
            v-if="showdialogFull"
            style="position: absolute; right: 53px; top: 15px; cursor: pointer"
            @click.prevent="expand"
          >
            <img
              src="@/assets/images/screen/home/<USER>"
              alt=""
              style="display: block; width: 24px; height: 19px"
            />
          </div>
          <div class="title-sign">
            <img
              src="@/assets/images/screen/screenDialog/Hengshanlogheader.png.png"
              alt=""
            />
          </div>
          <div class="avue-crud__dialog__header">
            <span class="el-dialog__title">
              {{ title }}
            </span>
          </div>
        </div>
      </template>
      <div :class="fullFlag ? 'fullBody' : 'noFullBody'">
        <slot></slot>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: "commonDialogBox",
  props: {
    title: {
      type: String,
      default: "标题",
    },
    height: {
      type: String,
      default: "600px",
    },
    width: {
      type: String,
      default: "55%",
    },
    top: {
      type: String,
      default: "00",
    },
    showdialogFull: {
      type: Boolean,
      default: true,
    },
    dialogFull: {
      type: Boolean,
      default: false,
    },
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      fullFlag: this.dialogFull,
      isShow: false,
      widthFirst: null,
      isExpand: true,
    };
  },
  watch: {
    dialogFull: {
      handler(val) {
        this.fullFlag = val;
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    this.widthFirst = this.width;
    this.$nextTick(() => {
      this.$refs.elDialog.$el.firstChild.style.height = this.height;
      this.$refs.elDialog.$el.firstChild.style.transition =
        "width .2s linear, height .2s linear";
      // this.$refs.elDialog.$el.firstChild.style.minHeight = '588px';
      // this.$refs.elDialog.$el.firstChild.style.height = '588px';
      this.width = this.widthFirst;
    });
  },

  created() {
    this.$nextTick(() => {
      this.getPath();
      let windowHref = window.location.href;
      // 获取项目id
      if (windowHref.includes("screen/hengshan")) {
        this.isShow = false;
      } else if (windowHref.includes("screen/longzhu")) {
        this.isShow = false;
      } else if (windowHref.includes("screen/youche")) {
        this.isShow = true;
      } else if (windowHref.includes("screen/index")) {
        this.isShow = false;
      } else if (windowHref.includes("screen/polling")) {
        this.isShow = true;
      }
    });
  },
  methods: {
    getPath() {
      let windowHref = window.location.href;
      if (windowHref.includes("screen/youche")) {
        var screenPopup = document.querySelector(".screenPopup");
        var elDialogHeader = screenPopup.querySelector(".el-dialog__header");
        elDialogHeader.classList.add("newClassName");
      } else {
        var screenPopup = document.querySelector(".screenPopup");
        var elDialogHeader = screenPopup.querySelector(".el-dialog__header");
        if (elDialogHeader.classList.contains("newClassName")) {
          elDialogHeader.classList.remove("newClassName");
        }
      }
    },
    expand() {
      if (this.isExpand) {
        this.$nextTick(() => {
          this.$refs.elDialog.$el.firstChild.style.height = "78%";
          this.$refs.elDialog.$el.firstChild.style.top = "47%";
          this.$refs.elDialog.$el.firstChild.style.width = "80%";
          this.isExpand = false;
        });
      } else {
        this.$nextTick(() => {
          this.$refs.elDialog.$el.firstChild.style.height = this.height;
          this.$refs.elDialog.$el.firstChild.style.top = "50%";
          this.$refs.elDialog.$el.firstChild.style.width = this.widthFirst;
          this.isExpand = true;
        });
      }
    },
    closeBtn() {
      this.$emit("update:visible", false); // 直接修改父组件的属性
    },
    clickFullScreen() {
      this.fullFlag = !this.fullFlag;
      this.$emit("fullScreen", this.fullFlag);
    },
  },
};
</script>
<style scoped lang="scss">
/* ::v-deep .el-dialog__wrapper {
  background: red;
} */
::v-deep .screenPopup {
  background: rgba(0, 0, 0, 0.8);
}
.fullBody {
  height: 100%;
  overflow: hidden;
}

::v-deep .el-dialog__body {
  height: calc(100% - 30px);
}

::v-deep .noFullBody {
  max-width: 100%;
  padding: 8px 5px;
  height: 100%;

  /* overflow-y: auto; */
  overflow: auto;
}

::v-deep .noFullBody::-webkit-scrollbar {
  width: 0 !important;
}

/*  dialog*/
::v-deep .el-dialog__header {
  padding: 0;
  color: #fff;
}

//::v-deep .el-dialog__wrapper {
//  z-index: 99999;
//}

::v-deep .el-dialog__headerbtn {
  position: absolute;
  right: 10px;
  top: 51%;
  transform: translate(0, -50%);
  font-size: 26px;
}

::v-deep .el-dialog__title {
  color: #fff !important;
  padding-left: 5px;
  font-size: 18px;
}

::v-deep .avue-crud__dialog__header {
  width: calc(100% - 10px);
  margin-top: 0px;
}
@font-face {
  font-family: "myfont";
  src: url("~@/assets/font/youshebiaotihei.ttf");
}
::v-deep .el-dialog__header {
  color: #ffffff;
  padding: 0 5px;
  box-sizing: border-box;
  font-family: "SourceHanSansCN-Regular";
  height: 50px;
  line-height: 50px;
  position: relative;
  border-bottom: solid 1px #00567b;
  /* background: url(~@/assets/images/bg-title.png) no-repeat; */
  /* background: url(~@/assets/images/bg-title.png) no-repeat; */
  background: url(~@/assets/images/screen/screenDialog/hengshanBg.png) no-repeat;
  background-size: 97% 99%;
  background-position: 7% 1%;
}

::v-deep .el-dialog:not(.is-fullscreen) {
  /* margin-top: 15vh!important;
      max-height: 80vh;
      overflow-y:scroll ; */
}

::v-deep .el-dialog.is-fullscreen {
  height: 100% !important;
}

/*dialog header*/
.el-dialog__header {
  background: #e3eaed;
}

.title-sign {
  width: 21px;
  height: 20px;
  margin-left: 10px;
  margin-top: 14px;
  margin-right: 4px;
  /* background: #51cdf5; */
}
.title-sign img {
  display: block;
  width: 100%;
  height: 100%;
}
.avue-crud__dialog__header {
  /* display: -webkit-box;
    display: -ms-flexbox;
    display: flex; */
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

::v-deep .el-dialog {
  border: solid 1px #00567b;
  border-radius: 5px;
  max-height: 100vh;
  border: 1px solid rgba(0, 107, 148, 0.5);
  background-color: rgb(19 46 72 / 73%);
  position: relative;
  /* -webkit-box-shadow: 0 35px 25px #315e78 inset, 0 -35px 25px #315e78 inset; */
  /* box-shadow: inset 0 35px 25px rgba(60, 188, 221, 0.7),
    inset 0 -15px 25px #1b4376; */

  /* background-image: url("~@/assets/images/logbody.png"); */
}

.el-dialog__title {
  color: rgba(0, 0, 0, 0.85);
  word-wrap: break-word;
  font-size: 21px !important;
  font-weight: 500;
}

.avue-crud__dialog__menu {
  padding-right: 20px;
  float: right;
}

::v-deep .el-dialog__body {
  padding-top: 10px;
  padding-bottom: 10px;
  color: #fff;
}

.avue-crud__dialog__menu i {
  color: #909399;
  font-size: 15px;
  cursor: pointer;
}

.el-icon-full-screen {
  cursor: pointer;
}

.el-icon-full-screen:before {
  content: "\e719";
}

::v-deep .el-pagination {
  padding: 0 5px;
}

.plate_info_angle_1 {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 0px;
  height: 0px;
  border: 4px solid;
  border-color: #15a5e3a1 transparent transparenrgba(21, 165, 227, 0.747) e3;
}

.plate_info_angle_2 {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 0px;
  height: 0px;
  border: 4px solid;
  border-color: #15a5e3 #15a5e3 transparent transparent;
}

.plate_info_angle_3 {
  position: absolute;
  right: 2px;
  bottom: 2px;
  width: 0px;
  height: 0px;
  border: 4px solid;
  border-color: transparent #15a5e3 #15a5e3 transparent;
}

.plate_info_angle_4 {
  position: absolute;
  bottom: 2px;
  left: 2px;
  width: 0px;
  height: 0px;
  border: 4px solid;
  border-color: transparent transparent #15a5e3 #15a5e3;
}


::v-deep .el-input__inner {
  border: 1px solid #337ab7;
}

::v-deep .el-input__inner,
::v-deep .el-range-input {
  background: none !important;

  color: #fff;
}

::v-deep .el-input__innerel-input__inner {
  background: none !important;
  border: 1px solid #337ab7 !important;
}

/* ----------------------- */

::v-deep .el-pagination.is-background .btn-prev,
.el-pagination.is-background .btn-next,
.el-pagination.is-background .el-pager li {
  margin: 0 5px;
  background-color: #354f68 !important;
  color: #606266;
  min-width: 30px;
  border-radius: 2px;
}

::v-deep .el-pager li {
  background: #354f68 !important;
}

::v-deep .el-pagination .btn-next {
  margin: 0 5px;
  background-color: #354f68 !important;
  color: #606266;
  min-width: 30px;
  border-radius: 2px;
}

::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: #295480 !important;
  color: #ffffff;
}

::v-deep .pagination-container {
  position: relative;
  height: auto;
  margin-bottom: 10px;
  margin-top: 15px;
  background: none !important;
  padding: 10px 20px !important;
}

::v-deep .el-input--small .el-input__inner {
  height: 30px;
}

::v-deep .el-tabs__item.is-active {
  color: #349dffa1 !important;
}

::v-deep .el-tabs__item {
  color: #fff;
  font-size: 16px;
}

::v-deep .el-tabs__nav-wrap::after {
  background: none;
}

::v-deep .el-tabs__item:hover {
  color: #349dffa1 !important;
}

/* ::v-deep .el-tabs__active-bar {
    width: 65px !important;
  } */
::v-deep .el-dialog__close {
  color: rgb(60, 188, 221) !important;
}
::v-deep .el-descriptions__body {
  background: rgba(22, 41, 69, 0.8);
}

::v-deep .el-descriptions-item__label.is-bordered-label {
  background: transparent;
  color: #fff;
}
::v-deep .el-descriptions--medium.is-bordered .el-descriptions-item__cell {
  color: #ddd;
  border: 1px solid rgba(255, 255, 255, 0.4);
}

::v-deep .newClassName {
  background: url(~@/assets/images/screen/screenDialog/youcheBg.png) no-repeat !important;
  background-size: 100% 100% !important;
}
</style>
