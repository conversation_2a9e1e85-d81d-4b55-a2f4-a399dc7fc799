import {getWeiyiPoints, getShenyaPoints, getVideoPoints} from "@/api/admin/platformmanage/section/section"
import {roamingCoords} from "@/components/DigitalTwin/roamingCooord";
// import { uavData } from "@/views/screen/polling/components/uavData.js";
import {parseTime} from "@/utils/zhy";
// import { addVrRecord } from "@/api/screenApi/youche/siguan";
import {hsskCameraRoamingData} from "@/components/DigitalTwin/hsskCameraRoamingData";
import store from "@/store";
import cache from "@/plugins/cache";

let poiInfo = null
let poiId = null
let personModelInfo = {
  eid: '-9150751364756233164',
  path: [],
  speed: 3,
  times: 1,
  cameraTrace: false,
  state: 0,
  distance: 10,
  poiList: [],
  mapViewerOptions: [],
  timer: null,
  startTime: null,
  endTime: null,
}
let surfaceWaterobjH=30
let digitalTwinApi = {

  /**
   * 根据eid获取水面位置高度信息并进行移动水面
   * @param option
   * @returns {Promise<void>}
   */
  async getWaterFaceInfoAndMove(option) {
    let pitch=90
    if (option.targetWaterHeight>surfaceWaterobjH){
      pitch=-90
    }
    if (option.targetWaterHeight==surfaceWaterobjH){
      return null
    }
    const res = await window.cloudRender.Scene.GetByEids([option.surfaceWaterEID]);
    let obj=await res.result[0].Get()
    console.log("水面", obj);
    let longitude= obj.result.location[0]
    let latitude=obj.result.location[1]
    const path = new window.cloudRender.Path({
      "polyline": {
        "coordinates": [
          [longitude,latitude,surfaceWaterobjH],
          [longitude,latitude,option.targetWaterHeight],
        ],
      },
      "pathStyle": {
        "type": "arrow",
        "width": 20,
        "color": "a54cffff",
        "passColor": "c9ff23ff"
      },
      "bVisible": false //是否可见(true/false)
    });
    const { success } = await window.cloudRender.Scene.Add(path);
    if (success) {
      const moveObj = new window.cloudRender.Bound({
        "moving": res.result[0],
        "path": path,
        "boundStyle": {
          "time": 5,
          "bLoop": false,
          "bReverse": false,
          "state":"play",
        },
        "rotator":{
          "pitch": pitch,
          "yaw": 0,
          "roll": 0
        },
        "offset":{
          "left": 0,
          "forward": 0,
          "up": 0
        },
      });
      const { success }=await window.cloudRender.Scene.Add(moveObj);
      if (success) {
        surfaceWaterobjH=option.targetWaterHeight // 修改当前水位高度
      }
    }
  },

  /**
   * 白浪河 水花显隐
   * @param isShow
   * @returns {Promise<void>}
   */
  async showSpray(isShow) {
    console.log("水花", isShow)
    let jsondata = {
      "apiClassName": "CustomApi",
      "apiFuncName": "Sluice",
      "args": {
        "placename": "水库溢洪闸",
        "Ids": ["01", "02", "03"],
        "action": "setvisibility",
        "moreparameters": {
          "show": isShow
        }
      }
    }
    const res = await window.cloudRender.Customize.RunCustomizeApi(jsondata);
    console.log("水花显隐:", res);
  },

  /**
   * 白浪河 闸门开度
   * @param openrange (0-1)
   * @returns {Promise<void>}
   */
  async openFloodgates(openrange) {
    let jsondata = {
      "apiClassName": "CustomApi",
      "apiFuncName": "Sluice",
      "args": {
        "placename": "水库溢洪闸",
        "Ids": ["01", "02", "03"],
        "action": "open",
        "moreparameters": {
          "openrange": openrange,  // 开启幅度（0~1）
          "duration": "2"  // 持续时间
        }
      }
    }
    const res = await window.cloudRender.Customize.RunCustomizeApi(jsondata);
    console.log("闸门开度:", openrange, res);
  },
  //立体巡检功能
  /**
   * @description 添加普通路径
   * @param routes 航线
   */
  addRoutePath(routes, jsondata) {
    const routeJson = {
      id: jsondata.id,
      advancedSetting: {
        smoothnessOfCorners: "extremelyHigh", //设置路径边角的平滑度(extremelyHigh:极高; high:高; middle:中; low:低;)
      },
      coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
      coord_z_type: jsondata.coord_z_type ? 2 : jsondata.coord_z_type, //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
      cad_mapkey: "", //CAD基准点Key值, 项目中约定
      type: jsondata.type ? jsondata.type : "fit_solid", //样式类型; 注①
      color: jsondata.color ? jsondata.color : "ff0000", //颜色(HEX颜色值, 空值即透明; railway类型无效, 默认黑白色)
      pass_color: jsondata.pass_color ? jsondata.pass_color : "", //覆盖物移动经过路径颜色(HEX颜色值)
      width: jsondata.width ? jsondata.width : 2, //宽度(单位:米, 圆柱直径或方柱边长；当类型为“adaptive_solid”，含义为倍率)
      speedRate: 1, //流动特效的移动倍率,仅针对部分类型有效（arrow,arrow_dot,arrow_dashed,brimless_arrow,scan_line,scan_line_solid）
      points: routes,
    };
    window.cloudRender.SuperAPI('AddPath', routeJson).then((_roamingpath) => {
      console.log("AddPath", _roamingpath)
    })
  },

  /**
   * @description 覆盖物随路径运行
   */
  coverToMove(jsondata) {
    const pathData = {
      "attach_id": jsondata.id,    //要移动的覆盖物id
      "attach_type": jsondata.attach_type ? jsondata.attach_type : "",           //要移动的覆盖物类型 见下表
      "be_attach_id": jsondata.be_attach_id ? jsondata.be_attach_id : "path_id",      //依附的覆盖物id
      "be_attach_type": jsondata.be_attach_type ? jsondata.be_attach_type : "path",       //依附的覆盖物类型 见下表
      "speed": jsondata.speed ? jsondata.speed : 5,                     //移动速度 (单位:米/秒)
      "loop": true,                   //是否循环
      "reverse": false,               //是否反向移动
      "current_attitude": true,       //元素姿态(true: 前一帧的姿态; 注: true优先, "pitch", "yaw", "roll" 无效)
    }
    window.cloudRender.SuperAPI('CoverToMove', pathData).then((_back) => {
      console.log(_back)
    })
  },

  /**
   * @description 镜头跟踪
   */
  cameTrace(jsonData) {
    const jsonCamera = {
      "trace_object_type": jsonData.trace_object_type ? jsonData.trace_object_type : "aes_object",    //支持类型：aes_object  ,poi，场景特效，可视域，3D文字，灯光
      "trace_object_id": jsonData.trace_object_id ? jsonData.trace_object_id : "-9148499563847004871", //对象ID
      "arm_distance": jsonData.arm_distance ? jsonData.arm_distance : 100,
      "fly": true
    }
    window.cloudRender.SuperAPI('CameraTrace', jsonCamera).then((_back) => {
      console.log(_back)
    })
  },
  /**
   * @description 这只是油车的！函数名就不能写清楚！返回初始视角
   */
  defaultCameraView() {
    let jsondata = {
      coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
      cad_mapkey: "", //CAD基准点Key值, 项目中约定
      coord_z: "28.021563", //海拔高度(单位:米)
      center_coord: "119.757355,31.219568", //中心点的坐标 lng,lat
      arm_distance: "172.622116", //镜头距中心点距离(单位:米)
      pitch: "18.238342", //镜头俯仰角(5~89)
      yaw: "227.0", //镜头偏航角(0正北, 0~359)
      fly: false, //true: 飞行动画(有一个短暂飞行动画,并按照arm_distance,pitch,yaw设置镜头);
    }
    window.cloudRender.SuperAPI('SetCameraInfo', jsondata).then((_back) => {
      console.log(_back)
    })
  },

  /**
   * @description 告警点 油车
   */
  addSinglePoint(point) {
    if (String(point.id) == poiId) {
      let jsondata = {
        "id": String(point.id),
        "always_show_label": 0,              //是否永远显示label, true:显示label(默认), false:不显示label
        "show_label_range": "0,200",       //此POI点显示label的范围(单位:米, 范围最小、最大距离; 在此范围内显示, 超出范围隐藏; 注意: always_show_label 属性优先于此属性)
        "sort_order": false,                 //是否开启遮挡排序(根据POI点真实3D位置开启遮挡排序,
        //注1: 只与其他开启排序的POI之间进行排序; 注2: 开启此排序会消耗性能(最多60个POI点同时开启排序))
        "state": "state_1",                  //与marker之中images中的define_state对应
        "animation_type": "stretch",          //动画类型(bounce:弹出式; stretch:伸缩式; wipe:展开式)
        "duration_time": 0.9                 //规定完成动画所花费的时间(单位:秒)
      }
      window.cloudRender.SuperAPI('UpdateCustomPOIStyle', jsondata).then((_back) => {
        console.log(_back)
      })
    } else {
      let jsonPoint = {
        id: String(point.id),
        coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
        cad_mapkey: "", //CAD基准点的Key值, 项目中约定
        adaptive: false, //true:自适应大小;false:默认
        coord: point.coord, //POI点的坐标 lng,lat
        coord_z: point.coord_z ? point.coord_z : 0.0, //高度(单位:米)
        coord_z_type: point.coord_z_type ? 2 : point.coord_z_type, //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
        always_show_label: false, //是否永远显示label, true:显示label(默认), false:不显示label
        show_label_range: "0,200", //POI点显示label的范围(单位:米, 范围最小、最大距离; 在此范围内显示, 超出范围隐藏; 注意:always_show_label属性优先于此属性)
        sort_order: false, //是否开启遮挡排序(根据POI点真实3D位置开启遮挡排序,
        //注: 只与其他开启排序的POI之间进行排序, 开启此排序会消耗性能(最多60个POI点同时开启排序)
        animation_type: "bounce", //动画类型(bounce:弹出式; stretch:伸缩式; wipe:展开式)
        duration_time: 0.7, //规定完成动画所花费的时间(单位:秒)
        state: "state_1", //与marker之中images中的define_state对应
        marker: {
          size: "40,40", //marker大小(宽,高 单位:像素)
          images: [
            {
              define_state: "state_1", //marker图片组
              normal_url:
                point.image || "http://www.zhywater.com:18190/static/sylogo/yx_wrj.png", //normal 图片url地址
              activate_url:
                point.image || "http://www.zhywater.com:18190/static/sylogo/yx_wrj.png", //hover, active 图片url地址
            },
          ],
        },
        label: {
          bg_image_url: "http://superapi.51hitech.com/doc-static/images/static/LabelBg.png",
          bg_size: "300,50", //label大小(宽, 高 单位:像素)
          bg_offset: "25,40", //整个label左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
          content: [
            {
              text: ["告警类型：" + point.description, "ffffff", "15"], //[文本内容, HEXA颜色, 文本大小]
              text_offset: "5,5", //文本框左上角相对于label中bg_offset左上角的margin偏移(x,y 单位:像素), 注: x为正向右, y为正向下
              text_boxwidth: 300, //文本框宽度
              text_centered: false, //文本居中(true:居中; false:不居中)
              scroll_speed: 0, //文本滚动速度(0:不滚动; 注: "text_centered":true优先)
            },
            {
              text: ["告警时间：" + point.alarmTime, "ffffff", "15"], //[文本内容, HEXA颜色, 文本大小]
              text_offset: "5,25", //文本框左上角相对于label中bg_offset左上角的margin偏移(x,y 单位:像素), 注: x为正向右, y为正向下
              text_boxwidth: 300, //文本框宽度
              text_centered: false, //文本居中(true:居中; false:不居中)
              scroll_speed: 0, //文本滚动速度(0:不滚动; 注: "text_centered":true优先)
            }
          ],
        },
      };
      poiId = String(point.id)
      window.cloudRender
        .SuperAPI("AddCustomPOI", jsonPoint)
        .then((_back) => {
          console.log(_back, "_back");
        });
    }
  },

  //流量坐标点和信息
  waterLabelShow: true,
  addWaterCoord(point) {
    // this.deletePoi(point.deviceId, "poi")
    if (point.deviceId == poiInfo) {
      let jsondata = {
        id: point.deviceId,
        coord_type: 0,                     //坐标类型(0:经纬度坐标, 1:cad坐标)
        cad_mapkey: "",                    //CAD基准点的Key值, 项目中约定
        coord: point.longitudeWgs84 + "," + point.latitudeWgs84,     //POI点的坐标 lng,lat
        coord_z: point.coord_z,                        //高度(单位:米)
        coord_z_type: 2                  //高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
      }
      window.cloudRender.SuperAPI("UpdateCustomPOICoord", jsondata, (status) => {
        // console.log(status); //成功、失败回调
        window.cloudRender.SuperAPI('ShowHideCovering', {
          id: point.deviceId,            //覆盖物id
          covering_type: "poi",    //覆盖物类型, 详见下表
          bshow: this.waterLabelShow              //true:显示; false:隐藏
        }).then((_back) => {
          // console.log("hide", _back)
        })
      })
      let jsonPoint = {
        id: point.deviceId,
        label: {
          bg_image_url: "http://superapi.51hitech.com/doc-static/images/static/LabelBg.png",
          bg_size: "200,140", //label大小(宽, 高 单位:像素)
          bg_offset: "10,150", //整个label左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
          "content": [
            {
              "text": ["电导率(mS/cm)：" + point.wq.ct, "ffffff", "15"],   //[文本内容, HEXA颜色, 文本大小]
              "text_offset": "5,5",    //文本框左上角相对于label中bg_offset左上角的margin偏移(x,y 单位:像素), 注: x为正向右, y为正向下
              "text_boxwidth": 200,     //文本框宽度
              "text_centered": false,  //文本居中(true:居中; false:不居中)
              "scroll_speed": 0       //文本滚动速度(0:不滚动; 注: "text_centered":true优先)
            },
            {
              "text": ["溶解氧(mg/L)：" + point.wq.doxygen, "ffffff", "15"],
              "text_offset": "5,25",
              "text_boxwidth": 200,
              "text_centered": false,
              "scroll_speed": 0
            },
            {
              "text": ["浊度(NTU)：" + point.wq.tur, "ffffff", "15"],
              "text_offset": "5,45",
              "text_boxwidth": 200,
              "text_centered": false,
              "scroll_speed": 0
            },
            {
              "text": ["PH值：" + point.wq.ph, "ffffff", "15"],
              "text_offset": "5,65",
              "text_boxwidth": 200,
              "text_centered": false,
              "scroll_speed": 0
            },
            {
              "text": ["温度(°C)：" + point.wq.temper, "ffffff", "15"],
              "text_offset": "5,85",
              "text_boxwidth": 200,
              "text_centered": false,
              "scroll_speed": 0
            },
            {
              "text": ["氨氮(mg/L)：" + point.wq.nh3, "ffffff", "15"],
              "text_offset": "5,105",
              "text_boxwidth": 200,
              "text_centered": false,
              "scroll_speed": 0
            }
          ]
        },
      };
      window.cloudRender.SuperAPI("UpdateCustomPOILabel", jsonPoint, (status) => {
        console.log(status); //成功、失败回调
        window.cloudRender.SuperAPI('ShowHideCovering', {
          id: point.deviceId,            //覆盖物id
          covering_type: "poi",    //覆盖物类型, 详见下表
          bshow: this.waterLabelShow              //true:显示; false:隐藏
        }).then((_back) => {
          // console.log("hide", _back)
        })
      })
    } else {
      let jsonPoint = {
        id: point.deviceId,
        coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
        cad_mapkey: "", //CAD基准点的Key值, 项目中约定
        adaptive: false, //true:自适应大小;false:默认
        coord: point.coord, //POI点的坐标 lng,lat
        coord_z: point.coord_z, //高度(单位:米)
        coord_z_type: 2, //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
        always_show_label: false, //是否永远显示label, true:显示label(默认), false:不显示label
        show_label_range: "0,80", //POI点显示label的范围(单位:米, 范围最小、最大距离; 在此范围内显示, 超出范围隐藏; 注意:always_show_label属性优先于此属性)
        sort_order: false, //是否开启遮挡排序(根据POI点真实3D位置开启遮挡排序,
        //注: 只与其他开启排序的POI之间进行排序, 开启此排序会消耗性能(最多60个POI点同时开启排序)
        animation_type: "bounce", //动画类型(bounce:弹出式; stretch:伸缩式; wipe:展开式)
        duration_time: 0.1, //规定完成动画所花费的时间(单位:秒)
        state: "state_1", //与marker之中images中的define_state对应
        "marker": {
          "size": "1,1",                   //marker大小(宽,高 单位:像素)
          "images": [
            {
              "define_state": "state_1",   //marker图片组
              "normal_url": "",        //normal 图片url地址
              "activate_url": ""       //hover, active
            }
          ]
        },
        label: {
          bg_image_url: "http://superapi.51hitech.com/doc-static/images/static/LabelBg.png",
          bg_size: "200,140", //label大小(宽, 高 单位:像素)
          bg_offset: "10,150", //整个label左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
          "content": [
            {
              "text": ["电导率(mS/cm)：" + point.wq.ct, "ffffff", "15"],   //[文本内容, HEXA颜色, 文本大小]
              "text_offset": "5,5",    //文本框左上角相对于label中bg_offset左上角的margin偏移(x,y 单位:像素), 注: x为正向右, y为正向下
              "text_boxwidth": 200,     //文本框宽度
              "text_centered": false,  //文本居中(true:居中; false:不居中)
              "scroll_speed": 0       //文本滚动速度(0:不滚动; 注: "text_centered":true优先)
            },
            {
              "text": ["溶解氧(mg/L)：" + point.wq.doxygen, "ffffff", "15"],
              "text_offset": "5,25",
              "text_boxwidth": 200,
              "text_centered": false,
              "scroll_speed": 0
            },
            {
              "text": ["浊度(NTU)：" + point.wq.tur, "ffffff", "15"],
              "text_offset": "5,45",
              "text_boxwidth": 200,
              "text_centered": false,
              "scroll_speed": 0
            },
            {
              "text": ["PH值：" + point.wq.ph, "ffffff", "15"],
              "text_offset": "5,65",
              "text_boxwidth": 200,
              "text_centered": false,
              "scroll_speed": 0
            },
            {
              "text": ["温度(°C)：" + point.wq.temper, "ffffff", "15"],
              "text_offset": "5,85",
              "text_boxwidth": 200,
              "text_centered": false,
              "scroll_speed": 0
            },
            {
              "text": ["氨氮(mg/L)：" + point.wq.nh3, "ffffff", "15"],
              "text_offset": "5,105",
              "text_boxwidth": 200,
              "text_centered": false,
              "scroll_speed": 0
            }
          ]
        },
      };
      poiInfo = point.deviceId
      window.cloudRender
        .SuperAPI("AddCustomPOI", jsonPoint)
        .then((_back) => {
          // console.log(_back, "_back");
          window.cloudRender.SuperAPI('ShowHideCovering', {
            id: point.deviceId,            //覆盖物id
            covering_type: "poi",    //覆盖物类型, 详见下表
            bshow: this.waterLabelShow             //true:显示; false:隐藏
          }).then((_back) => {
            // console.log("hide", _back)
          })
        });

    }

  },

  /**
   * @description 巡查漫游特效（人工、无人机、无人船）
   */
  async roamingPatrol(roamingCoord, {coord_z_type, modelEid, pathId}) {
    // 路径
    let pathjson = {
      "id": pathId,    //路径id
      "coord_type": 0,         //路径中坐标类型(0:经纬度坐标, 1:cad坐标)
      "cad_mapkey": "",        //CAD基准点Key值, 项目中约定
      "coord_z_type": coord_z_type == 2 ? 2 : 0,       //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
      "type": "none",       //路径样式类型(none, solid, arrow, arrow_dot, dashed_dot, arrow_dashed, flash, scan_line, brimless_arrow, railway, railway, square_pipe, dashed_line)
      "color": "",       //路径颜色(HEX颜色值, 空值即透明; railway类型无效, 默认黑白色)
      "pass_color": "",  //覆盖物移动经过路径颜色(HEX颜色值)
      "width": 10,             //宽度(单位:米, 圆柱直径或方柱边长)
      "speedRate": 1,
      "points": roamingCoord
    }
    // 覆盖物沿路径移动
    let CoverToMoveData = {
      "attach_id": modelEid,    //要移动的覆盖物id (POI, 场景特效; 需提前创建完毕)
      "attach_type": "aes_object",    //要移动的覆盖物类型 (poi, scene_effect, viewshed)
      "be_attach_id": pathId,   //依附的覆盖物id (路径, 区域, 圆形区域; 需提前创建完毕)
      "be_attach_type": "path",         //依附的覆盖物类型 (path, range, circular_range)
      "speed": 3,                     //移动速度 (单位:米/秒)
      "loop": false,                    //是否循环
      "reverse": false,                 //是否反向移动,
      "current_attitude": true,       //元素姿态(true: 前一帧的姿态; 注: true优先, "pitch", "yaw", "roll" 无效)
    }
    // 镜头沿覆盖物移动
    let CameraToMoveData = {
      "be_attach_id": pathId,  //依附的覆盖物id (路径, 区域, 圆形区域; 需提前创建完毕)
      "be_attach_type": "path",        //依附的覆盖物类型 (path, range, circular_range)
      "speed": 3,                    //移动速度 (单位:米/秒)
      "loop": false,                   //是否循环
      "reverse": false,                //是否反向移动
      "arm_distance": 15,             //镜头距覆盖物距离(单位:米)
      "pitch": 25                     //镜头俯仰角(0 ~ 89)
    }
    //模型动画
    let jsondata = {
      "eid": modelEid,
      "clip_name": "AESFBX_Anim",                  //片段名称
      "start_and_end": "1,32",       //动画起止帧（按动画默认总帧数计算）
      //"12,"从第12帧播放到最后一帧；"12,12"停止到第12帧；","停止到最后一帧；"1,"从第一帧播放到最后一帧
      "play_rate": 1,           //播放倍速
      "loop": true,            //是否循环
      "reverse": false       //是否倒放
    }
    window.cloudRender
      .SuperAPI("ShowHideAESObject", {
        eid: modelEid,
        bshow: true, //true:显示; false:隐藏
      })
      .then((_back) => {
        console.log(_back);
      });
    window.cloudRender.SuperAPI('AddPath', pathjson, (mycallback) => {
      console.log('镜头沿覆盖物移动路径: ', mycallback)
      let obj = JSON.parse(mycallback)
      if (obj.success) {
        // setTimeout(() => {
        window.cloudRender.SuperAPI('SetAESObjectAnimationPlay', jsondata, (mycallback) => {
          console.log('动画开始: ', mycallback)
        })
        window.cloudRender.SuperAPI('CoverToMove', CoverToMoveData, (mycallback) => {
          console.log('覆盖物沿路径移动行为: ', mycallback)
        })
        window.cloudRender.SuperAPI("SetCameraToMove", CameraToMoveData, (mycallback) => {
          console.log('镜头沿路径移动行为: ', mycallback)
        })
      }
    })
  },

  /**
   * @description 无人机飞行
   */
  uavFly: true,
  //无人机飞行路径
  cameraRoaming: false,
  uavFlySocketCopy(data, uavId) {
    let obj = uavData.find((item) => {
      return item.id == String(uavId);
    });
    if (obj) {
      let state = obj.state;
      //5s延迟
      if (state != null && (new Date().getTime() - state.time.getTime()) / 1000 < 5) {
        return;
      }
      //作位置偏移调整
      data.coord = Number(data.longitudeWgs84 + 0.000017) + "," + Number(data.latitudeWgs84 - 0.000083);

      if (obj.state == null) {
        obj.state = data;
        obj.state.time = new Date();
        this.setAESObjectAnimationPlay(obj.eid, true);
      } else {
        data.time = new Date();
        if (data.coord == state.coord && data.coord_z == state.coord_z) {
          obj.state = data;
          return;
        }
        let path = obj.path.find((item) => {
          return item.id == "uavPath" + uavId;
        });
        let sCoord = state.coord;
        let dCoord = data.coord;
        let h = state.altitude - data.altitude;
        let distance = this.getCameraAndPOIDistance(sCoord, dCoord, h);
        let jo = {
          attach_id: String(obj.eid), //要移动的覆盖物id
          attach_type: "aes_object", //要移动的覆盖物类型 见下表
          be_attach_id: "uavPath" + uavId, //依附的覆盖物id
          be_attach_type: "path", //依附的覆盖物类型 见下表
          speed: Math.ceil(Number(distance) / ((data.time.getTime() - state.time.getTime()) / 1000)) || 2.0, // state.vel != null?Number(state.vel).toFixed(0):Math.ceil(Number(distance)/((data.time.getTime() - state.time.getTime())/1000)), //移动速度 (单位:米/秒)
          current_attitude: this.uavFly,
          // Number(data.coord_z) - Number(state.coord_z - 0.07) !== 0,
          //   Number(data.coord_z) - Number(state.coord_z) !== 0, //元素姿态(true: 前一帧的姿态; 注: true优先, "pitch", "yaw", "roll" 无效)
          pitch: 0, //俯仰角, 参考值(-90~90)
          yaw: state.yaw, //偏航角, 参考值(0~360)
          roll: 0, //翻滚角, 参考值(0~360)
        };
        //fb761c00-fc57-11ee-ba2a-913e88ae4e70
        let jodata = {
          attach_id: "fb761c00-fc57-11ee-ba2a-913e88ae4e70", //要移动的覆盖物id
          attach_type: "scene_effect", //要移动的覆盖物类型 见下表
          be_attach_id: "uavPath" + uavId, //依附的覆盖物id
          be_attach_type: "path", //依附的覆盖物类型 见下表
          speed: Math.ceil(Number(distance) / ((data.time.getTime() - state.time.getTime()) / 1000)) || 2.0, // state.vel != null?Number(state.vel).toFixed(0):Math.ceil(Number(distance)/((data.time.getTime() - state.time.getTime())/1000)), //移动速度 (单位:米/秒)
          current_attitude: true,
          // Number(data.coord_z) - Number(state.coord_z - 0.07) !== 0,
          //   Number(data.coord_z) - Number(state.coord_z) !== 0, //元素姿态(true: 前一帧的姿态; 注: true优先, "pitch", "yaw", "roll" 无效)
          pitch: 0, //俯仰角, 参考值(-90~90)
          yaw: state.yaw, //偏航角, 参考值(0~360)
          roll: 0, //翻滚角, 参考值(0~360)
        };
        let cameraRoaming = {
          coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
          cad_mapkey: "", //CAD基准点Key值, 项目中约定
          coord_z_type: 2, //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
          subsidiary_show: false, //是否显示辅助线(true:显示; false:不显示)
          points: [
            {
              coord: state.coord, //路径坐标点 lng,lat
              coord_z: state.coord_z > 30 ? state.coord_z : 30, //高度(单位:米, cad坐标无效)
              coord_easetype: "linear", //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
              arm_distance: obj.arm_distance, //镜头与坐标点距离(单位:米)
              pitch: Number(state.gimbalPitch) >= 0 ? Number(state.gimbalPitch) + 45 : -state.gimbalPitch - 1,
              yaw: state.yaw >= 0 ? state.yaw : state.yaw + 360, //镜头偏航角(0正北, 0~359)
              attitude_easetype: "Linear", //镜头漫游至下一坐标点缓动姿态类型(见下表)
              time: (data.time.getTime() - state.time.getTime()) / 1000, //镜头漫游至下一坐标点所花费的时间(单位:秒)
              speed_easetype: "linear", //镜头漫游速度类型(见下表)
            },

            {
              coord: data.coord, //路径坐标点 lng,lat
              coord_z: data.coord_z > 30 ? data.coord_z : 30, //高度(单位:米, cad坐标无效)
              coord_easetype: "linear", //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
              arm_distance: obj.arm_distance, //镜头与坐标点距离(单位:米)
              pitch: Number(data.gimbalPitch) >= 0 ? Number(data.gimbalPitch) + 45 : -data.gimbalPitch - 1,
              yaw: data.yaw >= 0 ? data.yaw : data.yaw + 360, //镜头偏航角(0正北, 0~359)
              attitude_easetype: "Linear", //镜头漫游至下一坐标点缓动姿态类型(见下表)
              // "time": 1,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
              speed_easetype: "linear", //镜头漫游速度类型(见下表)

            },
          ],

        };
        if (path != null) {
          path.points = [
            {
              coord: sCoord,
              coord_z: Number(state.coord_z), // coord_z: Number(state.relativeAlt),

            },
            {
              coord: dCoord,
              coord_z: Number(data.coord_z), // coord_z: Number(data.relativeAlt),
            },
          ];
          obj.state = data;
          if (sCoord == dCoord && state.coord_z == data.coord_z) {
            this.setAESObjectAnimationPlay(obj.eid, false);
            return;
          }
          window.cloudRender.SuperAPI("UpdatePathCoord", path, (status) => {
            this.CoverToMove(jo);
            // window.cloudRender.SuperAPI('SetLogMode', true);
            this.CoverToMove(jodata);
            if (this.cameraRoaming) {
              this.SetCameraRoamingPro(cameraRoaming);
            }
          });
        } else {
          path = {
            id: "uavPath" + uavId,
            advancedSetting: {
              smoothnessOfCorners: "extremelyHigh", //设置路径边角的平滑度(extremelyHigh:极高; high:高; middle:中; low:低;)
            },
            coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
            coord_z_type: obj.coord_z_type, //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
            cad_mapkey: "", //CAD基准点Key值, 项目中约定
            type: "solid", //样式类型; 注①
            color: "", //颜色(HEX颜色值, 空值即透明; railway类型无效, 默认黑白色)
            pass_color: "", //覆盖物移动经过路径颜色(HEX颜色值)
            width: 1, //宽度(单位:米, 圆柱直径或方柱边长；当类型为“adaptive_solid”，含义为倍率)
            speedRate: 1,
            points: [
              {
                coord: sCoord,
                coord_z: Number(state.coord_z),
              },
              {
                coord: dCoord,
                coord_z: Number(data.coord_z),
              },
            ],
          };
          obj.state = data;
          obj.path.push(path);
          window.cloudRender.SuperAPI("AddPath", path, (status) => {
            this.CoverToMove(jo);
            // window.cloudRender.SuperAPI('SetLogMode', true);
            this.CoverToMove(jodata);
            if (this.cameraRoaming) {
              this.SetCameraRoamingPro(cameraRoaming);
            }
          });
        }
      }
    } else {
    }

  },

  //无人船飞行路径
  boardFlySocketCopy(data, boardId) {
    let obj = uavData.find((item) => {
      return item.id == String(boardId);
    });
    if (obj) {
      let state = obj.state;
      let hpr = null;
      // if (state != null && (new Date().getTime() - state.timeStamp) / 1000 < 3) return
      let yawChange;
      if (state == null) {
        hpr = this.calculateYaw(obj.latitudeWgs84, obj.longitudeWgs84, data.latitudeWgs84, data.longitudeWgs84)
        console.log(hpr, 'hpr null');
        obj.state = data
        state = data
      } else {
        hpr = this.calculateYaw(state.latitudeWgs84, state.longitudeWgs84, data.latitudeWgs84, data.longitudeWgs84)
        console.log(hpr, 'hpr state');
        console.log(((data.timeStamp - state.timeStamp) / 1000).toFixed(3), '(data.timeStamp - state.timeStamp) / 1000).toFixed(3)');
      }
      let yaw = hpr
      let dataArray = [{
        "object": [   //当object中包含多个对象时, 他们拥有相同的坐标（经纬高）, 但姿态只对aes_object生效, 目前只支持自定义poi、scene_effect、aes_objct三种类型
          {
            "id": [obj.eid],
            "type": "aes_object"
          }
        ],
        "coord": data.coord, //目的地的坐标, 对象们从他们当前位置向目的地移动(lng,lat)
        "coord_type": 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
        "cad_mapkey": "", //CAD基准点Key值, 通过“注册CAD锚点”API创建；coord_type为0时可为空
        "coord_z": data.coord_z, //高度(单位:米, cad坐标无效)
        "coord_z_type": 2, //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
        // "periodic": ((data.timeStamp - state.timeStamp) / 1000).toFixed(3), //周期, 当前状态到该状态所需时间
        // "periodic": Math.ceil(Number(distance) / ((data.timeStamp - state.timeStamp) / 1000)) || 2.0, //周期, 当前状态到该状态所需时间
        "periodic": 0.5, //周期, 当前状态到该状态所需时间
        "pitch": 0, //俯仰角,姿态数据只对aes_object生效, 当aes_object到达目的地后, 在一个短暂时间内, 旋转到目标姿态
        "yaw": hpr, //偏航角
        "default_yaw": true,  //true：根据运动方向自动计算各姿态角的0度方向；false：以固定yaw值运动
        "roll": 0 //滚动角
      }]
      let jsondata = {
        "data": dataArray
      }
      obj.state = data
      window.cloudRender.SuperAPI('DataDriveObjectToMove', jsondata).then((_back) => {
        console.log(_back)
      })
    }
  },
  //无人船
  stateArry: [],
  boardFly: true,
  firstYaw: 0,
  boardSocketCopy(data, boardId) {
    let obj = uavData.find((item) => {
      return item.id == String(boardId);
    });
    let yaw = null
    if (obj) {
      let state = obj.state;
      // state.time = new Date()
      //2s延迟
      if (state != null && (data.timeStamp - state.timeStamp) / 1000 < 3) {
        this.stateArry.push(data)
        //拿到了socket返回的第二个点
        //这时候已经有了第一个点
        yaw = this.calculateBearing(state.latitudeWgs84, state.longitudeWgs84, this.stateArry[0].latitudeWgs84, this.stateArry[0].longitudeWgs84)
        // data.time = new Date();
        // yaw = this.calculateBearing(state.latitudeWgs84, state.longitudeWgs84, data.latitudeWgs84, data.longitudeWgs84)
        this.firstYaw = yaw + 190
        this.firstYaw > 360 ? (this.firstYaw = this.firstYaw - 360) : this.firstYaw
        if (this.firstYaw > 300 && this.firstYaw < 360) {
          this.firstYaw = this.firstYaw - 180
        } else if (this.firstYaw > 0 && this.firstYaw < 90) {
          this.firstYaw = this.firstYaw + 90
        }
        // obj.ownState = data
        return;
      }
      this.stateArry = []
      if (obj.state == null) {
        // console.log(0, '++obj.ownState');
        obj.state = data;
        obj.state.longitudeWgs84 = obj.longitudeWgs84,
          obj.state.latitudeWgs84 = obj.latitudeWgs84
        //websocket返回的第一个点和无人船的初始点做偏航角计算
        // yaw = this.calculateBearing(obj.latitudeWgs84, obj.longitudeWgs84, data.latitudeWgs84, data.longitudeWgs84)
        // this.firstYaw = yaw
        obj.state.time = new Date();
        // this.setAESObjectAnimationPlay(obj.eid, true);
      } else {
        // data.time = new Date();
        // if (data.coord == state.coord && data.coord_z == state.coord_z) {
        //     obj.state = data;
        //     return;
        // }
        let path = obj.path.find((item) => {
          return item.id == "boardPath" + boardId;
        });
        let sCoord = state.coord;
        let dCoord = data.coord;
        let h = state.altitude - data.altitude;
        let distance = this.getCameraAndPOIDistance(sCoord, dCoord, h);
        //计算偏航角，要根据每段路径的前两个点计算偏转方向
        // yaw = this.calculateBearing(obj.ownState.latitudeWgs84, obj.ownState.longitudeWgs84, data.latitudeWgs84, data.longitudeWgs84)
        // console.log(this.firstYaw, 'this.firstYaw obj.ownState');
        let joyaw = {
          attach_id: String(obj.eid), //要移动的覆盖物id
          attach_type: "aes_object", //要移动的覆盖物类型 见下表
          be_attach_id: "boardPath" + boardId, //依附的覆盖物id
          be_attach_type: "path", //依附的覆盖物类型 见下表
          reverse: false,
          speed: Math.ceil(Number(distance) / ((data.timeStamp - state.timeStamp) / 1000)) || 2.0, // state.vel != null?Number(state.vel).toFixed(0):Math.ceil(Number(distance)/((data.time.getTime() - state.time.getTime())/1000)), //移动速度 (单位:米/秒)
          current_attitude: this.boardFly,
          pitch: 0, //俯仰角, 参考值(-90~90)
          yaw: this.firstYaw, //偏航角, 参考值(0~360)
          roll: 0, //翻滚角, 参考值(0~360)
        };
        if (path != null) {
          path.points = [
            {
              coord: sCoord,
              coord_z: Number(state.coord_z), // coord_z: Number(state.relativeAlt),
            },
            {
              coord: dCoord,
              coord_z: Number(data.coord_z), // coord_z: Number(data.relativeAlt),
            },
          ];
          obj.state = data;
          window.cloudRender.SuperAPI("UpdatePathCoord", path, (status) => {
            this.CoverToMove(joyaw);
          });
        } else {
          path = {
            id: "boardPath" + boardId,
            advancedSetting: {
              smoothnessOfCorners: "extremelyHigh", //设置路径边角的平滑度(extremelyHigh:极高; high:高; middle:中; low:低;)
            },
            coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
            coord_z_type: obj.coord_z_type, //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
            cad_mapkey: "", //CAD基准点Key值, 项目中约定
            type: "solid", //样式类型; 注①
            color: "", //颜色(HEX颜色值, 空值即透明; railway类型无效, 默认黑白色)
            pass_color: "", //覆盖物移动经过路径颜色(HEX颜色值)
            width: 1, //宽度(单位:米, 圆柱直径或方柱边长；当类型为“adaptive_solid”，含义为倍率)
            speedRate: 1,
            points: [
              {
                coord: sCoord,
                coord_z: Number(state.coord_z),
              },
              {
                coord: dCoord,
                coord_z: Number(data.coord_z),
              },
            ],
          };
          obj.state = data;
          obj.path.push(path);
          window.cloudRender.SuperAPI("AddPath", path, (status) => {
            this.CoverToMove(joyaw);
          });
        }
      }
    }
  },
  degreesToRadians1(degrees) {
    return degrees * (Math.PI / 180);
  },
  radiansToDegrees1(radians) {
    return radians * (180 / Math.PI);
  },
  calculateBearing(lat1, lon1, lat2, lon2) {
    var φ1 = this.degreesToRadians1(lat1);
    var φ2 = this.degreesToRadians1(lat2);
    var Δλ = this.degreesToRadians1(lon2 - lon1);

    var y = Math.sin(Δλ) * Math.cos(φ2);
    var x = Math.cos(φ1) * Math.sin(φ2) - Math.sin(φ1) * Math.cos(φ2) * Math.cos(Δλ);
    var θ = Math.atan2(y, x);
    // Convert θ from range [-π, π] to [0, 2π]
    var bearing = (this.radiansToDegrees1(θ) + 360) % 360;
    return bearing;
  },

  // var bearing = calculateBearing(lat1, lon1, lat2, lon2);
  startTimeSave: [],
  updateTimeSave: [],
  startPathSave: [],
  updatePathSave: [],
  boardSocketCopy2(data, boardId) {
    let obj = uavData.find((item) => {
      return item.id == String(boardId);
    });
    let yaw = null
    if (obj) {
      let state = obj.state;
      // state.time = new Date()
      this.startTimeSave.push(new Date().getTime())
      if (obj.state == null) {
        if ((data.timeStamp - this.startTimeSave[0]) / 1000 < 4) {
          this.startPathSave.push({
            coord: data.coord,
            coord_z: data.coord_z,
            timeStamp: data.timeStamp,
            longitudeWgs84: data.longitudeWgs84,
            latitudeWgs84: data.latitudeWgs84
          })
          console.log(this.startPathSave, 'this.startPathSave');
          return
        }
        console.log(this.startPathSave, 'this.startPathSave');
        obj.state = data;
        obj.state.time = new Date();
      } else {
        data.time = new Date();
        // if (data.coord == state.coord && data.coord_z == state.coord_z) {
        //     obj.state = data;
        //     return;
        // }
        let path = obj.path.find((item) => {
          return item.id == "boardPath" + boardId;
        });
        console.log(path, 'boardPath');
        let distance = null
        let joyaw = {
          attach_id: String(obj.eid), //要移动的覆盖物id
          attach_type: "aes_object", //要移动的覆盖物类型 见下表
          be_attach_id: "boardPath" + boardId, //依附的覆盖物id
          be_attach_type: "path", //依附的覆盖物类型 见下表
          reverse: false,
          speed: 5.0, // state.vel != null?Number(state.vel).toFixed(0):Math.ceil(Number(distance)/((data.time.getTime() - state.time.getTime())/1000)), //移动速度 (单位:米/秒)
          current_attitude: this.boardFly,
          pitch: 0, //俯仰角, 参考值(-90~90)
          yaw: 0, //偏航角, 参考值(0~360)
          roll: 0, //翻滚角, 参考值(0~360)
        };
        if (path != undefined) {
          this.updateTimeSave.push(new Date().getTime())
          //5s延迟
          if ((data.timeStamp - this.updateTimeSave[0]) / 1000 < 5) {
            this.updatePathSave.push({
              coord: data.coord,
              coord_z: data.coord_z,
              timeStamp: data.timeStamp,
              longitudeWgs84: data.longitudeWgs84,
              latitudeWgs84: data.latitudeWgs84
            })
            return;
          }
          obj.state = data
          distance = this.getCameraAndPOIDistance(this.updatePathSave[0].coord, data.coord, 0)
          joyaw.speed = Math.ceil(
            Number(distance) / ((data.timeStamp - this.updatePathSave[0].timeStamp) / 1000)
          ) || 2.0
          path.points = this.updatePathSave;
          // yaw = this.calculateYaw(state.latitudeWgs84, state.longitudeWgs84, data.latitudeWgs84, data.longitudeWgs84)
          window.cloudRender.SuperAPI("UpdatePathCoord", path, (status) => {
            console.log(status, 'status boardpath');
            for (let i = 0; i < this.updatePathSave.length - 1; i++) {
              yaw = this.calculateYaw(
                this.updatePathSave[i].longitudeWgs84, this.updatePathSave[i].latitudeWgs84,
                this.updatePathSave[i + 1].longitudeWgs84, this.updatePathSave[i + 1].latitudeWgs84
              )
              // let yawChange = yaw + 160
              // yawChange > 360 ? yawChange - 360 : yawChange
              console.log(yaw, 'yaw');
              joyaw.yaw = yaw
              this.CoverToMove(joyaw);
              setTimeout(() => {
                this.updatePathSave = []
              }, 400);
            }
          });
          obj.state = data;
        } else {
          distance = this.getCameraAndPOIDistance(obj.coord, data.coord, 0)
          joyaw.speed = Math.ceil(
            Number(distance) / ((data.timeStamp - this.startPathSave[0].timeStamp) / 1000)
          ) || 2.0;
          path = {
            id: "boardPath" + boardId,
            advancedSetting: {
              smoothnessOfCorners: "extremelyHigh", //设置路径边角的平滑度(extremelyHigh:极高; high:高; middle:中; low:低;)
            },
            coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
            coord_z_type: obj.coord_z_type, //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
            cad_mapkey: "", //CAD基准点Key值, 项目中约定
            type: "solid", //样式类型; 注①
            color: "", //颜色(HEX颜色值, 空值即透明; railway类型无效, 默认黑白色)
            pass_color: "", //覆盖物移动经过路径颜色(HEX颜色值)
            width: 1, //宽度(单位:米, 圆柱直径或方柱边长；当类型为“adaptive_solid”，含义为倍率)
            speedRate: 1,
            points: this.startPathSave,
          };
          obj.state = data;
          obj.path.push(path);
          window.cloudRender.SuperAPI("AddPath", path, (status) => {
            console.log(status, 'status boardpath');
            for (let i = 0; i < this.startPathSave.length - 1; i++) {
              yaw = this.calculateYaw(
                this.startPathSave[i].longitudeWgs84, this.startPathSave[i].latitudeWgs84,
                this.startPathSave[i + 1].longitudeWgs84, this.startPathSave[i + 1].latitudeWgs84
              )
              joyaw = JSON.parse(JSON.stringify(joyaw))
              joyaw.yaw = yaw
              this.CoverToMove(joyaw);
            }
          });

        }
        // let sCoord = state.coord;
        // let dCoord = data.coord;
        // let h = state.altitude - data.altitude;
        // distance = this.getCameraAndPOIDistance(sCoord, dCoord, h);
      }
    }
  },
  boardSocketCopy4(data, boardId) {
    let obj = uavData.find((item) => {
      return item.id == String(boardId);
    });
    let yaw = null
    if (obj) {
      let state = obj.state;
      // state.time = new Date()
      //2s延迟
      // if (state != null && (data.timeStamp - state.timeStamp) / 1000 < 3) {
      //     // data.time = new Date();
      //     return;
      // }
      if (obj.state == null) {
        obj.state = data;
        obj.state.time = new Date();
        // this.setAESObjectAnimationPlay(obj.eid, true);
      } else {
        data.time = new Date();
        if (data.coord == state.coord && data.coord_z == state.coord_z) {
          obj.state = data;
          return;
        }
        let path = obj.path.find((item) => {
          return item.id == "boardPath" + boardId;
        });
        let sCoord = state.coord;
        let dCoord = data.coord;
        let h = state.altitude - data.altitude;
        let distance = this.getCameraAndPOIDistance(sCoord, dCoord, h);
        yaw = this.calculateYaw(state.latitudeWgs84, state.longitudeWgs84, data.latitudeWgs84, data.longitudeWgs84)
        // let yawChange = yaw + 160
        // yawChange > 360 ? yawChange - 360 : yawChange
        console.log(yaw, 'yaw');
        let joyaw = {
          attach_id: String(obj.eid), //要移动的覆盖物id
          attach_type: "aes_object", //要移动的覆盖物类型 见下表
          be_attach_id: "boardPath" + boardId, //依附的覆盖物id
          be_attach_type: "path", //依附的覆盖物类型 见下表
          reverse: false,
          speed: Math.ceil(Number(distance) / ((data.time.timeStamp - state.time.timeStamp) / 1000)) || 2.0, // state.vel != null?Number(state.vel).toFixed(0):Math.ceil(Number(distance)/((data.time.getTime() - state.time.getTime())/1000)), //移动速度 (单位:米/秒)
          current_attitude: this.boardFly,
          pitch: 0, //俯仰角, 参考值(-90~90)
          yaw: yaw, //偏航角, 参考值(0~360)
          roll: 0, //翻滚角, 参考值(0~360)
        };
        if (path != null) {
          path.points = [
            {
              coord: sCoord,
              coord_z: Number(state.coord_z), // coord_z: Number(state.relativeAlt),
            },
            {
              coord: dCoord,
              coord_z: Number(data.coord_z), // coord_z: Number(data.relativeAlt),
            },
          ];
          obj.state = data;
          if (sCoord == dCoord && state.coord_z == data.coord_z) {
            this.setAESObjectAnimationPlay(obj.eid, false);
            return;
          }
          window.cloudRender.SuperAPI("UpdatePathCoord", path, (status) => {
            this.CoverToMove(joyaw);
          });
        } else {
          path = {
            id: "boardPath" + boardId,
            advancedSetting: {
              smoothnessOfCorners: "extremelyHigh", //设置路径边角的平滑度(extremelyHigh:极高; high:高; middle:中; low:低;)
            },
            coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
            coord_z_type: obj.coord_z_type, //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
            cad_mapkey: "", //CAD基准点Key值, 项目中约定
            type: "solid", //样式类型; 注①
            color: "", //颜色(HEX颜色值, 空值即透明; railway类型无效, 默认黑白色)
            pass_color: "", //覆盖物移动经过路径颜色(HEX颜色值)
            width: 1, //宽度(单位:米, 圆柱直径或方柱边长；当类型为“adaptive_solid”，含义为倍率)
            speedRate: 1,
            points: [
              {
                coord: sCoord,
                coord_z: Number(state.coord_z),
              },
              {
                coord: dCoord,
                coord_z: Number(data.coord_z),
              },
            ],
          };
          obj.state = data;
          obj.path.push(path);
          window.cloudRender.SuperAPI("AddPath", path, (status) => {
            this.CoverToMove(joyaw);
          });
        }
      }
    }
  },
  pathCoords: [],
  boardSocketCopy3(data, boardId) {
    let obj = uavData.find((item) => {
      return item.id == String(boardId);
    });
    let yaw = null
    if (obj) {
      let state = obj.state;
      // state.time = new Date()
      //5s延迟
      if (state != null && (data.timeStamp - state.timeStamp) / 1000 < 5) {
        this.pathCoords.push({
          coord: data.coord,
          coord_z: data.coord_z,
          timeStamp: data.timeStamp,
          longitudeWgs84: data.longitudeWgs84,
          latitudeWgs84: data.latitudeWgs84
        })
        // data.time = new Date();
        return;
      }
      if (obj.state == null) {
        obj.state = data;
        obj.state.time = new Date();
        // this.setAESObjectAnimationPlay(obj.eid, true);
      } else {
        data.time = new Date();
        if (data.coord == state.coord && data.coord_z == state.coord_z) {
          obj.state = data;
          return;
        }
        let path = obj.path.find((item) => {
          return item.id == "boardPath" + boardId;
        });
        let sCoord = state.coord;
        let dCoord = data.coord;
        let h = state.altitude - data.altitude;
        let distance = this.getCameraAndPOIDistance(sCoord, dCoord, h);
        let joyaw = {
          attach_id: String(obj.eid), //要移动的覆盖物id
          attach_type: "aes_object", //要移动的覆盖物类型 见下表
          be_attach_id: "boardPath" + boardId, //依附的覆盖物id
          be_attach_type: "path", //依附的覆盖物类型 见下表
          reverse: false,
          speed: Math.ceil(Number(distance) / ((data.time.getTime() - state.time.getTime()) / 1000)) || 2.0, // state.vel != null?Number(state.vel).toFixed(0):Math.ceil(Number(distance)/((data.time.getTime() - state.time.getTime())/1000)), //移动速度 (单位:米/秒)
          current_attitude: this.boardFly,
          pitch: 0, //俯仰角, 参考值(-90~90)
          yaw: 0, //偏航角, 参考值(0~360)
          roll: 0, //翻滚角, 参考值(0~360)
        };
        console.log(this.pathCoords, 'this.pathCoords');
        if (path != null) {
          path.points = [
            {
              coord: sCoord,
              coord_z: Number(state.coord_z), // coord_z: Number(state.relativeAlt),
            },
            {
              coord: dCoord,
              coord_z: Number(data.coord_z), // coord_z: Number(data.relativeAlt),
            },
          ];
          obj.state = data;
          if (sCoord == dCoord && state.coord_z == data.coord_z) {
            this.setAESObjectAnimationPlay(obj.eid, false);
            return;
          }
          window.cloudRender.SuperAPI("UpdatePathCoord", path, (status) => {
            for (let i = 0; i < this.pathCoords.length - 1; i++) {
              yaw = this.calculateYaw(
                this.pathCoords[i].longitudeWgs84, this.pathCoords[i].latitudeWgs84,
                this.pathCoords[i + 1].longitudeWgs84, this.pathCoords[i + 1].latitudeWgs84
              )
              console.log(yaw, 'yaw');
              joyaw = JSON.parse(JSON.stringify(joyaw))
              joyaw.yaw = yaw
              console.log(joyaw, 'joyaw');
              this.CoverToMove(joyaw);
              setTimeout(() => {
                this.pathCoords = []
              }, 200);
            }
          });
        } else {
          path = {
            id: "boardPath" + boardId,
            advancedSetting: {
              smoothnessOfCorners: "extremelyHigh", //设置路径边角的平滑度(extremelyHigh:极高; high:高; middle:中; low:低;)
            },
            coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
            coord_z_type: obj.coord_z_type, //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
            cad_mapkey: "", //CAD基准点Key值, 项目中约定
            type: "solid", //样式类型; 注①
            color: "", //颜色(HEX颜色值, 空值即透明; railway类型无效, 默认黑白色)
            pass_color: "", //覆盖物移动经过路径颜色(HEX颜色值)
            width: 1, //宽度(单位:米, 圆柱直径或方柱边长；当类型为“adaptive_solid”，含义为倍率)
            speedRate: 1,
            points: [
              {
                coord: sCoord,
                coord_z: Number(state.coord_z),
              },
              {
                coord: dCoord,
                coord_z: Number(data.coord_z),
              },
            ],
          };
          obj.state = data;
          obj.path.push(path);
          window.cloudRender.SuperAPI("AddPath", path, (status) => {
            this.CoverToMove(joyaw);
          });
        }
      }
    }
  },
  getCameraAndPOIDistance(point, cameraLocation, b) {
    let p = point.split(",");
    let c = cameraLocation.split(",");
    let a = this.GetDistance(
      Number(p[1]),
      Number(p[0]),
      Number(c[1]),
      Number(c[0])
    );
    return Math.sqrt(Number(a) * Number(a) + Number(b) * Number(b));
  },
  GetDistance(lat1, lng1, lat2, lng2) {
    var radLat1 = this.Rad(lat1);
    var radLat2 = this.Rad(lat2);
    var a = radLat1 - radLat2;
    var b = this.Rad(lng1) - this.Rad(lng2);
    var s =
      2 *
      Math.asin(
        Math.sqrt(
          Math.pow(Math.sin(a / 2), 2) +
          Math.cos(radLat1) *
          Math.cos(radLat2) *
          Math.pow(Math.sin(b / 2), 2)
        )
      );
    s = s * 6378.137; // 地球半径;
    s = Math.round(s * 10000) / 10; //输出为米
    return s;
  },
  Rad(d) {
    return (d * Math.PI) / 180.0;
  },
  // 设置aes对象动画片段播放
  setAESObjectAnimationPlay(eid, play) {
    if (play) {
      let jo = {
        eid: String(eid),
        clip_name: "AESFBX_Anim", //片段名称
        start_and_end: "1,", //动画起止帧（按动画默认总帧数计算）
        //"12,"从第12帧播放到最后一帧；"12,12"停止到第12帧；","停止到最后一帧；"1,"从第一帧播放到最后一帧
        play_rate: 2, //播放倍速
        loop: true, //是否循环
        reverse: false, //是否倒放
      };
      window.cloudRender.SuperAPI(
        "SetAESObjectAnimationPlay",
        jo,
        (status) => {
          console.log("SetAESObjectAnimationPlay", status); //成功、失败回调
        }
      );
    } else {
      let jsondata = {
        eid: String(eid),
        state: "stop",
        //pause:暂停播放; continue:从暂停处继续播放，播放设置沿用暂停前的设置;
        //stop，中止播放，模型重置回初始状态，continue此时无效
      };
      window.cloudRender.SuperAPI(
        "SetAesObjectAnimationPlayState",
        jsondata,
        (status) => {
        }
      );
    }
  },

  degreesToRadians(degrees) {
    return degrees * Math.PI / 180;
  },
  radiansToDegrees(radians) {
    return radians * 180 / Math.PI;
  },
  calculateYaw(lat1, lon1, lat2, lon2) {
    lat1 = this.degreesToRadians(lat1);
    lon1 = this.degreesToRadians(lon1);
    lat2 = this.degreesToRadians(lat2);
    lon2 = this.degreesToRadians(lon2);

    let y = Math.sin(lon2 - lon1) * Math.cos(lat2);
    let x = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(lon2 - lon1);

    let initialBearing = Math.atan2(y, x);

    // 转换为正角度
    let yaw = this.radiansToDegrees(initialBearing);

    // 确保结果在0到360之间
    yaw = (yaw + 360) % 360;

    return yaw;
  },

  //数据驱动对象移动
  dataToMOve(data) {
    let jsondata = {
      "data": [
        {
          "object": [   //当object中包含多个对象时, 他们拥有相同的坐标（经纬高）, 但姿态只对aes_object生效, 目前只支持自定义poi、scene_effect、aes_objct三种类型
            {
              "id": data.eid,
              "type": data.type
            },
          ],
          "coord": data.coord, //目的地的坐标, 对象们从他们当前位置向目的地移动(lng,lat)
          "coord_type": 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
          "cad_mapkey": "default", //CAD基准点Key值, 通过“注册CAD锚点”API创建；coord_type为0时可为空
          "coord_z": data.coord_z, //高度(单位:米, cad坐标无效)
          "coord_z_type": 2, //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
          "periodic": 1, //周期, 当前状态到该状态所需时间
          "pitch": data.pitch, //俯仰角,姿态数据只对aes_object生效, 当aes_object到达目的地后, 在一个短暂时间内, 旋转到目标姿态
          "yaw": data.yaw, //偏航角
          "default_yaw": false,  //true：根据运动方向自动计算各姿态角的0度方向；false：以固定yaw值运动
          "roll": data.yaw //滚动角
        }
      ]
    }

    window.cloudRender.SuperAPI("DataDriveObjectToMove", jsondata, (e) => {
      console.log(e);
    })
  },

  boardDataToMove(data, uavId) {
    let obj = uavData.find((item) => {
      return item.id == String(uavId);
    });
    if (obj) {
      let state = obj.state;
      //5s延迟
      // if (
      //     state != null &&
      //     (new Date().getTime() - state.time.getTime()) / 1000 < 5
      // ) {
      //     return;
      // }
      //作位置偏移调整，
      // if (obj.id == "140") {
      data.coord = data.longitudeWgs84 + "," + data.latitudeWgs84;
      // }

      data.coord_z = data.altitude ? data.altitude : 37.56999969482422;

      if (obj.state == null) {
        obj.state = data;
        obj.state.time = new Date();
        // this.setAESObjectAnimationPlay(obj.eid, true);
      } else {
        data.time = new Date();
        if (data.coord == state.coord && data.coord_z == state.coord_z) {
          obj.state = data;
          return;
        }
        let path = obj.path.find((item) => {
          return item.id == "boardPath" + uavId;
        });
        let sCoord = state.coord;
        let dCoord = data.coord;


        // let h = state.relativeAlt - data.relativeAlt;
        let h = state.altitude - data.altitude;
        let distance = this.getCameraAndPOIDistance(sCoord, dCoord, h);
        let jsondata = {
          "data": [
            {
              "object": [   //当object中包含多个对象时, 他们拥有相同的坐标（经纬高）, 但姿态只对aes_object生效, 目前只支持自定义poi、scene_effect、aes_objct三种类型
                {
                  "id": ["-465846857687687978"],
                  "type": "aes_object"
                }
              ],
              "coord": data.coord, //目的地的坐标, 对象们从他们当前位置向目的地移动(lng,lat)
              "coord_type": 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
              "cad_mapkey": "default", //CAD基准点Key值, 通过“注册CAD锚点”API创建；coord_type为0时可为空
              "coord_z": data.coord_z, //高度(单位:米, cad坐标无效)
              "coord_z_type": 2, //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
              "periodic": data,
              // Math.ceil(
              //     Number(distance) /
              //     ((data.time.getTime() - state.time.getTime()) / 1000)
              // ) || 2.0, //周期, 当前状态到该状态所需时间
              "pitch": 0, //俯仰角,姿态数据只对aes_object生效, 当aes_object到达目的地后, 在一个短暂时间内, 旋转到目标姿态
              "yaw": hpr, //偏航角
              "default_yaw": true,  //true：根据运动方向自动计算各姿态角的0度方向；false：以固定yaw值运动
              "roll": 30 //滚动角
            }
          ]
        }
        window.cloudRender.SuperAPI('DataDriveObjectToMove', jsondata).then((_back) => {
          console.log(_back)
        })
      }


      let jsondata = {
        "data": [
          {
            "object": [   //当object中包含多个对象时, 他们拥有相同的坐标（经纬高）, 但姿态只对aes_object生效, 目前只支持自定义poi、scene_effect、aes_objct三种类型
              {
                "id": ["-465846857687687978"],
                "type": "aes_object"
              }
            ],
            "coord": data.coord, //目的地的坐标, 对象们从他们当前位置向目的地移动(lng,lat)
            "coord_type": 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
            "cad_mapkey": "default", //CAD基准点Key值, 通过“注册CAD锚点”API创建；coord_type为0时可为空
            "coord_z": data.coord_z, //高度(单位:米, cad坐标无效)
            "coord_z_type": 2, //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
            "periodic": Math.ceil(
              Number(distance) /
              ((data.time.getTime() - state.time.getTime()) / 1000)
            ) || 2.0, //周期, 当前状态到该状态所需时间
            "pitch": 0, //俯仰角,姿态数据只对aes_object生效, 当aes_object到达目的地后, 在一个短暂时间内, 旋转到目标姿态
            "yaw": hpr, //偏航角
            "default_yaw": true,  //true：根据运动方向自动计算各姿态角的0度方向；false：以固定yaw值运动
            "roll": 30 //滚动角
          }
        ]
      }
      window.cloudRender.SuperAPI('DataDriveObjectToMove', jsondata).then((_back) => {
        console.log(_back)
      })
    }

  },

  poiId: [],
  cameraInfoTimer: null,
  combinedArray: [],
  //流场
  addDigitalFlood(jsonData) {
    window.cloudRender.SuperAPI("AddGeoPath", jsonData, (status) => {
      console.log("AddGeoPath流场", status)
    })

  },
  removeDigitalFlood(jsonData) {
    window.cloudRender.SuperAPI("RemoveCovering", jsonData, (status) => {
      console.log(status); //成功、失败回调
    })
  },
  //天气
  switchWeather(weather) {
    window.cloudRender.SuperAPI("SetEnvWeather",
      {env_weather: weather},
      (statue) => {
        console.log("SetEnvWeather", weather, statue);
      }
    );
  },
  //特定的闸门抬升
  DamState: (jsonData) => {
    // let jsondata =
    // {
    //     "DamName": "ZM01",    //闸门名称  ZM01,ZM02,ZM03,All
    //     "IsFocus": "true",        //是否聚焦  true:聚焦   false:不聚焦
    //     "State": "true"            //闸门是否开启   true:开启 false:关闭
    // }
    window.cloudRender.SuperAPI("DamState", jsonData, (status) => {
      console.log('DamState', status) //成功、失败回调
    });
  },
  //显示/隐藏AES实体
  ShowHideAESObject: (jsondata) => {
    cloudRender.SuperAPI('ShowHideAESObject', jsondata).then((_AESObject) => {
      console.log(_AESObject)
    })
  },
  //断面操作（上升）
  duanMianStateUp() {
    let jsondata = {
      id: "path_duanmian",
      advancedSetting: {
        smoothnessOfCorners: "extremelyHigh", //设置路径边角的平滑度(extremelyHigh:极高; high:高; middle:中; low:低;)
      },
      coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
      coord_z_type: 2, //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
      cad_mapkey: "", //CAD基准点Key值, 项目中约定
      type: "solid", //样式类型; 注①
      color: "", //颜色(HEX颜色值, 空值即透明; railway类型无效, 默认黑白色)
      pass_color: "", //覆盖物移动经过路径颜色(HEX颜色值)
      width: 5, //宽度(单位:米, 圆柱直径或方柱边长；当类型为“adaptive_solid”，含义为倍率)
      speedRate: 1,
      points: [
        {
          coord: "116.959930,36.491454", //路径坐标点 lng,lat
          coord_z: 69.9199981689453, //高度(单位:米)
        },
        {
          coord: "116.959930,36.491454", //路径坐标点 lng,lat
          coord_z: 209.25,
        },
      ],
    };
    window.cloudRender.SuperAPI("AddPath", jsondata).then((_back) => {
      console.log("断面路径" + _back);
      let jsondata = {
        attach_id: "-9108811597299511653", //要移动的覆盖物id
        attach_type: "aes_object", //要移动的覆盖物类型 见下表
        be_attach_id: "path_duanmian", //依附的覆盖物id
        be_attach_type: "path", //依附的覆盖物类型 见下表
        speed: 100, //移动速度 (单位:米/秒)
        loop: false, //是否循环
        reverse: false, //是否反向移动
        current_attitude: true, //元素姿态(true: 前一帧的姿态; 注: true优先, "pitch", "yaw", "roll" 无效)
      };
      window.cloudRender.SuperAPI("CoverToMove", jsondata).then((_back) => {
        console.log("断面提高" + _back);
      });
      let jsonPipe = {
        attach_id: "-9111626347064181833", //要移动的覆盖物id
        attach_type: "aes_object", //要移动的覆盖物类型 见下表
        be_attach_id: "path_duanmian", //依附的覆盖物id
        be_attach_type: "path", //依附的覆盖物类型 见下表
        speed: 100, //移动速度 (单位:米/秒)
        loop: false, //是否循环
        reverse: false, //是否反向移动
        current_attitude: true, //元素姿态(true: 前一帧的姿态; 注: true优先, "pitch", "yaw", "roll" 无效)
      };
      window.cloudRender.SuperAPI("CoverToMove", jsonPipe).then((_back) => {
        console.log("断面提高" + _back);
      });
      // let piedata = {
      //     "eid": ["-9111626347064181833"],            //覆盖物id
      //     "bshow": false              //true:显示; false:隐藏
      // }
      // window.cloudRender.SuperAPI('ShowHideAESObject', piedata).then((_back) => {
      //     console.log(_back)
      // })
    });

    setTimeout(() => {
      //场景镜头视界行为
      let jsondataCamera = {
        coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
        cad_mapkey: "", //CAD基准点Key值, 项目中约定
        coord_z: "177.539993", //海拔高度(单位:米)
        center_coord: "116.959216,36.490726", //中心点的坐标 lng,lat
        arm_distance: 199.515228, //镜头距中心点距离(单位:米)
        pitch: 9.694946, //镜头俯仰角(5~89)
        yaw: 220.0, //镜头偏航角(0正北, 0~359)
        fly: false, //true: 飞行动画(有一个短暂飞行动画,并按照arm_distance,pitch,yaw设置镜头);
        //false: 立刻跳转过去(瞬移)
      };
      window.cloudRender
        .SuperAPI("SetCameraInfo", jsondataCamera)
        .then((_back) => {
          console.log("场景镜头视界行为", _back);
        });
    }, 500);
  },
  //（下降）
  duanMianStateDown() {
    //场景镜头视界行为
    let jsondataCamera = {
      coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
      cad_mapkey: "", //CAD基准点Key值, 项目中约定
      coord_z: "177.539993", //海拔高度(单位:米)
      center_coord: "116.958230,36.489929", //中心点的坐标 lng,lat
      arm_distance: 627.202576, //镜头距中心点距离(单位:米)
      pitch: 17.605042, //镜头俯仰角(5~89)
      yaw: 135.0, //镜头偏航角(0正北, 0~359)
      fly: false, //true: 飞行动画(有一个短暂飞行动画,并按照arm_distance,pitch,yaw设置镜头);
      //false: 立刻跳转过去(瞬移)
    };

    window.cloudRender
      .SuperAPI("SetCameraInfo", jsondataCamera)
      .then((_back) => {
        console.log("场景镜头视界行为", _back);
      });
    let jsondata = {
      id: "path_duanmian",
      coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
      coord_z_type: 2, //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
      cad_mapkey: "", //CAD基准点Key值, 项目中约定
      is_append: false, //true: 追加路径数据(注意顺序); false: 重建路径数据
      points: [
        {
          coord: "116.959930,36.491454", //路径坐标点 lng,lat
          coord_z: 209.25,
        },
        {
          coord: "116.959930,36.491454", //路径坐标点 lng,lat
          coord_z: 39.25,
        },
      ],
    };
    window.cloudRender.SuperAPI("UpdatePathCoord", jsondata).then((_back) => {
      console.log("更新断面路径" + _back);
    });
    let jsondata1 = {
      attach_id: "-9108811597299511653", //要移动的覆盖物id
      attach_type: "aes_object", //要移动的覆盖物类型 见下表
      be_attach_id: "path_duanmian", //依附的覆盖物id
      be_attach_type: "path", //依附的覆盖物类型 见下表
      speed: 100, //移动速度 (单位:米/秒)
      loop: false, //是否循环
      reverse: false, //是否反向移动
      current_attitude: true, //元素姿态(true: 前一帧的姿态; 注: true优先, "pitch", "yaw", "roll" 无效)
    };
    window.cloudRender.SuperAPI("CoverToMove", jsondata1).then((_back) => {
      console.log("断面下降" + _back);
    });
    let jsonPipe = {
      attach_id: "-9111626347064181833", //要移动的覆盖物id
      attach_type: "aes_object", //要移动的覆盖物类型 见下表
      be_attach_id: "path_duanmian", //依附的覆盖物id
      be_attach_type: "path", //依附的覆盖物类型 见下表
      speed: 100, //移动速度 (单位:米/秒)
      loop: false, //是否循环
      reverse: false, //是否反向移动
      current_attitude: true, //元素姿态(true: 前一帧的姿态; 注: true优先, "pitch", "yaw", "roll" 无效)
    };
    window.cloudRender.SuperAPI("CoverToMove", jsonPipe).then((_back) => {
      console.log("断面提高" + _back);
    });
    // let piedata = {
    //     "eid": ["-9111626347064181833"],            //覆盖物id
    //     "bshow": false              //true:显示; false:隐藏
    // }
    // window.cloudRender.SuperAPI('ShowHideAESObject', piedata).then((_back) => {
    //     console.log(_back)
    // })
    setTimeout(() => {
      this.deletePoi(["path_duanmian"], "path");
    }, 2000);
  },
  //管网
  PipeState: (jsonData) => {
    // let jsondata =
    // {
    //     "Height": "2000",      //管网整体抬升高度  单位：cm
    //     "EffectState": "Highlight",     //管网高亮状态    Highlight:高亮    Normal:不高亮
    //     "VisibleState": "Show"        //管网显示状态   Show:显示  Hide:隐藏
    // }
    window.cloudRender.SuperAPI("PipeState", jsonData, (status) => {
      console.log('PipeState', status) //成功、失败回调
    });
  },
  pipeOperator(pipeFlood) {
    let jsondata
    let jsonPipe
    let zfdata
    if (pipeFlood) { //显示
      switch (pipeFlood) {
        case "NEW_NYGS":
          jsondata = {
            "Height": "10000", //管网整体抬升高度 单位：cm
            "EffectState": "Highlight", //管网高亮状态 Highlight:高亮 Normal:不高亮
            "HighlightParts": [pipeFlood], //显示流向管网名称,为空的时候不显示流向
            "VisibleState": "Show" //管网显示状态 Show:显示 Hide:隐藏
          }
          zfdata = {
            "IDs": ["ZF01", "ZF02", "ZF03", "ZF04", "ZF05", "ZF06", "ZF07", "ZF08"], //闸阀ID
            "HighlightState": "Blue", //设备高亮颜色 Red，Green，Blue
            "LabelState": "Hide", //标签颜色 Red,Blue,Green Hide：隐藏标签
            "LabelText": "开" //标签文字
          }
          window.cloudRender.SuperAPI("GateValveHighlightState", zfdata)

          zfdata = {
            "IDs": ["ZF03", "ZF02", "ZF01"], //闸阀ID
            "HighlightState": "Blue", //设备高亮颜色 Red，Green，Blue
            "LabelState": "Blue", //标签颜色 Red,Blue,Green Hide：隐藏标签
            "LabelText": "开" //标签文字
          }
          window.cloudRender.SuperAPI("GateValveHighlightState", zfdata)
          zfdata = {
            "IDs": ["ZF04", "ZF05", "ZF06", "ZF07", "ZF08"], //闸阀ID
            "HighlightState": "Blue", //设备高亮颜色 Red，Green，Blue
            "LabelState": "Blue", //标签颜色 Red,Blue,Green Hide：隐藏标签
            "LabelText": "关" //标签文字
          }
          window.cloudRender.SuperAPI("GateValveHighlightState", zfdata)
          break;
        case "NEW_CSGS":
          jsondata = {
            "Height": "10000", //管网整体抬升高度 单位：cm
            "EffectState": "Highlight", //管网高亮状态 Highlight:高亮 Normal:不高亮
            "HighlightParts": [pipeFlood], //显示流向管网名称,为空的时候不显示流向
            "VisibleState": "Show" //管网显示状态 Show:显示 Hide:隐藏
          }
          zfdata = {
            "IDs": ["ZF01", "ZF02", "ZF03", "ZF04", "ZF05", "ZF06", "ZF07", "ZF08"], //闸阀ID
            "HighlightState": "Blue", //设备高亮颜色 Red，Green，Blue
            "LabelState": "Hide", //标签颜色 Red,Blue,Green Hide：隐藏标签
            "LabelText": "开" //标签文字
          }
          window.cloudRender.SuperAPI("GateValveHighlightState", zfdata)
          zfdata = {
            "IDs": ["ZF03", "ZF04"], //闸阀ID
            "HighlightState": "Blue", //设备高亮颜色 Red，Green，Blue
            "LabelState": "Blue", //标签颜色 Red,Blue,Green Hide：隐藏标签
            "LabelText": "开" //标签文字
          }
          window.cloudRender.SuperAPI("GateValveHighlightState", zfdata)
          zfdata = {
            "IDs": ["ZF01", "ZF02", "ZF05", "ZF06", "ZF07", "ZF08"], //闸阀ID
            "HighlightState": "Blue", //设备高亮颜色 Red，Green，Blue
            "LabelState": "Blue", //标签颜色 Red,Blue,Green Hide：隐藏标签
            "LabelText": "关" //标签文字
          }
          window.cloudRender.SuperAPI("GateValveHighlightState", zfdata)
          break;
        case "NEW_HS":
          jsondata = {
            "Height": "10000", //管网整体抬升高度 单位：cm
            "EffectState": "Highlight", //管网高亮状态 Highlight:高亮 Normal:不高亮
            "HighlightParts": [pipeFlood], //显示流向管网名称,为空的时候不显示流向
            "VisibleState": "Show" //管网显示状态 Show:显示 Hide:隐藏
          }
          zfdata = {
            "IDs": ["ZF01", "ZF02", "ZF03", "ZF04", "ZF05", "ZF06", "ZF07", "ZF08"], //闸阀ID
            "HighlightState": "Blue", //设备高亮颜色 Red，Green，Blue
            "LabelState": "Hide", //标签颜色 Red,Blue,Green Hide：隐藏标签
            "LabelText": "开" //标签文字
          }
          window.cloudRender.SuperAPI("GateValveHighlightState", zfdata)
          zfdata = {
            "IDs": ["ZF03", "ZF04"], //闸阀ID
            "HighlightState": "Blue", //设备高亮颜色 Red，Green，Blue
            "LabelState": "Blue", //标签颜色 Red,Blue,Green Hide：隐藏标签
            "LabelText": "开" //标签文字
          }
          window.cloudRender.SuperAPI("GateValveHighlightState", zfdata)
          zfdata = {
            "IDs": ["ZF01", "ZF02", "ZF05", "ZF06", "ZF07", "ZF08"], //闸阀ID
            "HighlightState": "Blue", //设备高亮颜色 Red，Green，Blue
            "LabelState": "Blue", //标签颜色 Red,Blue,Green Hide：隐藏标签
            "LabelText": "关" //标签文字
          }
          window.cloudRender.SuperAPI("GateValveHighlightState", zfdata)
          break;
        case "OLD_NYGS": //旧管道农业供水
          jsondata = {
            "Height": "10000", //管网整体抬升高度 单位：cm
            "EffectState": "Highlight", //管网高亮状态 Highlight:高亮 Normal:不高亮
            "HighlightParts": [pipeFlood], //显示流向管网名称,为空的时候不显示流向
            "VisibleState": "Show" //管网显示状态 Show:显示 Hide:隐藏
          }
          zfdata = {
            "IDs": ["ZF01", "ZF02", "ZF03", "ZF04", "ZF05", "ZF06", "ZF07", "ZF08"], //闸阀ID
            "HighlightState": "Blue", //设备高亮颜色 Red，Green，Blue
            "LabelState": "Hide", //标签颜色 Red,Blue,Green Hide：隐藏标签
            "LabelText": "开" //标签文字
          }
          window.cloudRender.SuperAPI("GateValveHighlightState", zfdata)

          zfdata = {
            "IDs": ["ZF02", "ZF01", "ZF08", "ZF07", "ZF06"], //闸阀ID
            "HighlightState": "Blue", //设备高亮颜色 Red，Green，Blue
            "LabelState": "Blue", //标签颜色 Red,Blue,Green Hide：隐藏标签
            "LabelText": "开" //标签文字
          }
          window.cloudRender.SuperAPI("GateValveHighlightState", zfdata)
          zfdata = {
            "IDs": ["ZF03", "ZF04"], //闸阀ID
            "HighlightState": "Blue", //设备高亮颜色 Red，Green，Blue
            "LabelState": "Blue", //标签颜色 Red,Blue,Green Hide：隐藏标签
            "LabelText": "关" //标签文字
          }
          window.cloudRender.SuperAPI("GateValveHighlightState", zfdata)
          break;
        case "OLD_CSGS": //旧管道城市供水
          jsondata = {
            "Height": "10000", //管网整体抬升高度 单位：cm
            "EffectState": "Highlight", //管网高亮状态 Highlight:高亮 Normal:不高亮
            "HighlightParts": [pipeFlood], //显示流向管网名称,为空的时候不显示流向
            "VisibleState": "Show" //管网显示状态 Show:显示 Hide:隐藏
          }
          zfdata = {
            "IDs": ["ZF01", "ZF02", "ZF03", "ZF04", "ZF05", "ZF06", "ZF07", "ZF08"], //闸阀ID
            "HighlightState": "Blue", //设备高亮颜色 Red，Green，Blue
            "LabelState": "Hide", //标签颜色 Red,Blue,Green Hide：隐藏标签
            "LabelText": "开" //标签文字
          }
          window.cloudRender.SuperAPI("GateValveHighlightState", zfdata)
          zfdata = {
            "IDs": ["ZF08", "ZF07", "ZF06"], //闸阀ID
            "HighlightState": "Blue", //设备高亮颜色 Red，Green，Blue
            "LabelState": "Blue", //标签颜色 Red,Blue,Green Hide：隐藏标签
            "LabelText": "开" //标签文字
          }
          window.cloudRender.SuperAPI("GateValveHighlightState", zfdata)
          zfdata = {
            "IDs": ["ZF01", "ZF02", "ZF03", "ZF04", "ZF05",], //闸阀ID
            "HighlightState": "Blue", //设备高亮颜色 Red，Green，Blue
            "LabelState": "Blue", //标签颜色 Red,Blue,Green Hide：隐藏标签
            "LabelText": "关" //标签文字
          }
          window.cloudRender.SuperAPI("GateValveHighlightState", zfdata)
          break;
        case "OLD_HS": //旧管道回水
          jsondata = {
            "Height": "10000", //管网整体抬升高度 单位：cm
            "EffectState": "Highlight", //管网高亮状态 Highlight:高亮 Normal:不高亮
            "HighlightParts": [pipeFlood], //显示流向管网名称,为空的时候不显示流向
            "VisibleState": "Show" //管网显示状态 Show:显示 Hide:隐藏
          }
          zfdata = {
            "IDs": ["ZF08", "ZF07", "ZF06"], //闸阀ID
            "HighlightState": "Blue", //设备高亮颜色 Red，Green，Blue
            "LabelState": "Blue", //标签颜色 Red,Blue,Green Hide：隐藏标签
            "LabelText": "开" //标签文字
          }
          window.cloudRender.SuperAPI("GateValveHighlightState", zfdata)
          zfdata = {
            "IDs": ["ZF01", "ZF02", "ZF03", "ZF04", "ZF05",], //闸阀ID
            "HighlightState": "Blue", //设备高亮颜色 Red，Green，Blue
            "LabelState": "Blue", //标签颜色 Red,Blue,Green Hide：隐藏标签
            "LabelText": "关" //标签文字
          }
          window.cloudRender.SuperAPI("GateValveHighlightState", zfdata)
          break;
        case "Show": //显示
          jsondata = {
            "Height": "10000", //管网整体抬升高度 单位：cm
            "EffectState": "Highlight", //管网高亮状态 Highlight:高亮 Normal:不高亮
            "HighlightParts": [], //显示流向管网名称,为空的时候不显示流向
            "VisibleState": pipeFlood //管网显示状态 Show:显示 Hide:隐藏
          }
          zfdata = {
            "IDs": ["ZF01", "ZF02", "ZF03", "ZF04", "ZF05", "ZF06", "ZF07", "ZF08"], //闸阀ID
            "HighlightState": "Blue", //设备高亮颜色 Red，Green，Blue
            "LabelState": "Hide", //标签颜色 Red,Blue,Green Hide：隐藏标签
            "LabelText": "开" //标签文字
          }
          window.cloudRender.SuperAPI("GateValveHighlightState", zfdata)
          break;
        case "Hide": //隐藏
          jsondata = {
            "Height": "10000", //管网整体抬升高度 单位：cm
            "EffectState": "Highlight", //管网高亮状态 Highlight:高亮 Normal:不高亮
            "HighlightParts": [], //显示流向管网名称,为空的时候不显示流向
            "VisibleState": pipeFlood //管网显示状态 Show:显示 Hide:隐藏
          }
          zfdata = {
            "IDs": ["ZF01", "ZF02", "ZF03", "ZF04", "ZF05", "ZF06", "ZF07", "ZF08"], //闸阀ID
            "HighlightState": "Blue", //设备高亮颜色 Red，Green，Blue
            "LabelState": "Hide", //标签颜色 Red,Blue,Green Hide：隐藏标签
            "LabelText": "开" //标签文字
          }
          window.cloudRender.SuperAPI("GateValveHighlightState", zfdata)
          break;
      }
    } else { //隐藏
      jsondata = {
        "Height": "10000", //管网整体抬升高度 单位：cm
        "EffectState": "Highlight", //管网高亮状态 Highlight:高亮 Normal:不高亮
        "HighlightParts": [], //显示流向管网名称,为空的时候不显示流向
        "VisibleState": "Show" //管网显示状态 Show:显示 Hide:隐藏
      }
      zfdata = {
        "IDs": ["ZF01", "ZF02", "ZF03", "ZF04", "ZF05", "ZF06", "ZF07", "ZF08"], //闸阀ID
        "HighlightState": "Blue", //设备高亮颜色 Red，Green，Blue
        "LabelState": "Hide", //标签颜色 Red,Blue,Green Hide：隐藏标签
        "LabelText": "关" //标签文字
      }
      window.cloudRender.SuperAPI("GateValveHighlightState", zfdata)
    }
    window.cloudRender.SuperAPI("PipeState", jsondata, (status) => {
      console.log('PipeState', status) //成功、失败回调
    });
  },
  // 添加poi点
  AddPOI: (jsonData) => {
    let jo = {
      id: jsonData.id,
      label: jsonData.label || '',                   //POI title文本
      coord_type: jsonData.coord_type || 0,          //坐标类型(0:经纬度坐标, 1:cad坐标)
      cad_mapkey: jsonData.cad_mapkey || '',         //CAD基准点Key值, 项目中约定
      coord: jsonData.coord,                         //POI点的坐标 lng,lat
      coord_z: jsonData.coord_z || 0,                //高度(单位:米)
      coord_z_type: jsonData.coord_z_type || 0,      //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
      always_show_label: jsonData.always_show_label != null ? jsonData.always_show_label : true,    //是否永远显示title, true:显示title(默认), false:不显示title
      show_label_range: jsonData.show_label_range || '0,2000',                                  //POI点显示label的范围(单位:米, 范围最小、最大距离; 在此范围内显示, 超出范围隐藏; 注意:always_show_label属性优先于此属性)
      umg_type: jsonData.umg_type || 'default',                                                 //此POI所使用的UI模板类型(default: 带线的默认POI UI样式, default_noline:不带线的POI UI样式, 项目中约定)
      sort_order: jsonData.sort_order != null ? jsonData.sort_order : true,                         //是否自动遮挡排序
      params: jsonData.params || null,
    }
    if (jsonData.animation_type) {
      //this.$set(jo, 'animation_type', jsonData.animation_type)
      jo.animation_type = jsonData.animation_type
    }
    if (jsonData.duration_time) {
      //this.$set(jo, 'duration_time', jsonData.duration_time)
      jo.duration_time = jsonData.duration_time
    }
    window.cloudRender.SuperAPI('AddPOI', jo, (status) => {
      console.log('AddPOI', status) //成功、失败回调
    })
  },
  // 更新poi点数据
  UpdatePOICoord: (jsonData) => {
    let jo = {
      id: jsonData.id,
      coord_type: jsonData.coord_type || 0,       //坐标类型(0:经纬度坐标, 1:cad坐标)
      cad_mapkey: jsonData.cad_mapkey || '',      //CAD基准点Key值, 项目中约定
      coord: jsonData.coord,                      //POI点的坐标 lng,lat
      coord_z: jsonData.coord_z || 0,             //高度(单位:米)
      coord_z_type: jsonData.coord_z_type || 0   //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
    }
    window.cloudRender.SuperAPI('UpdatePOICoord', jo, (status) => {
      console.log('UpdatePOICoord', status) //成功、失败回调
    })
  },
  // 更新poi点样式
  UpdatePOIStyle: (jsonData) => {
    let jo = {
      id: jsonData.id,
      label: jsonData.label || '',                                 //POI title文本
      always_show_label: jsonData.always_show_label != null ? jsonData.always_show_label : true,   //是否永远显示title, true:显示title(默认), false:不显示title
      show_label_range: jsonData.show_label_range || '0,2000',                                 //POI点显示label的范围(单位:米, 范围最小、最大距离; 在此范围内显示, 超出范围隐藏; 注意:always_show_label属性优先于此属性)
      umg_type: jsonData.umg_type || 'default',                                                //此POI所使用的UI模板类型(default: 带线的默认POI UI样式, default_noline:不带线的POI UI样式, 项目中约定)
      sort_order: jsonData.sort_order != null ? jsonData.sort_order : true                        //是否自动遮挡排序
    }
    if (jsonData.animation_type) {
      //this.$set(jo, 'animation_type', jsonData.animation_type)
      jo.animation_type = jsonData.animation_type
    }
    if (jsonData.duration_time) {
      //this.$set(jo, 'duration_time', jsonData.duration_time)
      jo.duration_time = jsonData.duration_time
    }
    window.cloudRender.SuperAPI('UpdatePOIStyle', jo, (status) => {
      console.log('UpdatePOIStyle', status) //成功、失败回调
    })
  },
  // 设置poi点状态
  SetPOISelect: (jsonData) => {
    let jo = {
      id: jsonData.id,
      select: jsonData.select
    }
    window.cloudRender.SuperAPI('SetPOISelect', jo, (status) => {
      console.log('SetPOISelect', status) //成功、失败回调
    })
  },
  // 添加自定义poi点
  AddCustomPOI: (jsonData) => {
    if (jsonData instanceof Array && jsonData.length > 0) {
      // let joList = []
      jsonData.forEach(item => {
        // joList.push(handleCustomPOIData(item))
        let jo = handleCustomPOIData(item)
        window.cloudRender.SuperAPI('AddCustomPOI', jo, (status) => {
        })
      })
      // window.cloudRender.SuperAPI('AddCustomPOI', joList, (status) => {
      //     console.log('AddCustomPOI arr', status) //成功、失败回调
      // })
    } else if (typeof jsonData == 'object') {
      let jo = handleCustomPOIData(jsonData)
      window.cloudRender.SuperAPI('AddCustomPOI', jo, (status) => {
      })
    }
  },
  // 更新自定义poi点数据
  UpdateCustomPOICoord: (jsonData) => {
    let jo = {
      id: jsonData.id,
      coord_type: jsonData.coord_type || 0,       //坐标类型(0:经纬度坐标, 1:cad坐标)
      cad_mapkey: jsonData.cad_mapkey || '',      //CAD基准点Key值, 项目中约定
      coord: jsonData.coord,                      //POI点的坐标 lng,lat
      coord_z: jsonData.coord_z || 0,             //高度(单位:米)
      coord_z_type: jsonData.coord_z_type || 0   //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
    }
    window.cloudRender.SuperAPI('UpdateCustomPOICoord', jo, (status) => {
      console.log('UpdateCustomPOICoord', status) //成功、失败回调
    })
  },
  // 更新自定义poi点样式
  UpdateCustomPOIStyle: (jsonData) => {
    let jo = {
      id: jsonData.id,
      always_show_label: jsonData.always_show_label != null ? jsonData.always_show_label : true,    //是否永远显示title, true:显示title(默认), false:不显示title
      show_label_range: jsonData.show_label_range || '0,2000',                                  //POI点显示label的范围(单位:米, 范围最小、最大距离; 在此范围内显示, 超出范围隐藏; 注意:always_show_label属性优先于此属性)
      sort_order: jsonData.sort_order != null ? jsonData.sort_order : false,                        //是否自动遮挡排序
      state: jsonData.state                                                       //与marker之中images中的define_state对应
    }
    if (jsonData.animation_type) {
      //this.$set(jo, 'animation_type', jsonData.animation_type)
      jo.animation_type = jsonData.animation_type
    }
    if (jsonData.duration_time) {
      //this.$set(jo, 'duration_time', jsonData.duration_time)
      jo.duration_time = jsonData.duration_time
    }
    window.cloudRender.SuperAPI('UpdateCustomPOIStyle', jo, (status) => {
      console.log('UpdateCustomPOIStyle', status) //成功、失败回调
    })
  },
  // 更新自定义poi点marker
  UpdateCustomPOIMarker: (jsonData) => {
    let jo = {
      id: jsonData.id,
      marker: jsonData.marker
    }
    window.cloudRender.SuperAPI('UpdateCustomPOIMarker', jo, (status) => {
      console.log('UpdateCustomPOIMarker', status) //成功、失败回调
    })
  },
  // 更新自定义poi点label
  UpdateCustomPOILabel: (jsonData) => {
    let jo = {
      id: jsonData.id,
      label: jsonData.label
    }
    window.cloudRender.SuperAPI('UpdateCustomPOILabel', jo, (status) => {
      console.log('UpdateCustomPOILabel', status) //成功、失败回调
    })
  },
  // 更新自定义poi点window
  UpdateCustomPOIWindow: (jsonData) => {
    let jo = {
      id: jsonData.id,
      window: jsonData.window
    }
    window.cloudRender.SuperAPI('UpdateCustomPOIWindow', jo, (status) => {
      console.log('UpdateCustomPOIWindow', status) //成功、失败回调
    })
  },
  // 设置当前场景镜头视界
  SetCameraInfo: (jsonData) => {
    let jo = {
      coord_type: jsonData.coord_type || 0,          //坐标类型(0:经纬度坐标, 1:cad坐标)
      cad_mapkey: jsonData.cad_mapkey || "",         //CAD基准点Key值, 项目中约定
      center_coord: jsonData.center_coord,          //中心点的坐标 lng,lat
      arm_distance: jsonData.arm_distance || 100,   //镜头距中心点距离(单位:米)
      pitch: jsonData.pitch || 30,                   //镜头俯仰角(5~89)
      yaw: jsonData.yaw || 0,                        //镜头偏航角(0正北, 0~359)
      fly: jsonData.fly != null ? jsonData.fly : true   //true: 飞行动画(有一个短暂飞行动画,并按照arm_distance,pitch,yaw设置镜头);
      //false: 立刻跳转过去(瞬移)
    }
    if (jsonData.coord_z) {
      //this.$set(jo, 'coord_z', jsonData.coord_z)    //海拔高度(单位:米)
      jo.coord_z = jsonData.coord_z
    }
    window.cloudRender.SuperAPI("SetCameraInfo", jo, (status) => {
      console.log(status); //成功、失败回调
    })
  },
  // 镜头漫游
  SetCameraRoamingPro: (jsonData) => {
    let jo = {
      coord_type: jsonData.coord_type || 0,            //坐标类型(0:经纬度坐标, 1:cad坐标)
      cad_mapkey: jsonData.cad_mapkey || "",           //CAD基准点Key值, 项目中约定
      coord_z_type: jsonData.coord_z_type || 0,        //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
      subsidiary_show: jsonData.subsidiary_show != null ? jsonData.subsidiary_show : true,     //是否显示辅助线(true:显示; false:不显示)
      points: [
        // {

        // }
      ]
    }
    let points = jsonData.points
    console.log('jsonData.points', points)
    if (points && points.length > 0) {
      points.forEach(item => {
        console.log('item', item)
        let pointJo = {
          coord: item.coord,                                      //路径坐标点 lng,lat
          coord_z: item.coord_z || 5,                           //高度(单位:米, cad坐标无效)
          coord_easetype: item.coord_easetype || "linear",        //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
          arm_distance: item.arm_distance || 30,                 //镜头与坐标点距离(单位:米)
          pitch: item.pitch || 30,                                //镜头俯仰角(0~89)
          yaw: item.yaw || 65,                                    //镜头偏航角(0正北, 0~359)
          attitude_easetype: item.attitude_easetype || "Linear",  //镜头漫游至下一坐标点缓动姿态类型(见下表)
          time: item.time || 5,                                   //镜头漫游至下一坐标点所花费的时间(单位:秒)
          speed_easetype: item.speed_easetype || "linear"         //镜头漫游速度类型(见下表)
        }
        jo.points.push(pointJo)
      })
      console.log('points', jo.points)
    }
    window.cloudRender.SuperAPI("SetCameraRoamingPro", jo, (status) => {
      console.log('SetCameraRoamingPro', status); //成功、失败回调
    })
  },
  // 设置镜头漫游状态
  SetCameraRoamingProState: (state) => {
    let jo = {
      state: state
    }
    window.cloudRender.SuperAPI("SetCameraRoamingProState", jo, (status) => {
      console.log(status); //成功、失败回调
    })
  },
  // 显示\隐藏覆盖物
  ShowHideCovering: (jsonData) => {
    window.cloudRender.SuperAPI("ShowHideCovering", jsonData, (status) => {
      console.log('ShowHideCovering', status, jsonData); //成功、失败回调
    })
  },
  // 覆盖物移动
  CoverToMove: (jsonData) => {
    let jo = {
      "attach_id": jsonData.attach_id,     //要移动的覆盖物id
      "attach_type": jsonData.attach_type, //要移动的覆盖物类型 见下表
      "be_attach_id": jsonData.be_attach_id,      //依附的覆盖物id
      "be_attach_type": jsonData.be_attach_type,  //依附的覆盖物类型 见下表
      "speed": jsonData.speed,                     //移动速度 (单位:米/秒)
      "loop": jsonData.loop != null ? jsonData.loop : false,           //是否循环
      "reverse": jsonData.reverse != null ? jsonData.reverse : false,  //是否反向移动
      "current_attitude": jsonData.current_attitude != null ? jsonData.current_attitude : true,       //元素姿态(true: 前一帧的姿态; 注: true优先, "pitch", "yaw", "roll" 无效)
      "pitch": jsonData.pitch,                    //俯仰角, 参考值(-90~90)
      "yaw": jsonData.yaw,                       //偏航角, 参考值(0~360)
      "roll": jsonData.roll,                       //翻滚角, 参考值(0~360)
    }
    window.cloudRender.SuperAPI("CoverToMove", jo, (status) => {
      console.log('CoverToMove', status); //成功、失败回调
    })
  },
  // 聚焦至AES实体
  FocusAESObject: (jsonData) => {
    let jo = {
      "eid": jsonData.eid,
      "distance": jsonData.distance || 1000       //距离(单位:米; 多个EID自动计算)
    }
    window.cloudRender.SuperAPI("FocusAESObject", jo, (status) => {
      console.log('FocusAESObject', status); //成功、失败回调
    })
  },
  // 显示\隐藏覆盖物
  ShowHideAllCovering: (jsonData) => {
    window.cloudRender.SuperAPI("ShowHideAllCovering", jsonData, (status) => {
      console.log('ShowHideAllCovering', status); //成功、失败回调
    })
  },
  coordHandle(waterCoord) {
    for (let i = 0; i < waterCoord.length; i++) {
      if (waterCoord[i]["index"]) {
        delete waterCoord[i]["index"]
      }
    }
    return waterCoord

  },
  addPipePath(jsonData) {
    const pipeOldGuanGai = {
      id: jsonData.id,
      advancedSetting: {
        smoothnessOfCorners: "extremelyHigh", //设置路径边角的平滑度(extremelyHigh:极高; high:高; middle:中; low:低;)
      },
      coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
      coord_z_type: 2, //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
      cad_mapkey: "", //CAD基准点Key值, 项目中约定
      type: "arrow", //样式类型; 注①
      color: "ff0000", //颜色(HEX颜色值, 空值即透明; railway类型无效, 默认黑白色)
      pass_color: "ffff00", //覆盖物移动经过路径颜色(HEX颜色值)
      width: 2, //宽度(单位:米, 圆柱直径或方柱边长；当类型为“adaptive_solid”，含义为倍率)
      points: this.coordHandle(jsonData.waterCoord),
    };
    window.cloudRender.SuperAPI('AddPath', pipeOldGuanGai).then((_pipepath) => {
      console.log("AddPath", _pipepath)
    })

  },
  //添加闸门路径
  addZhamenPath: (jsondata) => {
    let jo = {
      id: jsondata.id,
      advancedSetting: {
        "smoothnessOfCorners": "high"  //设置路径边角的平滑度(extremelyHigh:极高; high:高; middle:中; low:低;)
      },
      coord_type: jsondata.coord_type || 0,
      coord_z_type: jsondata.coord_z_type || 1,
      cad_mapkey: "",
      type: "none",
      color: jsondata.color || "#ff3456",
      pass_color: jsondata.pass_color || "#ff6597",
      width: jsondata.width || 1,
      points: []
    }
    let points = jsondata.points
    if (points && points.length > 0) {
      points.forEach(item => {
        //console.log('item', item)
        let pointJo = {
          coord: item.coord,                                      //路径坐标点 lng,lat
          coord_z: item.coord_z,                           //高度(单位:米, cad坐标无效)
        }
        jo.points.push(pointJo)
      })
      //console.log('points', jo.points)
    }
    window.cloudRender.SuperAPI('AddPath', jo).then((_zhamenpath) => {
      console.log("AddPath", _zhamenpath)
      return _zhamenpath;
    })
  },
  //面积测量工具
  measureArea: (jsondata) => {
    let jo = {
      coord_type: jsondata.coord_type || 0,            //坐标类型(0:经纬度, 1:cad)
      cad_mapkey: jsondata.cad_mapkey || "default",    //CAD基准点Key值, 项目中约定
      coord_z_type: jsondata.coord_z_type || 0,          //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
      projection: jsondata.projection || true,         //是否开启投影(true:开启; false:不开启)
      accuracy: jsondata.accuracy || 2,              //精度,最大精确4位(0~4)
      surface: jsondata.surface || true,            //是否显示三角剖分面以及面积测量结果(true, false)
      length: jsondata.length || true,             //是否显示边线以及周长测量结果(true, false)
      unit: jsondata.unit || "m2",               //单位(km2:平方千米; m2:平方米; cm2:平方厘米; mm2:平方毫米)
      adsorption_mode: jsondata.adsorption_mode || true     //自动吸附(true/false; 注: "adsorption_mode": true 只对激活后的BIM模型有效)
    }
    window.cloudRender.SuperAPI("AddMeasureArea", jo, (e) => {
      console.log(e);
    })
  },
  // 添加Geo区域轮廓
  AddGeoRange: (jsonData) => {
    let jo = {
      id: jsonData.id,
      coord_type: jsonData.coord_type != null ? jsonData.coord_type : 0,                  //坐标类型(0:经纬度坐标, 1:cad坐标)
      cad_mapkey: jsonData.cad_mapkey || "",                 //CAD基准点Key值, 项目中约定
      coord_z: jsonData.coord_z != null ? jsonData.coord_z : 0,                     //高度(单位:米)
      coord_z_type: jsonData.coord_z_type != null ? jsonData.coord_z_type : 0,                //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
      type: jsonData.type || "wave",                   //样式类型; 注①
      color: jsonData.color || "ff00004c",              //轮廓颜色(HEXA颜色值)
      range_height: jsonData.range_height || 10,               //围栏高度(单位:米)
      stroke_weight: jsonData.stroke_weight || 10,              //底部轮廓线宽度(单位:米; 注: 区域中含有内环"inner_points"时无效)
      fill_area: jsonData.fill_area || "none",              //底部区域填充类型; 注②
      id_field_name: jsonData.id_field_name || "id",            //指定geojson中的id字段
      geojson: jsonData.geojson
    }
    window.cloudRender.SuperAPI("AddGeoRange", jo, (status) => {
      console.log('AddGeoRange', status); //成功、失败回调
    })
  },
  //根据eid获取覆盖物属性
  getCoveringInfo(arr) {//

    let jsondata = {
      "eid": arr || ["-9111626350604376433"]
    }
    window.cloudRender.SuperAPI('GetAESObjectDataWithEids', jsondata, (_back) => {
      let data = JSON.parse(_back)
      console.log("获取实体属性", data);
      return data
    });
  },
  //新增水面的移动路径
  addWaterPath(height, time) {
    let jsondata = {
      "data": [
        {
          "object": [   //当object中包含多个对象时, 他们拥有相同的坐标（经纬高）, 但姿态只对aes_object生效, 目前只支持自定义poi、scene_effect、aes_objct三种类型
            {
              "id": ["-9150751343265701460"],
              "type": "aes_object"
            },
            // {
            //   "id": ["POI_id"],
            //   "type": "poi"
            // }
          ],
          "coord": "113.531614,33.265985", //目的地的坐标, 对象们从他们当前位置向目的地移动(lng,lat)
          "coord_type": 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
          "cad_mapkey": "", //CAD基准点Key值, 通过“注册CAD锚点”API创建；coord_type为0时可为空
          "coord_z": height, //高度(单位:米, cad坐标无效)
          "coord_z_type": 2, //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
          "periodic": time, //周期, 当前状态到该状态所需时间
          "pitch": 0, //俯仰角,姿态数据只对aes_object生效, 当aes_object到达目的地后, 在一个短暂时间内, 旋转到目标姿态
          "yaw": 0, //偏航角
          "default_yaw": false,  //true：根据运动方向自动计算各姿态角的0度方向；false：以固定yaw值运动
          "roll": 0 //滚动角
        }
      ]
    }
    window.cloudRender.SuperAPI("DataDriveObjectToMove", jsondata, (e) => {
      console.log(e);
    })
    // let jsondata = {
    //   "id": id,
    //   "advancedSetting": {
    //     "smoothnessOfCorners": "high"  //设置路径边角的平滑度(extremelyHigh:极高; high:高; middle:中; low:低;)
    //   },
    //   "coord_type": 0,                    //坐标类型(0:经纬度坐标, 1:cad坐标)
    //   "coord_z_type": 2,                  //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
    //   "cad_mapkey": "",                   //CAD基准点Key值, 项目中约定
    //   "type": "solid",                    //样式类型; 注①
    //   "color": "#FFFFFF",                  //颜色(HEX颜色值, 空值即透明; railway类型无效, 默认黑白色)
    //   "pass_color": "ffff00",             //覆盖物移动经过路径颜色(HEX颜色值)
    //   "width": 1,                        //宽度(单位:米, 圆柱直径或方柱边长；当类型为“adaptive_solid”，含义为倍率)
    //   "points": [
    //     {
    //       "coord": lonLat,   //路径坐标点 lng,lat
    //       "coord_z": startheight     //高度(单位:米)
    //     },
    //     {
    //       "coord": lonLat,
    //       "coord_z": endHeight
    //     }

    //   ]
    // }
    // window.cloudRender.SuperAPI("AddPath", jsondata, (status) => {
    //   console.log(status)
    //   if (id == "water1") {
    //     this.waterMoveToUp()
    //   }else{
    //     this.waterMoveToDown()
    //   }
    // })
  },
  //水面上升
  waterMoveToUp() {
    let jsondata = {
      "attach_id": "-9150751343265701460",            //要移动的覆盖物id
      "attach_type": "aes_object",                   //要移动的覆盖物类型 见下表
      "be_attach_id": "water1",              //依附的覆盖物id
      "be_attach_type": "path",               //依附的覆盖物类型 见下表
      "speed": 1,                             //移动速度 (单位:米/秒)
      "loop": false,                           //是否循环
      "reverse": false                        //是否反向移动
    }
    window.cloudRender.SuperAPI("CoverToMove", jsondata, (status) => {
      console.log(1111111111111111111111111111)
      console.log(status); //成功、失败回调
    })
  },
  //水面下降
  waterMoveToDown() {
    let jsondata = {
      "attach_id": "-9150751343265701460",            //要移动的覆盖物id
      "attach_type": "aes_object",                   //要移动的覆盖物类型 见下表
      "be_attach_id": "water2",              //依附的覆盖物id
      "be_attach_type": "path",               //依附的覆盖物类型 见下表
      "speed": 1,                             //移动速度 (单位:米/秒)
      "loop": false,                           //是否循环
      "reverse": false                        //是否反向移动
    }
    window.cloudRender.SuperAPI("CoverToMove", jsondata, (status) => {
      console.log(1111111111111111111111111111)
      console.log(status); //成功、失败回调
    })
  },
  updeteMeshInfo() {
    let jsondata = {
      "eid": "-9214927692375098736",
      "coord type": 0,
      "cad_mapkey": '',
      "scale": 1,
      "coord": "117.971089,35.440644",
      "coord_z_type": 2,
      "coord_z": -1827,
      "pitch": 0,
      "roll": 0,
      "yaw": 0
    }
    cloudRender.SuperAPI("updateAESObjectTransform", jsondata, (status) => {
      console.log(1111111111111111111111111111)
      console.log(status); //成功、失败回调
    })
  },

  // 测试两点距离
  testPoint: (jsondata) => {
    let jo = {
      coord_type: jsondata.coord_type || 0,      //坐标类型(0:经纬度坐标, 1:cad坐标)
      cad_mapkey: "",     //CAD基准点Key值, 项目中约定
      point1: jsondata.point1 || "113.33326,23.129248",
      point2: jsondata.point2 || "113.325981,23.129707"
    };
    window.cloudRender.SuperAPI('GEOMPointsRelationship', jo).then((_pointdistance) => {
      console.log("两点距离：" + _pointdistance)
    })
  },
  // 添加场景特效
  AddEffect(jsondata) {
    window.cloudRender.SuperAPI("AddEffect", jsondata, (status) => {
      console.log(status); //成功、失败回调
    })
  },
  //地图自定义poi点
  addSelfPoiPoint2(data) {
    let jsondata = ''
    if (data.direction == 'left') {
      data.bg_offset = "-100,0"

    } else {
      data.bg_offset = "0,0"
    }
    jsondata = {
      "id": "customPOI_id-" + data.id,
      "coord_type": 0,                         //坐标类型(0:经纬度坐标, 1:cad坐标)
      "cad_mapkey": "",                        //CAD基准点的Key值, 项目中约定
      "coord": data.lonLat,         //POI点的坐标 lng,lat
      "coord_z": data.coord_z || 2,                            //高度(单位:米)
      "coord_z_type": 0,                       //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
      "always_show_label": false,               //是否永远显示label, true:显示label(默认), false:不显示label
      "show_label_range": "0,30",            //POI点显示label的范围(单位:米, 范围最小、最大距离; 在此范围内显示, 超出范围隐藏; 注意:always_show_label属性优先于此属性)
      "sort_order": false,                     //是否开启遮挡排序(根据POI点真实3D位置开启遮挡排序,
      //注: 只与其他开启排序的POI之间进行排序, 开启此排序会消耗性能(最多60个POI点同时开启排序)
      "animation_type": "bounce",              //动画类型(bounce:弹出式; stretch:伸缩式; wipe:展开式)
      "duration_time": 0.7,                    //规定完成动画所花费的时间(单位:秒)
      "state": "state_1",                      //与marker之中images中的define_state对应
      "marker": {
        "size": "1,1",                   //marker大小(宽,高 单位:像素)
        "images": [
          {
            "define_state": "state_1",   //marker图片组
            "normal_url": "http://superapi.51hitech.com/doc-static/images/static/markerNormal.png",        //normal 图片url地址
            "activate_url": "http://superapi.51hitech.com/doc-static/images/static/markerActive.png"       //hover, active 图片url地址
            //本地图片地址一: "file:///D:/xxx/markerNormal.png",    D: 在线席位所在盘符
            //本地图片地址二: "path:/UserData/markerNormal.png",    图片资源由在线席位后台管理, 在线席位4.3.1以上版本
          }
        ]
      },
      "label": {
        "bg_image_url": "https://www.zhywater.com:21058/zbchhshwzh/profile/51img/popupBg.png",
        //本地图片地址一: "file:///D:/xxx/LabelBg.png",    D: 在线席位所在盘符
        //本地图片地址二: "path:/UserData/LabelBg.png",    图片资源由在线席位后台管理, 在线席位4.3.1以上版本
        "bg_size": data.bg_size || "150,45", //label大小(宽, 高 单位:像素)
        "bg_offset": data.bg_offset, //整个label左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
        "content": [
          {
            "text": [data.title, "ffffff", "12"],   //[文本内容, HEXA颜色, 文本大小]
            "text_offset": "10,5",     //文本框左上角相对于label中bg_offset左上角的margin偏移(x,y 单位:像素), 注: x为正向右, y为正向下
            "text_boxwidth": 140,      //文本框宽度
            "text_centered": true,   //文本居中(true:居中; false:不居中)
            "scroll_speed": 1         //文本滚动速度(0:不滚动; 注: "text_centered":true优先)
          },
          {
            "text": [data.number == -1 ? '--' : data.number, "ff00ff", "12"],   //[文本内容, HEXA颜色, 文本大小]
            "text_offset": "10,20",     //文本框左上角相对于label中bg_offset左上角的margin偏移(x,y 单位:像素), 注: x为正向右, y为正向下
            "text_boxwidth": 140,      //文本框宽度
            "text_centered": true,   //文本居中(true:居中; false:不居中)
            "scroll_speed": 1         //文本滚动速度(0:不滚动; 注: "text_centered":true优先)
          }, {
            "text": [data.sign || '', "ffffff", "12"],   //[文本内容, HEXA颜色, 文本大小]
            "text_offset": "50,20",     //文本框左上角相对于label中bg_offset左上角的margin偏移(x,y 单位:像素), 注: x为正向右, y为正向下
            "text_boxwidth": 140,      //文本框宽度
            "text_centered": true,   //文本居中(true:居中; false:不居中)
            "scroll_speed": 1         //文本滚动速度(0:不滚动; 注: "text_centered":true优先)
          }
        ]
      },
      // "window":{
      //     "url":"http://superapi.51hitech.com/doc-static/images/static/video.html",
      //            //本地地址一: "file:///D:/xxx/echarts.html",    D: 在线席位所在盘符
      //     "size":"520,350",      //window大小(宽,高 单位:像素)
      //     "offset":"50,180"      //window左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
      // }
    }
    if (data.zType) {
      jsondata.coord_z_type = data.zType
    }
    console.log(jsondata)
    cloudRender.SuperAPI("AddCustomPOI", jsondata, (status) => {
      console.log(status); //成功、失败回调
    })

  },
  //添加水位poi点
  addSiYuInfo(jsonData) {
    cloudRender.SuperAPI("AddCustomPOI", jsonData, (status) => {
      console.log("AddCustomPOI", status); //成功、失败回调
    })
  },
  //更新poi点水位数据
  updateSiYuInfo(jsonData) {
    cloudRender.SuperAPI('UpdateCustomPOILabel', jsonData, (status) => {
      console.log('UpdateCustomPOILabel', status) //成功、失败回调
    })
  },
  //更新水位poi点坐标
  updateSiYuCoord(jsonData) {
    cloudRender.SuperAPI('UpdateCustomPOICoord', jsonData, (status) => {
      console.log('UpdateCustomPOICoord', status) //成功、失败回调
    })
  },
  //删除水位poi点
  deleteSiYuPoi(jsonData) {
    cloudRender.SuperAPI("RemoveCovering", jsonData, (status) => {
      console.log(status); //成功、失败回调
    })
  },
  deletePoi(id, type) {
    let jsondata = {
      "id": id,            //覆盖物id
      "covering_type": type     //覆盖物类型, 详见下表
    }
    window.cloudRender.SuperAPI('RemoveCovering', jsondata).then((_back) => {
      console.log(_back)
    })
  },
  //加载淹没数据
  addFloodRouting(jsonData) {
    // let jsondata = {
    //     "TimeIndex":"", //时序
    //        "FloodGridCount":"62353", //
    //     "GridIDArray":[],//
    //     "ValueArray":[]//
    //    }
    cloudRender.SuperAPI("LoadFloodData", jsonData, (status) => {
      console.log(status); //成功、失败回调
    })

  },
  //设置格网数据
  SetGridData() {
    cloudRender.SuperAPI("SetGridData", {});
  },
  //播放淹没
  PlayFloodSim(jsonData) {
    // let jsonData = {

    //     "Index":"1",//播放时序
    //     "bIsHotMap":"true",//true显示热力图，false显示真是水体
    //     "PlaySpeed":"2",//播放速度
    //     "bPlay":"false"//是否从index处继续播放

    // }
    console.log(jsonData);
    cloudRender.SuperAPI("PlayFloodSim", jsonData, (status) => {
      console.log(status); //成功、失败回调
    })
  },
  //清除淹没
  ClearFloodData() {
    let jsondata = {}
    cloudRender.SuperAPI("ClearData", jsondata)
  },
  //调整水库河面高度
  MoveWater(jsondata) {
    cloudRender.SuperAPI("MoveWater", jsondata, (status) => {
      console.log('MoveWater', jsondata, status)
    })
  },
  //巡查漫游特效
  async roamingMoveEffect(roamingCoord, {coord, coord_z, coord_z_type, modelEid, pathId}, showInfo) {
    console.log(coord, coord_z)
    // debugger
    try {
      // 获取SYData.json的数据
      let SYResponse = await fetch('/json/SYData.json');
      let SYData = await SYResponse.json();
      let SYResultArray = SYData.args.coord_result;

      // 获取WYData.json的数据
      let WYResponse = await fetch('/json/WYData.json');
      let WYData = await WYResponse.json();
      let WYResultArray = WYData.args.coord_result;
      // 获取QJData.json的数据
      let QJResponse = await fetch('/json/QJData.json');
      let QJData = await QJResponse.json();
      let QJResultArray = QJData.args.coord_result;
      // 获取SCDData.json的数据
      let SCDResponse = await fetch('/json/SCDData.json');
      let SCDData = await SCDResponse.json();
      let SCDResultArray = SCDData.args.coord_result;
      // 获取SPDData.json的数据
      let SPDResponse = await fetch('/json/SPDData.json');
      let SPDData = await SPDResponse.json();
      let SPDResultArray = SPDData.args.coord_result;
      // 获取SWData.json的数据
      let SWResponse = await fetch('/json/SWData.json');
      let SWData = await SWResponse.json();
      let SWResultArray = SWData.args.coord_result;
      // 获取SZDData.json的数据
      let SZDResponse = await fetch('/json/SZDData.json');
      let SZDData = await SZDResponse.json();
      let SZDResultArray = SZDData.args.coord_result;
      // 获取WRJDData.json的数据
      let WRJDResponse = await fetch('/json/WRJDData.json');
      let WRJDData = await WRJDResponse.json();
      let WRJDResultArray = WRJDData.args.coord_result;
      // 获取YLData.json的数据
      let YLResponse = await fetch('/json/YLData.json');
      let YLData = await YLResponse.json();
      let YLResultArray = YLData.args.coord_result;
      // 合并数组
      var combinedArray = [SYResultArray, WYResultArray, QJResultArray, SCDResultArray, SPDResultArray, SWResultArray, SZDResultArray, WRJDResultArray, YLResultArray];
      this.combinedArray = combinedArray

      // 现在combinedArray包含了两个文件中的所有数据
      console.log(combinedArray);
    } catch (error) {
      console.error('Error fetching JSON files:', error);
    }
    // 路径
    let pathjson = {
      "id": pathId,    //路径id
      "coord_type": 0,         //路径中坐标类型(0:经纬度坐标, 1:cad坐标)
      "cad_mapkey": "",        //CAD基准点Key值, 项目中约定
      "coord_z_type": coord_z_type == 2 ? 2 : 0,       //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
      "type": "none",       //路径样式类型(none, solid, arrow, arrow_dot, dashed_dot, arrow_dashed, flash, scan_line, brimless_arrow, railway, railway, square_pipe, dashed_line)
      "color": "",       //路径颜色(HEX颜色值, 空值即透明; railway类型无效, 默认黑白色)
      "pass_color": "",  //覆盖物移动经过路径颜色(HEX颜色值)
      "width": 10,             //宽度(单位:米, 圆柱直径或方柱边长)
      "speedRate": 1,
      "points": roamingCoord
    }
    // 覆盖物沿路径移动
    let CoverToMoveData = {
      "attach_id": modelEid,    //要移动的覆盖物id (POI, 场景特效; 需提前创建完毕)
      "attach_type": "aes_object",    //要移动的覆盖物类型 (poi, scene_effect, viewshed)
      "be_attach_id": pathId,   //依附的覆盖物id (路径, 区域, 圆形区域; 需提前创建完毕)
      "be_attach_type": "path",         //依附的覆盖物类型 (path, range, circular_range)
      "speed": 3,                     //移动速度 (单位:米/秒)
      "loop": false,                    //是否循环
      "reverse": false,                 //是否反向移动,
      "current_attitude": true,       //元素姿态(true: 前一帧的姿态; 注: true优先, "pitch", "yaw", "roll" 无效)
    }
    // 镜头沿覆盖物移动
    let CameraToMoveData = {
      "be_attach_id": pathId,  //依附的覆盖物id (路径, 区域, 圆形区域; 需提前创建完毕)
      "be_attach_type": "path",        //依附的覆盖物类型 (path, range, circular_range)
      "speed": 3,                    //移动速度 (单位:米/秒)
      "loop": false,                   //是否循环
      "reverse": false,                //是否反向移动
      "arm_distance": 15,             //镜头距覆盖物距离(单位:米)
      "pitch": 25                     //镜头俯仰角(0 ~ 89)
    }
    //模型动画
    let jsondata = {
      "eid": modelEid,
      "clip_name": "AESFBX_Anim",                  //片段名称
      "start_and_end": "1,32",       //动画起止帧（按动画默认总帧数计算）
      //"12,"从第12帧播放到最后一帧；"12,12"停止到第12帧；","停止到最后一帧；"1,"从第一帧播放到最后一帧
      "play_rate": 1,           //播放倍速
      "loop": true,            //是否循环
      "reverse": false       //是否倒放
    }
    let weiyiCoords = await getWeiyiPoints()
    let shenyaCoords = await getShenyaPoints()
    let videoCoords = await getVideoPoints()
    const coords = [weiyiCoords.data.flat(), shenyaCoords.data.flat(), videoCoords.data.flat()].flat()
    window.cloudRender
      .SuperAPI("ShowHideAESObject", {
        eid: modelEid,
        bshow: true, //true:显示; false:隐藏
      })
      .then((_back) => {
        console.log(_back);
      });
    window.cloudRender.SuperAPI('AddPath', pathjson, (mycallback) => {
      console.log('镜头沿覆盖物移动路径: ', mycallback)
      let obj = JSON.parse(mycallback)
      if (obj.success) {
        // setTimeout(() => {
        window.cloudRender.SuperAPI('SetAESObjectAnimationPlay', jsondata, (mycallback) => {
          console.log('动画开始: ', mycallback)
        })
        window.cloudRender.SuperAPI('CoverToMove', CoverToMoveData, (mycallback) => {
          console.log('覆盖物沿路径移动行为: ', mycallback)
          for (let i = 0; i < combinedArray.length; i++) {
            // debugger
            for (let j = 0; j < combinedArray[i].length; j++) {
              let jsondataTest = {
                "id": combinedArray[i][j].id,            //覆盖物id
                "covering_type": "poi",    //覆盖物类型, 详见下表
                "bshow": false              //true:显示; false:隐藏
              }
              cloudRender.SuperAPI("ShowHideCovering", jsondataTest, (status) => {
                console.log(status); //成功、失败回调
              })
            }
          }
        })

        window.cloudRender.SuperAPI("SetCameraToMove", CameraToMoveData, (mycallback) => {
          console.log('镜头沿路径移动行为: ', mycallback)
        })
        // }, 5e2);
      }
    })
  },

  //暂停/继续/停止
  moveState(type, state) {
    // debugger
    if (state == "stop") {
      console.log("停止了");
      if (this.cameraInfoTimer) {
        clearInterval(this.cameraInfoTimer);
        console.log("定时器已关闭");
        // 清除引用以防止内存泄漏
        this.cameraInfoTimer = null;
      }
      for (let i = 0; i < this.combinedArray.length; i++) {
        // debugger
        for (let j = 0; j < this.combinedArray[i].length; j++) {
          let jsondataTest = {
            "id": this.combinedArray[i][j].id,            //覆盖物id
            "covering_type": "poi",    //覆盖物类型, 详见下表
            "bshow": true              //true:显示; false:隐藏
          }
          cloudRender.SuperAPI("ShowHideCovering", jsondataTest, (status) => {
            console.log(status); //成功、失败回调
          })
        }
      }
    }
    if (type == "roaming") {
      window.cloudRender
        .SuperAPI("SetCameraRoamingProState", {
          state: state,
        })
        .then((_back) => {
          console.log(_back);
          if (state == "stop") {
            this.defaultCamera();
          }
        });
    } else {
      window.cloudRender
        .SuperAPI("SetCameraToMoveState", {
          state: state, //pause:暂停移动; continue:继续移动; stop:停止移动, 释放焦点
        })
        .then((_back) => {
          console.log(_back);

        });
      window.cloudRender
        .SuperAPI("SetAESObjectAnimationPlayState", {
          eid: "-9150751364756233164",
          state: state,
        })
        .then((_back) => {
          console.log(_back);
          if (state == "stop") {
            this.defaultCamera();
          }
        });
    }
  },
  //默认镜头
  defaultCamera() {
    //场景镜头视界行为
    let jsondataCamera = {
      coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
      cad_mapkey: "", //CAD基准点Key值, 项目中约定
      coord_z: "28.021563", //海拔高度(单位:米)
      center_coord: "119.565473,31.246013", //中心点的坐标 lng,lat
      arm_distance: 253, //镜头距中心点距离(单位:米)
      pitch: 12.192566, //镜头俯仰角(5~89)
      yaw: 203, //镜头偏航角(0正北, 0~359)
      fly: false, //true: 飞行动画(有一个短暂飞行动画,并按照arm_distance,pitch,yaw设置镜头);
      //false: 立刻跳转过去(瞬移)
    };
    window.cloudRender
      .SuperAPI("SetCameraInfo", jsondataCamera)
      .then((_back) => {
        console.log("场景镜头视界行为", _back);
      });
  },

  //巡查漫游特效
  async roamingMove(roamingCoord, pathId) {
    // console.log(coord, coord_z)
    let jsondata = {
      "coord_type": 0,            //坐标类型(0:经纬度坐标, 1:cad坐标)
      "cad_mapkey": "",           //CAD基准点Key值, 项目中约定
      "coord_z_type": 2,          //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
      "subsidiary_show": false,     //是否显示辅助线(true:显示; false:不显示)
      "points": roamingCoord
    }
    window.cloudRender.SuperAPI('SetCameraRoamingPro', jsondata).then((_back) => {
      console.log(_back)
    })
  },
  //初始化自定义poi点(渗压/位移)
  async initStation(item, state) {
    let labelList = item.deviceCode ? item.deviceCode : item.label;
    let content = [];
    content.push({
      text: [labelList, "#fff", "12"],
      text_offset: "0,0",
      text_centered: false,
      text_boxwidth: labelList.length * 16,
      scroll_speed: 1,
    });
    let point = {
      id: `${item.deviceCode ? item.deviceCode : item.id}`,
      //label: item.label,
      coord: item.gcjlon + "," + item.gcjlat,
      coord_z: item.height,
      coord_z_type: 2,
      point_type: item.type,
      state: state,
      always_show_label: false,
      show_label_range: "0,20",
      marker: {
        size: "21,35",
        images: [
          {
            define_state: state,
            normal_url: "",
            activate_url: ""
          },
        ],
      },
      label: {
        bg_size: content.length * 150 + "," + content.length * 25,
        bg_offset: "22,35", //整个label左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
        content: content,
      },
    };
    window.cloudRender.SuperAPI("AddCustomPOI", point, (status) => {
      console.log(status); //成功、失败回调
    })
  },

  cameraMoveState: () => {
    //场景镜头视界行为
    let jsondataCamera = {
      coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
      cad_mapkey: "", //CAD基准点Key值, 项目中约定
      coord_z: "28.021563", //海拔高度(单位:米)
      center_coord: "119.565473,31.246013", //中心点的坐标 lng,lat
      arm_distance: 253, //镜头距中心点距离(单位:米)
      pitch: 12.192566, //镜头俯仰角(5~89)
      yaw: 203, //镜头偏航角(0正北, 0~359)
      fly: false, //true: 飞行动画(有一个短暂飞行动画,并按照arm_distance,pitch,yaw设置镜头);
      //false: 立刻跳转过去(瞬移)
    };
    setTimeout(() => {
      window.cloudRender
        .SuperAPI("SetCameraInfo", jsondataCamera)
        .then((_back) => {
          console.log("场景镜头视界行为", _back);
        })
    }, 500);

  },

  resetCameraSpace(jsonData) {
    let jo = {
      "state": jsonData
    }
    window.cloudRender.SuperAPI("ResetCameraSpace", jo, (status) => {
      console.log('resetCameraSpace解除镜头限制', status);//成功、失败回调
    })
  },
  add3DText(jsonData) {
    let jo = {
      "id": jsonData.id,
      "coord_type": 0,                            //坐标类型(0:经纬度坐标, 1:cad坐标)
      "cad_mapkey": "",                           //CAD基准点Key值, 项目中约定
      "coord": jsonData.coord,        //坐标 lng,lat
      "coord_z": 3000,                              //高度(单位:米)
      "coord_z_type": 2,                          //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
      "text": jsonData.text,                           //文字内容
      "color": "000000",                          //颜色(HEX颜色值)
      "size": 800,                                 //字体大小(单位:米)
      "thickness": 100,                            //厚度(单位:米)
      "type": "plain",                            //样式(plain; reflection; metal)
      "outline": 0.5,                               //轮廓(单位:百分比), 取值范围[0~1]
      "portrait": false,                          //纵向(true/false)
      "space": 10,                               //间距(单位:米)
      "flash": 50,                                 //闪烁动效(单位:秒)
      "bounce": 1,                                //反弹动效(单位:米/秒)
      "pitch": 0,                                 //俯仰角(-90~90)
      "yaw": 0,                                   //偏航角(0正北, 0~360)
      "face_to_camera": true                      //文字是否跟踪朝向摄像机(注:true优先, "pitch", "yaw" 无效)
    }
    window.cloudRender.SuperAPI("Add3DText", jo, (status) => {
      console.log("add3DText添加3D文字", status); //成功、失败回调
    })
  },
  hsskCamreraRoamingState: 0,
  hsskCamreraRoamingPlay() {
    let jo = {
      "state": "free"
    }
    window.cloudRender.SuperAPI('ResetCameraSpace', jo, (_back) => {
      let jsondata = {
        "coord_type": 0,            //坐标类型(0:经纬度坐标, 1:cad坐标)
        "cad_mapkey": "",           //CAD基准点Key值, 项目中约定
        "coord_z_type": 2,          //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
        "subsidiary_show": false,     //是否显示辅助线(true:显示; false:不显示)
        "points": hsskCameraRoamingData
      }
      window.cloudRender.SuperAPI("SetCameraRoamingPro", jsondata, (status) => {
        this.hsskCamreraRoamingState = 1
        store.commit("map/SET_HSSK_CAMERA_ROAMING_STATE", 1);
        console.log(status); //成功、失败回调
      })
    })
  },
  updateHsskCameraRoamingState(index) {
    let state = null
    if (index == 1) {
      state = 'pause'
    } else if (index == 2) {
      state = 'continue'
    } else if (index == 0) {
      state = 'stop'
    }
    if (state != null) {
      let jsondata = {
        "state": state      //pause:暂停移动; continue:继续移动; stop:停止移动, 释放焦点
      }
      window.cloudRender.SuperAPI("SetCameraRoamingProState", jsondata, (status) => {
        this.hsskCamreraRoamingState = jsondata.state == 'pause' ? 2 : jsondata.state == 'continue' ? 1 : 0
        store.commit("map/SET_HSSK_CAMERA_ROAMING_STATE", this.hsskCamreraRoamingState);
        console.log(status); //成功、失败回调
      })
    }
  },
  personFly: true,
  personPatrol(show) {
    if (show) {
      if (personModelInfo.state == 0) {
        personModelInfo.state = 1
        personModelInfo.startTime = parseTime(new Date())
        let lnglat1 = roamingCoords[0].coord.split(',')
        let lnglat2 = roamingCoords[1].coord.split(',')
        // let yaw = getAngle(Number(lnglat1[0]), Number(lnglat1[1]), Number(lnglat2[0]), Number(lnglat2[1]))
        // 调整镜头朝向
        let cameraInfoJo = {
          "coord_type": 0,
          "cad_mapkey": "",
          "coord_z": roamingCoords[0].coord_z,
          "center_coord": roamingCoords[0].coord,
          "arm_distance": personModelInfo.distance,
          "pitch": 26,
          "yaw": 327.27177214203647, // yaw,
          "fly": false
        }
        window.cloudRender.SuperAPI("SetCameraInfo", cameraInfoJo, (status) => {
          console.log(status); //成功、失败回调
        })
        // 调整人物模型朝向
        let jo = {
          "eid": "-9150751364756233164",
          "coord_type": 0,
          "cad_mapkey": '',
          "scale": 1,
          "coord": roamingCoords[0]["coord"],
          "coord_z_type": 2,
          "coord_z": roamingCoords[0]["coord_z"],
          "pitch": 0,
          "roll": 0,
          "yaw": 147.27177214203647 // yaw - 180
        };
        window.cloudRender.SuperAPI("updateAESObjectTransform", jo, (status) => {
          console.log("updateAESObjectTransform", status); //成功、失败回调
          // 显示人物模型
          let showHideAESObjectJo = {
            "eid": [personModelInfo.eid],
            "bshow": true       //true:显示; false:隐藏
          }
          window.cloudRender.SuperAPI("ShowHideAESObject", showHideAESObjectJo, (ShowHideAESObjectBack) => {
            console.log(ShowHideAESObjectBack); //成功、失败回调
            if (personModelInfo.path.length > 0) {
              // 删除之前路径
              let ids = []
              personModelInfo.path.forEach(item => {
                ids.push(item.id)
              })
              let removeCoveringJo = {
                "id": ids,            //覆盖物id
                "covering_type": "path"     //覆盖物类型, 详见下表
              }
              window.cloudRender.SuperAPI("RemoveCovering", removeCoveringJo, (removeCoveringBack) => {
                console.log(removeCoveringBack); //成功、失败回调
                // 创建路径
                let path = {
                  "id": 'person_patrol_path',    //路径id
                  "coord_type": 0,         //路径中坐标类型(0:经纬度坐标, 1:cad坐标)
                  "cad_mapkey": "",        //CAD基准点Key值, 项目中约定
                  "coord_z_type": 2,       //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
                  "type": "none",       //路径样式类型(none, solid, arrow, arrow_dot, dashed_dot, arrow_dashed, flash, scan_line, brimless_arrow, railway, railway, square_pipe, dashed_line)
                  "color": "",       //路径颜色(HEX颜色值, 空值即透明; railway类型无效, 默认黑白色)
                  "pass_color": "",  //覆盖物移动经过路径颜色(HEX颜色值)
                  "width": 1,             //宽度(单位:米, 圆柱直径或方柱边长)
                  "speedRate": 1,
                  "points": roamingCoords
                }
                personModelInfo.path.push(path)
                window.cloudRender.SuperAPI('AddPath', path, (pathback) => {
                  console.log(pathback);
                  //模型动画
                  let animationPlayJo = {
                    "eid": personModelInfo.eid,
                    "clip_name": "AESFBX_Anim",                  //片段名称
                    "start_and_end": "1,32",       //动画起止帧（按动画默认总帧数计算）
                    //"12,"从第12帧播放到最后一帧；"12,12"停止到第12帧；","停止到最后一帧；"1,"从第一帧播放到最后一帧
                    "play_rate": 1,           //播放倍速
                    "loop": true,            //是否循环
                    "reverse": false       //是否倒放
                  }
                  window.cloudRender.SuperAPI('SetAESObjectAnimationPlay', animationPlayJo, (animationPlayback) => {
                    console.log('动画开始: ', animationPlayback)
                  })
                  // 覆盖物沿路径移动
                  let coverToMoveDataJo = {
                    "attach_id": personModelInfo.eid,    //要移动的覆盖物id (POI, 场景特效; 需提前创建完毕)
                    "attach_type": "aes_object",    //要移动的覆盖物类型 (poi, scene_effect, viewshed)
                    "be_attach_id": 'person_patrol_path',   //依附的覆盖物id (路径, 区域, 圆形区域; 需提前创建完毕)
                    "be_attach_type": "path",         //依附的覆盖物类型 (path, range, circular_range)
                    "speed": personModelInfo.speed * personModelInfo.times,                     //移动速度 (单位:米/秒)
                    "loop": false,                    //是否循环
                    "reverse": false,                 //是否反向移动,
                    "current_attitude": this.personFly,       //元素姿态(true: 前一帧的姿态; 注: true优先, "pitch", "yaw", "roll" 无效)
                    "pitch": 0,
                    "roll": 0,
                    "yaw": 147.27177214203647 // yaw - 180
                  }
                  window.cloudRender.SuperAPI('CoverToMove', coverToMoveDataJo, (mycallback) => {
                    console.log('覆盖物沿路径移动: ', mycallback)
                    personModelInfo.state = 1
                    // this.initPersonPatrolTempPoi(true, [{
                    //   id: '1',
                    //   label: '告警：人员非法闯入！'
                    // }])
                    // 镜头跟随
                    let cameraTraceJo = {
                      "trace_object_type": "aes_object",    //支持类型：aes_object  ,poi，场景特效，可视域，3D文字，灯光
                      "trace_object_id": personModelInfo.eid, //对象ID
                      "arm_distance": personModelInfo.distance,
                      "fly": true
                    }
                    window.cloudRender.SuperAPI("CameraTrace", cameraTraceJo, (CameraTraceBack) => {
                      console.log(CameraTraceBack); //成功、失败回调
                    })
                  })
                })
              })
            } else {
              // 创建路径
              let path = {
                "id": 'person_patrol_path',    //路径id
                "coord_type": 0,         //路径中坐标类型(0:经纬度坐标, 1:cad坐标)
                "cad_mapkey": "",        //CAD基准点Key值, 项目中约定
                "coord_z_type": 2,       //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
                "type": "none",       //路径样式类型(none, solid, arrow, arrow_dot, dashed_dot, arrow_dashed, flash, scan_line, brimless_arrow, railway, railway, square_pipe, dashed_line)
                "color": "",       //路径颜色(HEX颜色值, 空值即透明; railway类型无效, 默认黑白色)
                "pass_color": "",  //覆盖物移动经过路径颜色(HEX颜色值)
                "width": 1,             //宽度(单位:米, 圆柱直径或方柱边长)
                "speedRate": 1,
                "points": roamingCoords
              }
              personModelInfo.path.push(path)
              window.cloudRender.SuperAPI('AddPath', path, (pathback) => {
                console.log(pathback);
                //模型动画
                let animationPlayJo = {
                  "eid": personModelInfo.eid,
                  "clip_name": "AESFBX_Anim",                  //片段名称
                  "start_and_end": "1,32",       //动画起止帧（按动画默认总帧数计算）
                  //"12,"从第12帧播放到最后一帧；"12,12"停止到第12帧；","停止到最后一帧；"1,"从第一帧播放到最后一帧
                  "play_rate": 1,           //播放倍速
                  "loop": true,            //是否循环
                  "reverse": false       //是否倒放
                }
                window.cloudRender.SuperAPI('SetAESObjectAnimationPlay', animationPlayJo, (animationPlayback) => {
                  console.log('动画开始: ', animationPlayback)
                })
                // 覆盖物沿路径移动
                let coverToMoveDataJo = {
                  "attach_id": personModelInfo.eid,    //要移动的覆盖物id (POI, 场景特效; 需提前创建完毕)
                  "attach_type": "aes_object",    //要移动的覆盖物类型 (poi, scene_effect, viewshed)
                  "be_attach_id": 'person_patrol_path',   //依附的覆盖物id (路径, 区域, 圆形区域; 需提前创建完毕)
                  "be_attach_type": "path",         //依附的覆盖物类型 (path, range, circular_range)
                  "speed": personModelInfo.speed * personModelInfo.times,                     //移动速度 (单位:米/秒)
                  "loop": false,                    //是否循环
                  "reverse": false,                 //是否反向移动,
                  "current_attitude": this.personFly,       //元素姿态(true: 前一帧的姿态; 注: true优先, "pitch", "yaw", "roll" 无效)
                  "pitch": 0,
                  "roll": 0,
                  "yaw": 147.27177214203647 // yaw - 180
                }
                window.cloudRender.SuperAPI('CoverToMove', coverToMoveDataJo, (mycallback) => {
                  console.log('覆盖物沿路径移动: ', mycallback)
                  personModelInfo.state = 1
                  // this.initPersonPatrolTempPoi(true, [{
                  //   id: '1',
                  //   label: '告警：人员非法闯入！'
                  // }])
                  // 镜头跟随
                  let cameraTraceJo = {
                    "trace_object_type": "aes_object",    //支持类型：aes_object  ,poi，场景特效，可视域，3D文字，灯光
                    "trace_object_id": personModelInfo.eid, //对象ID
                    "arm_distance": personModelInfo.distance,
                    "fly": true
                  }
                  window.cloudRender.SuperAPI("CameraTrace", cameraTraceJo, (CameraTraceBack) => {
                    console.log(CameraTraceBack); //成功、失败回调
                  })
                })
              })
            }
          })
        })
      } else if (personModelInfo.state > 0) {
        // 调整镜头朝向
        let cameraInfoJo = {
          "coord_type": 0,
          "cad_mapkey": "",
          "coord_z": roamingCoords[0].coord_z,
          "center_coord": roamingCoords[0].coord,
          "arm_distance": personModelInfo.distance,
          "pitch": 26,
          "yaw": 327.27177214203647, // yaw,
          "fly": false
        }
        window.cloudRender.SuperAPI("SetCameraInfo", cameraInfoJo, (status) => {
          console.log(status); //成功、失败回调
        })
        // 调整人物模型朝向
        let jo = {
          "eid": "-9150751364756233164",
          "coord_type": 0,
          "cad_mapkey": '',
          "scale": 1,
          "coord": roamingCoords[0]["coord"],
          "coord_z_type": 2,
          "coord_z": roamingCoords[0]["coord_z"],
          "pitch": 0,
          "roll": 0,
          "yaw": 147.27177214203647 // yaw - 180
        };
        window.cloudRender.SuperAPI("updateAESObjectTransform", jo, (status) => {
          console.log("updateAESObjectTransform", status); //成功、失败回调
          // 显示人物模型
          let showHideAESObjectJo = {
            "eid": [personModelInfo.eid],
            "bshow": true       //true:显示; false:隐藏
          }
          window.cloudRender.SuperAPI("ShowHideAESObject", showHideAESObjectJo, (ShowHideAESObjectBack) => {
            console.log(ShowHideAESObjectBack); //成功、失败回调
            if (personModelInfo.path.length > 0) {
              // 删除之前路径
              let ids = []
              personModelInfo.path.forEach(item => {
                ids.push(item.id)
              })
              let removeCoveringJo = {
                "id": ids,            //覆盖物id
                "covering_type": "path"     //覆盖物类型, 详见下表
              }
              window.cloudRender.SuperAPI("RemoveCovering", removeCoveringJo, (removeCoveringBack) => {
                console.log(removeCoveringBack); //成功、失败回调
                // 创建路径
                let path = {
                  "id": 'person_patrol_path',    //路径id
                  "coord_type": 0,         //路径中坐标类型(0:经纬度坐标, 1:cad坐标)
                  "cad_mapkey": "",        //CAD基准点Key值, 项目中约定
                  "coord_z_type": 2,       //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
                  "type": "none",       //路径样式类型(none, solid, arrow, arrow_dot, dashed_dot, arrow_dashed, flash, scan_line, brimless_arrow, railway, railway, square_pipe, dashed_line)
                  "color": "",       //路径颜色(HEX颜色值, 空值即透明; railway类型无效, 默认黑白色)
                  "pass_color": "",  //覆盖物移动经过路径颜色(HEX颜色值)
                  "width": 1,             //宽度(单位:米, 圆柱直径或方柱边长)
                  "speedRate": 1,
                  "points": roamingCoords
                }
                personModelInfo.path.push(path)
                window.cloudRender.SuperAPI('AddPath', path, (pathback) => {
                  console.log(pathback);
                  //模型动画
                  let animationPlayJo = {
                    "eid": personModelInfo.eid,
                    "clip_name": "AESFBX_Anim",                  //片段名称
                    "start_and_end": "1,32",       //动画起止帧（按动画默认总帧数计算）
                    //"12,"从第12帧播放到最后一帧；"12,12"停止到第12帧；","停止到最后一帧；"1,"从第一帧播放到最后一帧
                    "play_rate": 1,           //播放倍速
                    "loop": true,            //是否循环
                    "reverse": false       //是否倒放
                  }
                  window.cloudRender.SuperAPI('SetAESObjectAnimationPlay', animationPlayJo, (animationPlayback) => {
                    console.log('动画开始: ', animationPlayback)
                  })
                  // 覆盖物沿路径移动
                  let coverToMoveDataJo = {
                    "attach_id": personModelInfo.eid,    //要移动的覆盖物id (POI, 场景特效; 需提前创建完毕)
                    "attach_type": "aes_object",    //要移动的覆盖物类型 (poi, scene_effect, viewshed)
                    "be_attach_id": 'person_patrol_path',   //依附的覆盖物id (路径, 区域, 圆形区域; 需提前创建完毕)
                    "be_attach_type": "path",         //依附的覆盖物类型 (path, range, circular_range)
                    "speed": personModelInfo.speed * personModelInfo.times,                     //移动速度 (单位:米/秒)
                    "loop": false,                    //是否循环
                    "reverse": false,                 //是否反向移动,
                    "current_attitude": this.personFly,       //元素姿态(true: 前一帧的姿态; 注: true优先, "pitch", "yaw", "roll" 无效)
                    "pitch": 0,
                    "roll": 0,
                    "yaw": 147.27177214203647 // yaw - 180
                  }
                  window.cloudRender.SuperAPI('CoverToMove', coverToMoveDataJo, (mycallback) => {
                    console.log('覆盖物沿路径移动: ', mycallback)
                    personModelInfo.state = 1
                    // this.initPersonPatrolTempPoi(true, [{
                    //   id: '1',
                    //   label: '告警：人员非法闯入！'
                    // }])
                    // 镜头跟随
                    let cameraTraceJo = {
                      "trace_object_type": "aes_object",    //支持类型：aes_object  ,poi，场景特效，可视域，3D文字，灯光
                      "trace_object_id": personModelInfo.eid, //对象ID
                      "arm_distance": personModelInfo.distance,
                      "fly": true
                    }
                    window.cloudRender.SuperAPI("CameraTrace", cameraTraceJo, (CameraTraceBack) => {
                      console.log(CameraTraceBack); //成功、失败回调
                    })
                  })
                })
              })
            } else {
              // 创建路径
              let path = {
                "id": 'person_patrol_path',    //路径id
                "coord_type": 0,         //路径中坐标类型(0:经纬度坐标, 1:cad坐标)
                "cad_mapkey": "",        //CAD基准点Key值, 项目中约定
                "coord_z_type": 2,       //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
                "type": "none",       //路径样式类型(none, solid, arrow, arrow_dot, dashed_dot, arrow_dashed, flash, scan_line, brimless_arrow, railway, railway, square_pipe, dashed_line)
                "color": "",       //路径颜色(HEX颜色值, 空值即透明; railway类型无效, 默认黑白色)
                "pass_color": "",  //覆盖物移动经过路径颜色(HEX颜色值)
                "width": 1,             //宽度(单位:米, 圆柱直径或方柱边长)
                "speedRate": 1,
                "points": roamingCoords
              }
              personModelInfo.path.push(path)
              window.cloudRender.SuperAPI('AddPath', path, (pathback) => {
                console.log(pathback);
                //模型动画
                let animationPlayJo = {
                  "eid": personModelInfo.eid,
                  "clip_name": "AESFBX_Anim",                  //片段名称
                  "start_and_end": "1,32",       //动画起止帧（按动画默认总帧数计算）
                  //"12,"从第12帧播放到最后一帧；"12,12"停止到第12帧；","停止到最后一帧；"1,"从第一帧播放到最后一帧
                  "play_rate": 1,           //播放倍速
                  "loop": true,            //是否循环
                  "reverse": false       //是否倒放
                }
                window.cloudRender.SuperAPI('SetAESObjectAnimationPlay', animationPlayJo, (animationPlayback) => {
                  console.log('动画开始: ', animationPlayback)
                })
                // 覆盖物沿路径移动
                let coverToMoveDataJo = {
                  "attach_id": personModelInfo.eid,    //要移动的覆盖物id (POI, 场景特效; 需提前创建完毕)
                  "attach_type": "aes_object",    //要移动的覆盖物类型 (poi, scene_effect, viewshed)
                  "be_attach_id": 'person_patrol_path',   //依附的覆盖物id (路径, 区域, 圆形区域; 需提前创建完毕)
                  "be_attach_type": "path",         //依附的覆盖物类型 (path, range, circular_range)
                  "speed": personModelInfo.speed * personModelInfo.times,                     //移动速度 (单位:米/秒)
                  "loop": false,                    //是否循环
                  "reverse": false,                 //是否反向移动,
                  "current_attitude": this.personFly,       //元素姿态(true: 前一帧的姿态; 注: true优先, "pitch", "yaw", "roll" 无效)

                }
                window.cloudRender.SuperAPI('CoverToMove', coverToMoveDataJo, (mycallback) => {
                  console.log('覆盖物沿路径移动: ', mycallback)
                  personModelInfo.state = 1
                  // this.initPersonPatrolTempPoi(true, [{
                  //   id: '1',
                  //   label: '告警：人员非法闯入！'
                  // }])
                  // 镜头跟随
                  let cameraTraceJo = {
                    "trace_object_type": "aes_object",    //支持类型：aes_object  ,poi，场景特效，可视域，3D文字，灯光
                    "trace_object_id": personModelInfo.eid, //对象ID
                    "arm_distance": personModelInfo.distance,
                    "fly": true
                  }
                  window.cloudRender.SuperAPI("CameraTrace", cameraTraceJo, (CameraTraceBack) => {
                    console.log(CameraTraceBack); //成功、失败回调
                  })
                })
              })
            }
          })
        })
      }
    } else {
      personModelInfo.state = 0
      personModelInfo.endTime = parseTime(new Date())
      let jo = {
        "eid": [personModelInfo.eid]
      }
      window.cloudRender.SuperAPI("GetAESObjectDataWithEids", jo, (_back) => {
        let data = JSON.parse(_back)
        let coordInfo = data.args.data[0].bounding_center_coord
        if (coordInfo) {
          let lnglat = roamingCoords[0].coord.split(',')
          let distance = this.GetDistance(Number(lnglat[1]), Number(lnglat[0]), Number(coordInfo.y), Number(coordInfo.x))
          // 添加vr记录
          let P = {
            "name": "vr巡查记录2",
            "startTime": personModelInfo.startTime,
            "endTime": personModelInfo.endTime,
            "distance": distance ? Number(distance / 1000).toFixed(2) : "0.46"
          }
          if (personModelInfo.startTime != null && personModelInfo.endTime != null) {
            addVrRecord(P).then(res => {
              console.log('addVrRecord', P, res)
            })
          }
        }
      })
      let jsondata = {
        "id": personModelInfo.eid,           //移动的覆盖物id
        "type": "aes_object",                  //移动的覆盖物类型 见下表
        "play": false                   //true:继续移动; false:暂停移动;
      }
      window.cloudRender.SuperAPI("PlayCoverMoveState", jsondata, (status) => {
        console.log(status); //成功、失败回调
        // 删除之前路径
        let ids = []
        personModelInfo.path.forEach(item => {
          ids.push(item.id)
        })
        let removeCoveringJo = {
          "id": ids,            //覆盖物id
          "covering_type": "path"     //覆盖物类型, 详见下表
        }
        window.cloudRender.SuperAPI("RemoveCovering", removeCoveringJo, (removeCoveringBack) => {
          console.log(removeCoveringBack);
          // 调整人物模型位置
          let jo = {
            "eid": "-9150751364756233164",
            "coord_type": 0,
            "cad_mapkey": '',
            "scale": 1,
            "coord": roamingCoords[0]["coord"],
            "coord_z_type": 2,
            "coord_z": roamingCoords[0]["coord_z"] - 2,
            "pitch": 0,
            "roll": 0,
            "yaw": 147.27177214203647, // yaw - 180
          };
          window.cloudRender.SuperAPI("updateAESObjectTransform", jo, (updateAESObjectTransformstatus) => {
            console.log(updateAESObjectTransformstatus)
            personModelInfo.state = 0
            this.personPatrolCameraTraceUpdate(false)
          })
          let animationPlayJo = {
            "eid": personModelInfo.eid,
            "state": "stop"
            //pause:暂停播放; continue:从暂停处继续播放，播放设置沿用暂停前的设置;
            //stop，中止播放，模型重置回初始状态，continue此时无效
          }
          window.cloudRender.SuperAPI("SetAESObjectAnimationPlayState", animationPlayJo, (status) => {
            console.log(status); //成功、失败回调
          })
          this.resetDtViewer('default')
        })
      })
    }

  },
  personPatrolState(state) {
    let jsondata = {
      "id": personModelInfo.eid,           //移动的覆盖物id
      "type": "aes_object",                  //移动的覆盖物类型 见下表
      "play": state                   //true:继续移动; false:暂停移动;
    }
    window.cloudRender.SuperAPI("PlayCoverMoveState", jsondata, (status) => {
      personModelInfo.state = state ? 1 : 2
      console.log(status); //成功、失败回调
      if (state) {
        //模型动画
        let animationPlayJo = {
          "eid": personModelInfo.eid,
          "clip_name": "AESFBX_Anim",                  //片段名称
          "start_and_end": "1,32",       //动画起止帧（按动画默认总帧数计算）
          //"12,"从第12帧播放到最后一帧；"12,12"停止到第12帧；","停止到最后一帧；"1,"从第一帧播放到最后一帧
          "play_rate": 1,           //播放倍速
          "loop": true,            //是否循环
          "reverse": false       //是否倒放
        }
        window.cloudRender.SuperAPI('SetAESObjectAnimationPlay', animationPlayJo, (animationPlayback) => {
          console.log('动画开始: ', animationPlayback)
        })
      } else {
        let animationPlayJo = {
          "eid": personModelInfo.eid,
          "state": "stop"
          //pause:暂停播放; continue:从暂停处继续播放，播放设置沿用暂停前的设置;
          //stop，中止播放，模型重置回初始状态，continue此时无效
        }
        window.cloudRender.SuperAPI("SetAESObjectAnimationPlayState", animationPlayJo, (status) => {
          console.log(status); //成功、失败回调
        })
      }
    })
  },
  personPatrolCameraTraceUpdate(cameraTrace) {
    if (cameraTrace) {
      let jsondata = {
        "trace_object_type": "aes_object",    //支持类型：aes_object  ,poi，场景特效，可视域，3D文字，灯光
        "trace_object_id": personModelInfo.eid, //对象ID
        "arm_distance": 30,
        "fly": true
      }
      window.cloudRender.SuperAPI("CameraTrace", jsondata, (status) => {
        personModelInfo.cameraTrace = cameraTrace
        console.log(status); //成功、失败回调
      })
    } else {
      window.cloudRender.SuperAPI("StopCameraTrace", null, (status) => {
        personModelInfo.cameraTrace = cameraTrace
        console.log(status); //成功、失败回调
      })
    }
  },
  initPersonPatrolTempPoi(show, list) {
    if (show) {
      if (list && list.length > 0) {
        list.forEach(item => {
          let obj = personModelInfo.poiList.find(ite => {
            return ite.point_type == 'alarm_temp_point' && ite.id == ('pp_temp_' + item.itemId)
          })
          if (obj) {

          } else {
            let point = {
              id: 'pp_temp_' + item.itemId,
              coord: "119.757668,31.220539", // '119.758171,31.220245', // '119.759697,31.217882',
              coord_z_type: 2,
              coord_z: "38.726357", // '36.209991',
              state: 'pp_temp_state',
              point_type: 'alarm_temp_point',
              always_show_label: false,
              show_label_range: "0,100",
              params: {
                ...item
              },
              marker: {
                size: "1,1",
                images: [
                  {
                    define_state: 'pp_temp_state',
                    normal_url: '', //'http://www.zhywater.com:18190/static/sylogo/a1.png',
                    activate_url: '', // 'http://www.zhywater.com:18190/static/sylogo/a1.png'
                  },
                ],
              },
              label: {
                bg_size: (item.label.length * 20 + 30) + ",32",
                bg_offset: "-60,15",
                content: [
                  {
                    text: [item.label, "#ff0000", "18"],
                    text_offset: "5,5",
                    text_centered: false,
                    text_boxwidth: 280,
                    scroll_speed: 1,
                  }
                ],
              },
            }
            personModelInfo.poiList.push(point)
            this.AddCustomPOI(point)
          }
        })
      }
    } else {
      let items = personModelInfo.poiList.find(item => {
        return item.point_type == 'alarm_temp_point'
      })
      if (items && items.length > 0) {
        let ids = []
        items.forEach(item => {
          ids.push(item.id)
        })
        let jsondata = {
          "id": ids,            //覆盖物id
          "covering_type": "poi"     //覆盖物类型, 详见下表
        }
        window.cloudRender.SuperAPI("RemoveCovering", jsondata, (status) => {
          console.log(status); //成功、失败回调
          personModelInfo.poiList = personModelInfo.poiList.filter(item => item.point_type != 'alarm_temp_point')
        })
      } else if (items && items.id) {
        let jsondata = {
          "id": items.id,            //覆盖物id
          "covering_type": "poi"     //覆盖物类型, 详见下表
        }
        window.cloudRender.SuperAPI("RemoveCovering", jsondata, (status) => {
          console.log(status); //成功、失败回调
          personModelInfo.poiList = personModelInfo.poiList.filter(item => item.point_type != 'alarm_temp_point')
        })
      }

    }
  },
  initEffect(data) {
    let jsondata = {
      "id": data.id || "effect_id",
      "coord_type": 0,                     //坐标类型(0:经纬度坐标, 1:cad坐标)
      "cad_mapkey": "",                    //CAD基准点Key值, 项目中约定
      "type": data.type || "shield",                    //样式类型(见下表)
      "scale": data.scale || 1,                         //半径(单位:米; "adaptive":true 时含义为倍率)
      "adaptive": data.adaptive != null ? data.adaptive : true,                    //true:自适应大小;false:默认
      "coord": data.coord,      //坐标点 lng,lat
      "coord_z": data.coord_z || 0,                        //高度(单位:米)
      "coord_z_type": data.coord_z_type != null ? data.coord_z_type : 0,                   //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
      "pitch": 0,                         //俯仰角, 参考(-90~90)
      "roll": 0,                           //翻滚角, 参考(0~360)
      "yaw": 0,                            //偏航角, 参考(0正北, 0~360)
      "title_text": data.text,                //文本内容, 富文本内容
      "title_offset": data.title_offset,            //文本偏移(单位:米; 东西向为x轴进行偏移)
      "title_face_to_camera": true,        //顶部文字是否跟踪朝向摄像机(注: true优先, "pitch", "roll", "yaw" 无效)
      "title_text_portrait": false          //顶部文字排列方向(true: 纵向, false: 横向)
    }
    window.cloudRender.SuperAPI("AddEffect", jsondata, (status) => {
      console.log(status); //成功、失败回调
    })
  },
  cameraToYoucheZM() {
    let cameraInfoJo = {
      "coord_type": 0,
      "cad_mapkey": "",
      "coord_z": 28.021563, //39.994629, //"49.058231",
      "center_coord": '119.757880,31.219626', //'119.757846,31.219863', //'119.757778,31.219953',
      "arm_distance": 47.700771, //22.978989, //"8.188735",
      "pitch": 31.539093, //37.403625, //"33.125916",
      "yaw": 166.0, //152.0, //"173.0",
      "fly": false
    }
    window.cloudRender.SuperAPI("SetCameraInfo", cameraInfoJo, (status) => {
      console.log(status); //成功、失败回调
    })
  },
  // 设置默认视角
  setDefaultView(jsondata) {
    if (jsondata != null) {
      window.cloudRender.SuperAPI("SetCameraInfo", jsondata).then((_back) => {
        //开始旋转
        // this.setCameraRotate(50, "clockwise");
        //开始漫游
        // setTimeout(() => {
        //   this.InitsetCameraRoamState();
        // }, 3000);
      });
    }
  },
  resetDtViewer(code) {
    let viewer = personModelInfo.mapViewerOptions.find((item) => {
      return code == item.code;
    });
    if (viewer) {
      this.setDefaultView(viewer)
    }
  },
  setCameraRotate(time, direction) {
    //开始旋转
    let jo = {
      time: time || 50,
      direction: direction == 1 ? 'clockwise' : direction == 1 ? 'anticlockwise' : 'stop',
    };
    this.cloudRender.SuperAPI("SetCameraRotate", jo, (e) => {
    });
  },
}

// 自定义POI点数据处理
function handleCustomPOIData(jsonData) {
  let jo = {
    id: jsonData.id,
    adaptive: jsonData.adaptive != null ? jsonData.adaptive : true,
    coord_type: jsonData.coord_type || 0,                         //坐标类型(0:经纬度坐标, 1:cad坐标)
    cad_mapkey: jsonData.cad_mapkey || '',                        //CAD基准点的Key值, 项目中约定
    coord: jsonData.coord,         //POI点的坐标 lng,lat
    coord_z: jsonData.coord_z || 0,                            //高度(单位:米)
    coord_z_type: jsonData.coord_z_type || 0,                       //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
    always_show_label: jsonData.always_show_label != null ? jsonData.always_show_label : true,               //是否永远显示label, true:显示label(默认), false:不显示label
    show_label_range: jsonData.show_label_range || '0,2000',            //POI点显示label的范围(单位:米, 范围最小、最大距离; 在此范围内显示, 超出范围隐藏; 注意:always_show_label属性优先于此属性)
    sort_order: jsonData.sort_order != null ? jsonData.sort_order : false,                     //是否开启遮挡排序(根据POI点真实3D位置开启遮挡排序,
    //注: 只与其他开启排序的POI之间进行排序, 开启此排序会消耗性能(最多60个POI点同时开启排序)
    state: jsonData.state,                      //与marker之中images中的define_state对应
    marker: {
      size: jsonData.marker.size || '100,228',                   //marker大小(宽,高 单位:像素)
      images: []
    },
    params: jsonData.params || null,
  }
  if (jsonData.animation_type) {
    //this.$set(jo, 'animation_type', jsonData.animation_type)
    jo.animation_type = jsonData.animation_type
  }
  if (jsonData.duration_time) {
    //this.$set(jo, 'duration_time', jsonData.duration_time)
    jo.duration_time = jsonData.duration_time
  }
  // marker images
  let images = jsonData.marker.images
  if (images && images.length > 0) {
    images.forEach(item => {
      let image = {
        define_state: item.define_state,   //marker图片组
        normal_url: item.normal_url, // || 'http://superapi.51hitech.com/doc-static/images/static/markerNormal.png',        //normal 图片url地址
        activate_url: item.activate_url, // || 'http://superapi.51hitech.com/doc-static/images/static/markerActive.png'       //hover, active 图片url地址
        //本地图片地址一: "file:///D:/xxx/markerNormal.png",    D: 在线席位所在盘符
        //本地图片地址二: "path:/UserData/markerNormal.png",    图片资源由在线席位后台管理, 在线席位4.3.1以上版本
      }
      jo.marker.images.push(image)
    })
  }
  // label
  let label = jsonData.label
  if (label) {
    let labelJo = {
      bg_image_url: label.bg_image_url || 'http://superapi.51hitech.com/doc-static/images/static/LabelBg.png',
      //本地图片地址一: "file:///D:/xxx/LabelBg.png",    D: 在线席位所在盘符
      //本地图片地址二: "path:/UserData/LabelBg.png",    图片资源由在线席位后台管理, 在线席位4.3.1以上版本
      bg_size: label.bg_size || '120,30', //label大小(宽, 高 单位:像素)
      bg_offset: label.bg_offset || '30,100', //整个label左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
      content: []
    }
    let content = label.content
    if (content && content.length > 0) {
      content.forEach(item => {
        let t = {
          text: item.text,   //[文本内容, HEXA颜色, 文本大小]
          text_offset: item.text_offset || '5,5',     //文本框左上角相对于label中bg_offset左上角的margin偏移(x,y 单位:像素), 注: x为正向右, y为正向下
          text_boxwidth: item.text_boxwidth || 110,      //文本框宽度
          text_centered: item.text_centered != null ? item.text_centered : false,   //文本居中(true:居中; false:不居中)
          scroll_speed: item.scroll_speed || 1         //文本滚动速度(0:不滚动; 注: "text_centered":true优先)
        }
        labelJo.content.push(t)
      })
    }
    //this.$set(jo, 'label', labelJo)
    jo.label = labelJo
  }
  // window
  let windowData = jsonData.window
  if (windowData) {
    let windowJo = {
      url: windowData.url || 'http://superapi.51hitech.com/doc-static/images/static/video.html',
      //本地地址一: "file:///D:/xxx/echarts.html",    D: 在线席位所在盘符
      size: windowData.size || '520,350',      //window大小(宽,高 单位:像素)
      offset: windowData.offset || '50,180'      //window左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
    }
    jo.window = windowJo
  }
  return jo
}

function getAngle(lng_a, lat_a, lng_b, lat_b) {
  var a = (90 - lat_b) * Math.PI / 180;
  var b = (90 - lat_a) * Math.PI / 180;
  var AOC_BOC = (lng_b - lng_a) * Math.PI / 180;
  var cosc = Math.cos(a) * Math.cos(b) + Math.sin(a) * Math.sin(b) * Math.cos(AOC_BOC);
  var sinc = Math.sqrt(1 - cosc * cosc);
  var sinA = Math.sin(a) * Math.sin(AOC_BOC) / sinc;
  var A = Math.asin(sinA) * 180 / Math.PI;
  var res = 0;
  if (lng_b > lng_a && lat_b > lat_a) res = A;
  else if (lng_b > lng_a && lat_b < lat_a) res = 180 - A;
  else if (lng_b < lng_a && lat_b < lat_a) res = 180 - A;
  else if (lng_b < lng_a && lat_b > lat_a) res = 360 + A;
  else if (lng_b > lng_a && lat_b == lat_a) res = 90;
  else if (lng_b < lng_a && lat_b == lat_a) res = 270;
  else if (lng_b == lng_a && lat_b > lat_a) res = 0;
  else if (lng_b == lng_a && lat_b < lat_a) res = 180;
  return res;
}

/**
 *
 * @param {Array || Object} jsonData
 * 自定义添加poi点
 */
function AddCustomPOI(jsonData) {
  if (jsonData instanceof Array && jsonData.length > 0) {
    jsonData.forEach(poiObject => {
      const jo = customPoi(poiObject)
      window.cloudRender.SuperAPI("AddCustomPOI", jo, (status) => {
        console.log('AddCustomPOI arr', status)
      })
    })
  } else if (typeof jsonData == 'object') {
    let jo = customPoi(poiObject)
    window.cloudRender.SuperAPI('AddCustomPOI', jo, (status) => {
    })
  }
}

/**
 *
 * @param {Object} poiObject
 * @returns {Object} jsondata
 * id, coord, coord_z, coor_z_type, marker, label is necessary
 * @param {string} poiObject.id
 * @param {number} poiObject.coord_type 坐标类型(0:经纬度坐标, 1:cad坐标)
 * @param {boolean} poiObject.adaptive true:自适应大小;false:默认
 * @param {string} poiObject.coord poi点的坐标（lng，lat)
 * @param {string} poiObject.cad_mapkey CAD基准点的Key值, 项目中约定
 * @param {number} poiObject.coord_z 高度（单位：米）
 * @param {number} poiObject.coord_z_type //坐标高度类型
 * @param {boolean} poiObject.always_show_label 是否永远显示label, true:显示label(默认), false:不显示label
 * @param {string} poiObject.show_label_range POI点显示label的范围(单位:米, 范围最小、最大距离; 在此范围内显示, 超出范围隐藏; 注意:always_show_label属性优先于此属性)
 * @param {boolean} poiObject.sort_order 是否开启遮挡排序(根据POI点真实3D位置开启遮挡排序,注: 只与其他开启排序的POI之间进行排序, 开启此排序会消耗性能(最多60个POI点同时开启排序)
 * @param {string} poiObject.animation_type 动画类型(bounce:弹出式; stretch:伸缩式; wipe:展开式)
 * @param {number} poiObject.duration_time 规定完成动画所花费的时间(单位:秒)
 * @param {string} poiObject.state 与marker之中images中的define_state对应
 * @param {Array} poiObject.images //marker图片组
 * @param {string} poiObject.markerSize marker大小(宽,高 单位:像素)
 * @param {string} poiObject.markerDefine_state marker图片组
 * @param {string} poiObject.markerNormal_url mormal图片url地址
 * @param {string} poiObject.markertActivate_url hover, active图片地址
 * @param {Array} poiObject.content label内容
 * @param {string} poiObject.labelBg_image_url 'label标签背景图片'
 * @param {string} poiObject.labelsize label大小(宽, 高 单位:像素)
 * @param {string} poiObject.labelBg_offset //整个label左上角相对于maeker的中心点
 * @param {Array} poiObject.contentText [文本内容, hexa颜色，文本大小]
 * @param {string} poiObject.contentText_offset 文本框左上角相对于label中bg_offset左上角的margin偏移(x,y 单位:像素), 注: x为正向右, y为正向下
 * @param {number} poiObject.contentText_boxwidth 文本框宽度
 * @param {boolean} poiObject.contentText_centered 文本居中(true:居中; false:不居中)
 * @param {number} poiObject.contentScroll_speed 文本滚动速度(0:不滚动; 注: "text_centered":true优先)
 * @param {string} poiObject.windowUrl
 * @param {string} poiObject.windowSize //window大小
 * @param {string} poiObject.windowOffset //window左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
 */
function customPoi(poiObject = {}) {
  const jsondata = {
    id: poiObject.id,
    coord_type: poiObject.coord_type || 0,
    adaptive: poiObject.adaptive || false,
    cad_mapkey: poiObject.cad_mapkey || "",
    coord: poiObject.coord,
    coord_z: poiObject.coord_z,
    coord_z_type: poiObject.coord_z_type || 0,
    always_show_label: poiObject.always_show_label !== null ? poiObject.always_show_label : false,
    show_label_range: typeof poiObject.show_label_range === "string" ? poiObject.show_label_range : '0,2000',            //POI点显示label的范围(单位:米, 范围最小、最大距离; 在此范围内显示, 超出范围隐藏; 注意:always_show_label属性优先于此属性)
    sort_order: poiObject.sort_order != null ? poiObject.sort_order : false,
    animation_type: poiObject.animation_type !== undefined ? poiObject.animation_type : "bounce",
    duration_time: poiObject.duration_time > 0 ? poiObject.duration_time : 0.7,
    state: poiObject.state || "state_1",                      //与marker之中images中的define_state对应
    marker: {
      size: poiObject.markerSize || '100,228',                   //marker大小(宽,高 单位:像素)
      images: poiObject.images || []
    },
    label: {
      bg_image_url: poiObject.labelBg_image_url || "http://superapi.51hitech.com/doc-static/images/static/LabelBg.png",
      bg_size: poiObject.balbelBg_size || "100,80",
      bg_offset: poiObject.labelBg_offset || "50,200",
      content: poiObject.content || []
    },
    window: {
      url: poiObject.windowUrl || "http://superapi.51hitech.com/doc-static/images/static/video.html",
      size: poiObject.windowSize || "520,350",
      offset: poiObject.windowOffset || "50,180"
    },
  };
  let images = jsondata.marker.images;
  if (images instanceof Array) {
    images.forEach((item) => {
      item.push({
        define_state: poiObject.state || "state_1",
        normal_url: poiObject.markerNormal_url || "http://superapi.51hitech.com/doc-static/images/static/markerNormal.png",
        activate_url: poiObject.markerActivate_url || "http://superapi.51hitech.com/doc-static/images/static/markerActive.png"
      })
    })
  }
  let content = jsondata.label.content
  if (content instanceof Array) {
    content.forEach((item) => {
      item.push({
        text: poiObject.contentText,
        text_offset: poiObject.contentText_offset || "10,5",
        text_boxwidth: poiObject.contentText[0].length * 16 || 80,
        text_centered: poiObject.contentText_centered || false,
        scroll_speed: poiObject.contentScroll_speed || 0
      })
    })
  }
  return jsondata
}

export {
  digitalTwinApi,
  personModelInfo,
  AddCustomPOI,

}
