<template>
  <div
    v-loading="loading"
    :element-loading-text="tips"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
  >
    <div id="container" ref="container" :style="'height: ' + height + ';'"></div>
  </div>
</template>

<script>
import {play, ptz} from "@/api/wvs";

export default {
  name: "WvsPlayer",
  props: {
    deviceId: {
      type: String,
      required: true
    },
    channelId: {
      type: String,
      required: true
    },
    protocol: {
      type: String,
      required: true
    },
    height: {
      type: String,
      default: "600px"
    }
  },
  data() {
    return {
      loading: false,
      jessibuca: null
    }
  },
  computed: {
    tips() {
      return this.protocol === "I1" ? "该类型设备首次播放需要唤醒，请耐心等待..." : "数据请求中...";
    },
    change() {
      let {deviceId, channelId, protocol} = this;
      return {deviceId, channelId, protocol};
    }
  },
  watch: {
    change(value) {
      let {deviceId, channelId, protocol} = value;
      this.stop().then(() => {
        this.create({});
        this.play(deviceId, channelId, protocol);
      })
    }
  },
  mounted() {
    this.create({});
    this.play(this.deviceId, this.channelId, this.protocol);
  },
  async beforeDestroy() {
    await this.stop();
    console.log("WvsPlayer -> Stop.");
  },
  methods: {
    create(options) {
      options = options || {};
      this.jessibuca = new window.JessibucaPro(
        {
          container: this.$refs.container,
          videoBuffer: 0.2,
          videoBufferDelay: 1,
          decoder: "/wvs/js/decoder-pro.js",
          forceNoOffscreen: true,
          hiddenAutoPause: false,
          hasAudio: true,
          rotate: 0,
          isResize: false,
          isFullResize: false,
          isFlv: false,
          debug: false,
          debugLevel: "debug",
          timeout: 10,
          heartTimeout: 5,
          heartTimeoutReplay: true,
          heartTimeoutReplayTimes: 3,
          loadingTimeout: 10,
          loadingTimeoutReplay: true,
          loadingTimeoutReplayTimes: 3,
          supportDblclickFullscreen: true,
          showBandwidth: true,
          operateBtns: {
            fullscreen: true,
            screenshot: true,
            play: false,
            audio: true,
            record: true,
            performance: true,
            ptz: true
          },
          extendOperateBtns: [
            {
              name: "close",
              index: 99,
              icon: "/wvs/assets/close.png",
              iconTitle: "关闭",
              click: () => {
                this.stop();
              }
            },
          ],
          keepScreenOn: false,
          isNotMute: true,
          loadingText: "视频加载中...",
          background: "",
          useMSE: true,
          useWCS: true,
          wcsUseVideoRender: true,
          autoWasm: true,
          hotKey: true,
          wasmDecodeErrorReplay: true,
          controlAutoHide: true,
          recordType: "mp4",
          useWebFullScreen: false,

          text: "",
          decoderErrorAutoWasm: true,
          useSIMD: true,
          useMThreading: true,
          showPerformance: false,
          useCanvasRender: false,
          useWebGPU: true,
          demuxUseWorker: true,
          mseDecoderUseWorker: true,

          ptzClickType: "mouseDownAndUp",
          ptzZoomShow: true,
          ptzMoreArrowShow: true,
          ptzApertureShow: false,
          ptzFocusShow: false,
          ptzCruiseShow: false,
          ptzFogShow: false,
          ptzWiperShow: false,
          ptzShowType: "vertical",
          ptzPositionConfig: {
            right: "20px",
            top: "20px"
          },
          ptzSupportDraggable: true,

          ...options
        }
      );

      const _this = this;

      this.jessibuca.on("videoInfo", function (data) {
        console.log("WvsPlayer -> videoInfo:", JSON.stringify(data));
      });

      this.jessibuca.on("audioInfo", function (data) {
        console.log("WvsPlayer -> audioInfo:", JSON.stringify(data));
      });

      this.jessibuca.on("log", function (data) {
        console.log("WvsPlayer -> log:", data);
      });

      this.jessibuca.on("error", function (error) {
        console.log("WvsPlayer -> error:", error);
      });

      this.jessibuca.on("performance", function (performance) {
        let show = "卡顿";
        if (performance === 2) {
          show = "非常流畅";
        } else if (performance === 1) {
          show = "流畅";
        }
        _this.performance = show;
      });

      this.jessibuca.on("stats", function (s) {
        _this.stats = s;
      });

      this.jessibuca.on("ptz", (arrow) => {
        console.log("WvsPlayer -> ptz:", arrow);
        _this.ptz(arrow);
      });
    },
    play(deviceId, channelId, protocol) {
      this.loading = true;
      const param = {
        deviceId: deviceId,
        channelId: channelId,
        protocol: protocol
      };
      play(param).then(response => {
        this.loading = false;

        let url;
        const isHttps = location.protocol === "https:";
        const isH264 = response.data.videoCodec === "H264";
        if (isH264) {
          url = isHttps ? response.data.rtcs.replace("https", "webrtc") : response.data.rtc.replace("http", "webrtc");
        } else {
          url = isHttps ? response.data.wssFlv : response.data.wsFlv;
        }
        console.log("WvsPlayer -> url:", url);

        return this.jessibuca.play(url);
      }).then(() => {
        console.log("WvsPlayer -> play success.");
      }).catch((e) => {
        console.error("WvsPlayer -> play error:", e);
        this.loading = false;
      });
    },
    stop() {
      if (this.jessibuca) {
        return this.jessibuca.destroy();
      }
    },
    ptz(command) {
      let speed = 80;
      switch (command) {
        case "leftUp":
          command = "upleft";
          break;
        case "leftDown":
          command = "downleft";
          break;
        case "rightUp":
          command = "upright";
          break;
        case "rightDown":
          command = "downright";
          break;
        case "zoomExpand":
          command = "zoomin";
          speed = 10;
          break;
        case "zoomNarrow":
          command = "zoomout";
          speed = 10;
          break;
      }
      const param = {
        deviceId: this.deviceId,
        channelId: this.channelId,
        command: command,
        horizonSpeed: speed,
        verticalSpeed: speed,
        zoomSpeed: speed
      }
      ptz(param);
    }
  }
}
</script>

<style scoped>
#container {
  width: 100%;
  background-color: rgba(0, 0, 0, 1);
}
</style>
