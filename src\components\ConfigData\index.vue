<template>
  <span>{{ value }}</span>
</template>

<script>
import {isJSON} from "@/utils/zhy";

// TODO 待优化: 同一页面多次使用同一配置时调用一次接口
export default {
  name: "ConfigData",
  props: {
    // 参数键名
    configKey: {
      type: String,
      required: true
    },
    // JSON 键名
    valueKey: {
      type: String
    }
  },
  data() {
    return {
      value: undefined
    }
  },
  created() {
    this.getConfigKey(this.configKey).then(response => {
      let valueStr = response.msg;
      if (this.valueKey) {
        if (isJSON(valueStr)) {
          let keys = this.valueKey.split(",");
          let tmp = JSON.parse(valueStr);
          for (let i = 0; i < keys.length; i++) {
              tmp = tmp?.[keys[i]];
          }
          this.value = tmp;
        } else {
          this.value = undefined;
        }
      } else {
        this.value = valueStr;
      }
    });
  }
}
</script>
