<!--
@File    index.vue
@Desc    自定义数字输入组件.
<AUTHOR> href="mailto:<EMAIL>">xiaoQQya</a>
@Date    2023/10/10
-->
<template>
  <el-input-number
    v-bind:value="value"
    @input="input"
    :placeholder="placeholder"
    :precision="precision"
    :step="step"
    :min="nativeMin"
    :max="nativeMax"
    :disabled="disabled"
    style="width: 100%;"
  />
</template>

<script>
export default {
  name: "InputNumber",
  props: {
    value: {
      default: undefined
    },
    placeholder: {
      type: String,
      default: null
    },
    maxlength: {
      type: Number,
      default: 1,
      required: true
    },
    precision: {
      type: Number,
      default: 0
    },
    min: {
      type: Number,
      default: null
    },
    max: {
      type: Number,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      step: undefined,
      nativeMin: undefined,
      nativeMax: undefined
    }
  },
  created() {
    this.computePrecision();
  },
  methods: {
    input(value) {
      this.$emit("input", value);
    },
    computePrecision() {
      this.step = Math.pow(10, -this.precision);
      this.nativeMax = this.max !== null ? this.max : Math.pow(10, (this.maxlength - this.precision)) - this.step;
      this.nativeMin = this.min !== null ? this.min : -(Math.pow(10, (this.maxlength - this.precision)) - this.step);
    }
  }
}
</script>
