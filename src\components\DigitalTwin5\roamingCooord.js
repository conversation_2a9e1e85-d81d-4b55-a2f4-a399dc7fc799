const roamingCoords = [
  {
    "index": "0",
    "coord": "119.758705,31.218878",
    "coord_z": "44.599979"
  },
  {
    "index": "1",
    "coord": "119.758598,31.21903",
    "coord_z": "44.599998"
  },
  {
    "index": "2",
    "coord": "119.758102,31.21969",
    "coord_z": "44.599998"
  },
  {
    "index": "3",
    "coord": "119.757828,31.220057",
    "coord_z": "44.599998"
  },
  {
    "index": "4",
    "coord": "119.757492,31.220501",
    "coord_z": "44.599998"
  },
  {
    "index": "5",
    "coord": "119.756432,31.221926",
    "coord_z": "44.599998"
  }
  /*{
    "index": "1",
    "coord": "119.760696,31.216215",
    "coord_z": "44.599991"
  },
  {
    "index": "2",
    "coord": "119.760628,31.216307",
    "coord_z": "44.599995"
  },
  {
    "index": "3",
    "coord": "119.760536,31.216429",
    "coord_z": "44.599998"
  },
  {
    "index": "4",
    "coord": "119.760445,31.216553",
    "coord_z": "44.599995"
  },
  {
    "index": "5",
    "coord": "119.760323,31.216715",
    "coord_z": "44.599995"
  },
  {
    "index": "6",
    "coord": "119.760216,31.216864",
    "coord_z": "44.599998"
  },
  {
    "index": "7",
    "coord": "119.760109,31.216999",
    "coord_z": "44.599998"
  },
  {
    "index": "8",
    "coord": "119.76001,31.217133",
    "coord_z": "44.599998"
  },
  {
    "index": "9",
    "coord": "119.759911,31.217266",
    "coord_z": "44.599998"
  },
  {
    "index": "10",
    "coord": "119.759789,31.217436",
    "coord_z": "44.599998"
  },
  {
    "index": "11",
    "coord": "119.759674,31.217588",
    "coord_z": "44.599998"
  },
  {
    "index": "12",
    "coord": "119.759575,31.217716",
    "coord_z": "44.599998"
  },
  {
    "index": "13",
    "coord": "119.75946,31.217869",
    "coord_z": "44.599998"
  },
  {
    "index": "14",
    "coord": "119.759354,31.218012",
    "coord_z": "44.599998"
  },
  {
    "index": "15",
    "coord": "119.759239,31.218172",
    "coord_z": "44.599998"
  },
  {
    "index": "16",
    "coord": "119.759109,31.218334",
    "coord_z": "44.599998"
  },
  {
    "index": "17",
    "coord": "119.758972,31.218525",
    "coord_z": "44.599998"
  },
  {
    "index": "18",
    "coord": "119.758835,31.218708",
    "coord_z": "44.599998"
  },
  {
    "index": "19",
    "coord": "119.75869,31.218903",
    "coord_z": "44.599998"
  },
  {
    "index": "20",
    "coord": "119.758537,31.21911",
    "coord_z": "44.599998"
  },
  {
    "index": "21",
    "coord": "119.758385,31.219315",
    "coord_z": "44.599998"
  },
  {
    "index": "22",
    "coord": "119.758202,31.219559",
    "coord_z": "44.599995"
  },
  {
    "index": "23",
    "coord": "119.758018,31.219803",
    "coord_z": "44.599998"
  },
  {
    "index": "24",
    "coord": "119.757828,31.220053",
    "coord_z": "44.599998"
  },
  {
    "index": "25",
    "coord": "119.757622,31.220329",
    "coord_z": "44.599991"
  },
  {
    "index": "26",
    "coord": "119.757423,31.220596",
    "coord_z": "44.599998"
  },
  {
    "index": "27",
    "coord": "119.757248,31.220835",
    "coord_z": "44.599998"
  },
  {
    "index": "28",
    "coord": "119.75708,31.221054",
    "coord_z": "44.599998"
  },
  {
    "index": "29",
    "coord": "119.75695,31.221228",
    "coord_z": "44.599991"
  },
  {
    "index": "30",
    "coord": "119.756805,31.221416",
    "coord_z": "44.599998"
  },
  {
    "index": "31",
    "coord": "119.756653,31.221626",
    "coord_z": "44.599998"
  },
  {
    "index": "32",
    "coord": "119.756493,31.221844",
    "coord_z": "44.599998"
  },
  {
    "index": "33",
    "coord": "119.756325,31.222073",
    "coord_z": "44.599995"
  },
  {
    "index": "34",
    "coord": "119.756165,31.222282",
    "coord_z": "44.599998"
  },
  {
    "index": "35",
    "coord": "119.755981,31.222515",
    "coord_z": "44.599998"
  },
  {
    "index": "36",
    "coord": "119.755814,31.222752",
    "coord_z": "44.599998"
  }*/
  /*{
    "index": "1",
    "coord": "119.763298,31.212721",
    "coord_z": "44.599998"
  },
  {
    "index": "2",
    "coord": "119.762749,31.213469",
    "coord_z": "44.599998"
  },
  {
    "index": "3",
    "coord": "119.762215,31.214186",
    "coord_z": "44.599998"
  },
  {
    "index": "4",
    "coord": "119.761566,31.215046",
    "coord_z": "44.599998"
  },
  {
    "index": "5",
    "coord": "119.760773,31.216112",
    "coord_z": "44.599998"
  },
  {
    "index": "6",
    "coord": "119.759865,31.217331",
    "coord_z": "44.599998"
  },
  {
    "index": "7",
    "coord": "119.758537,31.219101",
    "coord_z": "44.599998"
  },
  {
    "index": "8",
    "coord": "119.757973,31.219852",
    "coord_z": "44.600006"
  },
  {
    "index": "9",
    "coord": "119.757706,31.220211",
    "coord_z": "44.599998"
  },
  {
    "index": "10",
    "coord": "119.75676,31.221478",
    "coord_z": "44.599995"
  },
  {
    "index": "11",
    "coord": "119.755791,31.222778",
    "coord_z": "44.599995"
  }*/
]
  /*[
  // { index: '01', coord: '119.575096,31.240856', coord_z: '42.66003' },
  // { index: '02', coord: '119.575035,31.240889', coord_z: '42.660069' },
  // { index: '03', coord: '119.574974,31.240925', coord_z: '42.660038' },
  // { index: '04', coord: '119.574905,31.240957', coord_z: '42.660168' },
  // { index: '05', coord: '119.574844,31.240993', coord_z: '42.66016' },
  // { index: '06', coord: '119.574791,31.24103', coord_z: '42.660126' },
  // { index: '07', coord: '119.574722,31.241064', coord_z: '42.659943' },
  // { index: '08', coord: '119.574661,31.241102', coord_z: '42.660179' },
  // { index: '09', coord: '119.574608,31.24114', coord_z: '42.659912' },
  // { index: '10', coord: '119.574539,31.24118', coord_z: '42.660187' },
  // { index: '11', coord: '119.574478,31.241215', coord_z: '42.660049' },
  // { index: '12', coord: '119.574417,31.241253', coord_z: '42.660034' },
  // { index: '13', coord: '119.574356,31.241289', coord_z: '42.660069' },
  // { index: '14', coord: '119.574295,31.241325', coord_z: '42.660206' },
  // { index: '15', coord: '119.574234,31.241364', coord_z: '42.659981' },
  // { index: '16', coord: '119.574173,31.2414', coord_z: '42.660072' },
  // { index: '17', coord: '119.574112,31.241438', coord_z: '42.660088' },
  // { index: '18', coord: '119.574051,31.241474', coord_z: '42.660019' },
  // { index: '19', coord: '119.57399,31.24151', coord_z: '42.660049' },
  // { index: '20', coord: '119.573936,31.241547', coord_z: '42.66008' },
  // { index: '21', coord: '119.573776,31.241638', coord_z: '42.66011' },
  // { index: '22', coord: '119.573532,31.241791', coord_z: '42.660015' },
  // { index: '23', coord: '119.57328,31.24194', coord_z: '42.66011' },
  { index: '24', coord: '119.573036,31.242086', coord_z: '42.660126' },
  { index: '25', coord: '119.572823,31.242216', coord_z: '42.660069' },
  { index: '26', coord: '119.572617,31.242344', coord_z: '42.660107' },
  { index: '27', coord: '119.572372,31.242491', coord_z: '42.660084' },
  { index: '28', coord: '119.572121,31.242643', coord_z: '42.660023' },
  { index: '29', coord: '119.571899,31.242771', coord_z: '42.660072' },
  { index: '30', coord: '119.571686,31.242903', coord_z: '42.660053' },
  { index: '31', coord: '119.571419,31.243065', coord_z: '42.659985' },
  { index: '32', coord: '119.571175,31.243208', coord_z: '42.660019' },
  { index: '33', coord: '119.57093,31.243355', coord_z: '42.659988' },
  { index: '34', coord: '119.570694,31.243498', coord_z: '42.65995' },
  { index: '35', coord: '119.570457,31.243643', coord_z: '42.659981' },
  { index: '36', coord: '119.570221,31.243778', coord_z: '42.659977' },
  { index: '37', coord: '119.569984,31.243923', coord_z: '42.659946' },
  { index: '38', coord: '119.569733,31.244076', coord_z: '42.659927' },
  { index: '39', coord: '119.569489,31.244223', coord_z: '42.659878' },
  { index: '40', coord: '119.569359,31.244312', coord_z: '42.66' },
  { index: '41', coord: '119.569252,31.244532', coord_z: '42.659935' },
  { index: '42', coord: '119.569122,31.244801', coord_z: '42.659935' },
  { index: '43', coord: '119.569016,31.245024', coord_z: '42.659931' },
  { index: '44', coord: '119.568977,31.245066', coord_z: '42.659931' },
  { index: '45', coord: '119.568893,31.245131', coord_z: '42.659924' },
  { index: '46', coord: '119.568626,31.24523', coord_z: '42.659946' },
  { index: '47', coord: '119.568352,31.245333', coord_z: '42.659935' },
  { index: '48', coord: '119.568077,31.245438', coord_z: '42.659916' },
  { index: '49', coord: '119.56781,31.245537', coord_z: '42.659924' },
  { index: '50', coord: '119.567566,31.245628', coord_z: '42.659916' },
  { index: '51', coord: '119.567314,31.245716', coord_z: '42.659924' },
  { index: '52', coord: '119.567055,31.245817', coord_z: '42.659908' },
  { index: '53', coord: '119.566765,31.245918', coord_z: '42.659943' },
  { index: '54', coord: '119.566521,31.246008', coord_z: '42.659946' },
  { index: '55', coord: '119.566223,31.246111', coord_z: '42.659962' },
  { index: '56', coord: '119.566132,31.24613', coord_z: '42.659935' },
  { index: '57', coord: '119.565727,31.246101', coord_z: '42.660088' },
  { index: '58', coord: '119.565651,31.24605', coord_z: '42.660091' },
  { index: '59', coord: '119.565353,31.245644', coord_z: '42.660088' },
  { index: '60', coord: '119.565041,31.24552', coord_z: '42.660179' },
  { index: '61', coord: '119.564751,31.245419', coord_z: '42.659969' },
  { index: '62', coord: '119.564484,31.245323', coord_z: '42.660023' },
  { index: '63', coord: '119.564201,31.245222', coord_z: '42.660084' },
  { index: '64', coord: '119.563927,31.245125', coord_z: '42.660072' },
  { index: '65', coord: '119.563675,31.245039', coord_z: '42.660206' },
  { index: '66', coord: '119.563416,31.244942', coord_z: '42.659985' },
  { index: '67', coord: '119.56311,31.244827', coord_z: '42.660057' },
  { index: '68', coord: '119.562828,31.244726', coord_z: '42.660046' },
  { index: '69', coord: '119.562553,31.244631', coord_z: '42.660118' },
  { index: '70', coord: '119.562271,31.244526', coord_z: '42.660099' },
  { index: '71', coord: '119.561996,31.244429', coord_z: '42.660011' },
  { index: '72', coord: '119.561722,31.244331', coord_z: '42.660168' },
  { index: '73', coord: '119.56144,31.24423', coord_z: '42.66008' },
  { index: '74', coord: '119.561172,31.244135', coord_z: '42.660088' },
  { index: '75', coord: '119.560898,31.2440365', coord_z: '42.659927' },
  { index: '76', coord: '119.560616,31.243933', coord_z: '42.660133' },
  { index: '77', coord: '119.560333,31.243832', coord_z: '42.660122' },
  { index: '78', coord: '119.560066,31.243734', coord_z: '42.660088' },
  { index: '79', coord: '119.559792,31.243635', coord_z: '42.660141' },
  { index: '80', coord: '119.559517,31.24354', coord_z: '42.66' },
  { index: '81', coord: '119.559242,31.243439', coord_z: '42.660011' },
  { index: '82', coord: '119.558952,31.243334', coord_z: '42.660053' },
  { index: '83', coord: '119.558678,31.243235', coord_z: '42.660034' },
  { index: '84', coord: '119.558403,31.243147', coord_z: '42.660103' },
  { index: '85', coord: '119.558121,31.243074', coord_z: '42.660072' },
  { index: '86', coord: '119.557831,31.24301', coord_z: '42.660069' },
  { index: '87', coord: '119.557533,31.242937', coord_z: '42.660107' },
  { index: '88', coord: '119.557251,31.24287', coord_z: '42.660072' },
  { index: '89', coord: '119.556961,31.242792', coord_z: '42.66008' },
  { index: '90', coord: '119.556679,31.242733', coord_z: '42.660091' },
  { index: '91', coord: '119.556381,31.242661', coord_z: '42.660099' },
  { index: '92', coord: '119.556107,31.242579', coord_z: '42.660088' },
  { index: '93', coord: '119.555824,31.242451', coord_z: '42.660088' },
  { index: '94', coord: '119.555588,31.242308', coord_z: '42.660107' },
  { index: '95', coord: '119.555344,31.242157', coord_z: '42.660099' },
  { index: '96', coord: '119.555099,31.242006', coord_z: '42.660072' },
  { index: '97', coord: '119.554855,31.241852', coord_z: '42.660088' },
  { index: '98', coord: '119.554703,31.241693', coord_z: '42.660091' },
  { index: '99', coord: '119.554596,31.241457', coord_z: '42.660023' },
  { index: '100', coord: '119.554466,31.24119', coord_z: '42.66011' },
  { index: '101', coord: '119.554352,31.240953', coord_z: '42.660126' },
  { index: '102', coord: '119.554245,31.240725', coord_z: '42.66008' },
  { index: '103', coord: '119.554115,31.240479', coord_z: '42.660088' },
  { index: '104', coord: '119.554008,31.240248', coord_z: '42.660126' },
  { index: '105', coord: '119.553894,31.240011', coord_z: '42.660057' },
  { index: '106', coord: '119.55378,31.239779', coord_z: '42.660118' },
  { index: '107', coord: '119.553642,31.239548', coord_z: '42.660091' },
  { index: '108', coord: '119.553459,31.239346', coord_z: '42.660145' },
  { index: '109', coord: '119.553268,31.239145', coord_z: '42.66011' },
  { index: '110', coord: '119.553078,31.238939', coord_z: '42.660084' },
  { index: '111', coord: '119.552864,31.238714', coord_z: '42.660103' },
  { index: '112', coord: '119.552689,31.238516', coord_z: '42.660019' },
  { index: '113', coord: '119.55249,31.23831', coord_z: '42.660942' },
  { index: '114', coord: '119.552292,31.238089', coord_z: '42.660122' },
  { index: '115', coord: '119.552132,31.237921', coord_z: '42.660091' },

]*/

const roamingBoard = [
  {index: '0', coord:"119.757805,31.220032",coord_z:"42.131893"},
  {index: '1', coord: '119.761078,31.213329', coord_z: '28.383595'},
  {index: '2', coord: '119.760033,31.214685', coord_z: '31.033907'},
  {index: '3', coord: '119.758896,31.216291', coord_z: '31.299297'},
  {index: '4', coord: '119.757828,31.217716', coord_z: '28.078829'},
  {index: '5', coord: '119.75502,31.219761', coord_z: '31.547266'},
  {index: '6', coord: '119.75415,31.218014', coord_z: '29.259062'},
  {index: '7', coord: '119.755539,31.215586', coord_z: '27.638985'},
  {index: '8', coord: '119.758026,31.21328', coord_z: '30.703907'},
  {index: '9', coord: '119.75972,31.211655', coord_z: '35.816639'},
  {index:"10", coord:"119.757805,31.220032",coord_z:"42.131893"},
]

const xzRoamingCoords = [
  {
    "coord": "119.565575,31.250856",        //路径坐标点 lng,lat
    "coord_z": 2.467129,                        //高度(单位:米, cad坐标无效)
    "coord_easetype": "curve",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
    "arm_distance": 1026.849487,                    //镜头与坐标点距离(单位:米)
    "pitch": 12.114562,                           //镜头俯仰角(0~89)
    "yaw": 174.0,                             //镜头偏航角(0正北, 0~359)
    "attitude_easetype": "CircularInOut",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
    "time": 4,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
    "speed_easetype": "linear"             //镜头漫游速度类型(见下表)
  },
  {
    "coord": "119.565529,31.248425",        //路径坐标点 lng,lat
    "coord_z": 0.033848,                        //高度(单位:米, cad坐标无效)
    "coord_easetype": "curve",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
    "arm_distance": 689.273621,                    //镜头与坐标点距离(单位:米)
    "pitch": 12.114562,                           //镜头俯仰角(0~89)
    "yaw": 174.0,                             //镜头偏航角(0正北, 0~359)
    "attitude_easetype": "CircularInOut",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
    "time": 2,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
    "speed_easetype": "linear"             //镜头漫游速度类型(见下表)
  }, {
    "coord": "119.56591,31.246084",        //路径坐标点 lng,lat
    "coord_z": 48.493919,                        //高度(单位:米, cad坐标无效)
    "coord_easetype": "curve",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
    "arm_distance": 383.133606,                    //镜头与坐标点距离(单位:米)
    "pitch": 12.114562,                           //镜头俯仰角(0~89)
    "yaw": 174.0,                             //镜头偏航角(0正北, 0~359)
    "attitude_easetype": "CircularInOut",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
    "time": 2,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
    "speed_easetype": "linear"             //镜头漫游速度类型(见下表)
  },
  // {
  //   "coord": "119.56591,31.246084",        //路径坐标点 lng,lat
  //   "coord_z":48.493919,                         //高度(单位:米, cad坐标无效)
  //   "coord_easetype": "curve",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
  //   "arm_distance": 252.24762,                    //镜头与坐标点距离(单位:米)
  //   "pitch": 12.114562,                           //镜头俯仰角(0~89)
  //   "yaw": 174.0,                             //镜头偏航角(0正北, 0~359)
  //   "attitude_easetype": "CircularInOut",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
  //   "time": 2,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
  //   "speed_easetype": "linear"             //镜头漫游速度类型(见下表)
  // },
  {
    "coord": "119.565216,31.245586",        //路径坐标点 lng,lat
    "coord_z": 45.46809,                        //高度(单位:米, cad坐标无效)
    "coord_easetype": "curve",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
    "arm_distance": 186.392303,                    //镜头与坐标点距离(单位:米)
    "pitch": 31.318909,                           //镜头俯仰角(0~89)
    "yaw": 251.0,                             //镜头偏航角(0正北, 0~359)
    "attitude_easetype": "CircularInOut",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
    "time": 2,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
    "speed_easetype": "linear"             //镜头漫游速度类型(见下表)
  },
  {
    "coord": '119.564034,31.24514',        //路径坐标点 lng,lat
    "coord_z": 46.159,                        //高度(单位:米, cad坐标无效)
    "coord_easetype": "linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
    "arm_distance": 186.392303,                    //镜头与坐标点距离(单位:米)
    "pitch": 31.318909,                           //镜头俯仰角(0~89)
    "yaw": 251.0,                             //镜头偏航角(0正北, 0~359)
    "attitude_easetype": "Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
    "time": 1,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
    "speed_easetype": "linear"             //镜头漫游速度类型(见下表)
  },
  //////////////////



  // {
  //   coord: '116.955627,36.487457', coord_z: '163.296158',                        //高度(单位:米, cad坐标无效)
  //   "coord_easetype": "linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
  //   "arm_distance": 5,                    //镜头与坐标点距离(单位:米)
  //   "pitch": 18.170074,                           //镜头俯仰角(0~89)
  //   "yaw": 218,                              //镜头偏航角(0正北, 0~359)
  //   "attitude_easetype": "Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
  //   "time": 2,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
  //   "speed_easetype": "linear"             //镜头漫游速度类型(见下表)
  // },
  {
    coord: '119.561623,31.244278', coord_z: '45.746075',                        //高度(单位:米, cad坐标无效)
    "coord_easetype": "linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
    "arm_distance": 186.392303,                    //镜头与坐标点距离(单位:米)
    "pitch": 31.318909,                           //镜头俯仰角(0~89)
    "yaw": 251.0,                              //镜头偏航角(0正北, 0~359)
    "attitude_easetype": "Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
    "time": 2,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
    "speed_easetype": "linear"             //镜头漫游速度类型(见下表)
  },
  {
    coord: '119.559082,31.243412', coord_z: '48.695709',                        //高度(单位:米, cad坐标无效)
    "coord_easetype": "linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
    "arm_distance": 207.766998,                    //镜头与坐标点距离(单位:米)
    "pitch": 12.114502,                            //镜头俯仰角(0~89)
    "yaw": 268.0,                              //镜头偏航角(0正北, 0~359)
    "attitude_easetype": "Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
    "time": 2,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
    "speed_easetype": "linear"             //镜头漫游速度类型(见下表)
  },
  {
    coord: '119.556374,31.242643', coord_z: '42.541492',                        //高度(单位:米, cad坐标无效)
    "coord_easetype": "linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
    "arm_distance": 207.766998,                    //镜头与坐标点距离(单位:米)
    "pitch": 12.114502,                            //镜头俯仰角(0~89)
    "yaw": 268.0,                              //镜头偏航角(0正北, 0~359)
    "attitude_easetype": "Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
    "time": 2,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
    "speed_easetype": "linear"             //镜头漫游速度类型(见下表)
  },
  {
    coord: '119.554581,31.241375', coord_z: '43.526703',                        //高度(单位:米, cad坐标无效)
    "coord_easetype": "linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
    "arm_distance": 207.766998,                    //镜头与坐标点距离(单位:米)
    "pitch": 18.61322,                            //镜头俯仰角(0~89)
    "yaw": 260.0,                              //镜头偏航角(0正北, 0~359)
    "attitude_easetype": "Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
    "time": 2,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
    "speed_easetype": "linear"             //镜头漫游速度类型(见下表)
  },
  {
    coord: '119.55304,31.238871', coord_z: '45.733856',                        //高度(单位:米, cad坐标无效)
    "coord_easetype": "linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
    "arm_distance": 207.766998,                    //镜头与坐标点距离(单位:米)
    "pitch": 18.61322,                            //镜头俯仰角(0~89)
    "yaw": 255.0,                              //镜头偏航角(0正北, 0~359)
    "attitude_easetype": "Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
    "time": 2,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
    "speed_easetype": "linear"             //镜头漫游速度类型(见下表)
  },
  {
    coord: '119.553749,31.238565', coord_z: '31.255703',                        //高度(单位:米, cad坐标无效)
    "coord_easetype": "linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
    "arm_distance": 516.153748,                    //镜头与坐标点距离(单位:米)
    "pitch": 27.878265,                            //镜头俯仰角(0~89)
    "yaw": 72.0,                              //镜头偏航角(0正北, 0~359)
    "attitude_easetype": "Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
    "time": 2,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
    "speed_easetype": "linear"             //镜头漫游速度类型(见下表)
  },
  {
    coord: '119.558418,31.240568', coord_z: '29.00',                        //高度(单位:米, cad坐标无效)
    "coord_easetype": "linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
    "arm_distance": 516.153748,                    //镜头与坐标点距离(单位:米)
    "pitch": 27.878265,                            //镜头俯仰角(0~89)
    "yaw": 72.0,                              //镜头偏航角(0正北, 0~359)
    "attitude_easetype": "Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
    "time": 4,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
    "speed_easetype": "linear"             //镜头漫游速度类型(见下表)
  },
  {
    coord: '119.564606,31.24342', coord_z: '28.999',                        //高度(单位:米, cad坐标无效)
    "coord_easetype": "linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
    "arm_distance": 516.153748,                    //镜头与坐标点距离(单位:米)
    "pitch": 27.878265,                            //镜头俯仰角(0~89)
    "yaw": 72.0,                              //镜头偏航角(0正北, 0~359)
    "attitude_easetype": "Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
    "time": 2,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
    "speed_easetype": "linear"             //镜头漫游速度类型(见下表)
  },
  {
    coord: '119.567184,31.245762', coord_z: '45.454979',                        //高度(单位:米, cad坐标无效)
    "coord_easetype": "linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
    "arm_distance": 388.144989,                    //镜头与坐标点距离(单位:米)
    "pitch": 27.495941,                            //镜头俯仰角(0~89)
    "yaw": 141.0,                              //镜头偏航角(0正北, 0~359)
    "attitude_easetype": "Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
    "time": 2,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
    "speed_easetype": "linear"             //镜头漫游速度类型(见下表)
  },
  {
    coord: '119.569633,31.244141', coord_z: '46.534843',                        //高度(单位:米, cad坐标无效)
    "coord_easetype": "linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
    "arm_distance": 353.329987,                    //镜头与坐标点距离(单位:米)
    "pitch": 27.495941,                            //镜头俯仰角(0~89)
    "yaw": 141.0,                              //镜头偏航角(0正北, 0~359)
    "attitude_easetype": "Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
    "time": 2,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
    "speed_easetype": "linear"             //镜头漫游速度类型(见下表)
  },
  {
    coord: '119.573296,31.241905', coord_z: '44.516758',                        //高度(单位:米, cad坐标无效)
    "coord_easetype": "linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
    "arm_distance": 353.329987,                    //镜头与坐标点距离(单位:米)
    "pitch": 27.495941,                            //镜头俯仰角(0~89)
    "yaw": 141.0,                              //镜头偏航角(0正北, 0~359)
    "attitude_easetype": "Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
    "time": 2,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
    "speed_easetype": "linear"             //镜头漫游速度类型(见下表)
  },
  {
    coord: '119.566074,31.245175', coord_z: '45.021553',                        //高度(单位:米, cad坐标无效)
    "coord_easetype": "linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
    "arm_distance": 291,                    //镜头与坐标点距离(单位:米)
    "pitch": 12.114562,                            //镜头俯仰角(0~89)
    "yaw": 174,                              //镜头偏航角(0正北, 0~359)
    "attitude_easetype": "Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
    "time": 2,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
    "speed_easetype": "linear"             //镜头漫游速度类型(见下表)
  },
  // {
  //   coord: '116.955208,36.487064', coord_z: '166.857895',                        //高度(单位:米, cad坐标无效)
  //   "coord_easetype": "linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
  //   "arm_distance": 11,                    //镜头与坐标点距离(单位:米)
  //   "pitch": 18.170074,                            //镜头俯仰角(0~89)
  //   "yaw": 218,                              //镜头偏航角(0正北, 0~359)
  //   "attitude_easetype": "Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
  //   "time": 2,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
  //   "speed_easetype": "linear"             //镜头漫游速度类型(见下表)
  // },
  // {
  //   coord: '116.955185,36.48703', coord_z: '166.856445',                        //高度(单位:米, cad坐标无效)
  //   "coord_easetype": "linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
  //   "arm_distance": 11,                    //镜头与坐标点距离(单位:米)
  //   "pitch": 18.170074,                            //镜头俯仰角(0~89)
  //   "yaw": 218,                              //镜头偏航角(0正北, 0~359)
  //   "attitude_easetype": "Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
  //   "time": 2,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
  //   "speed_easetype": "linear"             //镜头漫游速度类型(见下表)
  // },
  // {
  //   coord: '116.955147,36.486996', coord_z: '166.856445',                        //高度(单位:米, cad坐标无效)
  //   "coord_easetype": "linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
  //   "arm_distance": 11,                    //镜头与坐标点距离(单位:米)
  //   "pitch": 18.170074,                            //镜头俯仰角(0~89)
  //   "yaw": 218,                              //镜头偏航角(0正北, 0~359)
  //   "attitude_easetype": "Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
  //   "time": 2,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
  //   "speed_easetype": "linear"             //镜头漫游速度类型(见下表)
  // },
  // {
  //   coord: '116.955132,36.486973', coord_z: '166.856445',                        //高度(单位:米, cad坐标无效)
  //   "coord_easetype": "linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
  //   "arm_distance": 11,                    //镜头与坐标点距离(单位:米)
  //   "pitch": 18.170074,                            //镜头俯仰角(0~89)
  //   "yaw": 218,                              //镜头偏航角(0正北, 0~359)
  //   "attitude_easetype": "Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
  //   "time": 2,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
  //   "speed_easetype": "linear"             //镜头漫游速度类型(见下表)
  // },
  // {
  //   coord: '116.955101,36.486942', coord_z: '166.857849',                       //高度(单位:米, cad坐标无效)
  //   "coord_easetype": "linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
  //   "arm_distance": 11,                    //镜头与坐标点距离(单位:米)
  //   "pitch": 18.170074,                            //镜头俯仰角(0~89)
  //   "yaw": 218,                              //镜头偏航角(0正北, 0~359)
  //   "attitude_easetype": "Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
  //   "time": 2,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
  //   "speed_easetype": "linear"             //镜头漫游速度类型(见下表)
  // },
  // {
  //   coord: '116.95507,36.486904', coord_z: '166.856445',                      //高度(单位:米, cad坐标无效)
  //   "coord_easetype": "linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
  //   "arm_distance": 11,                    //镜头与坐标点距离(单位:米)
  //   "pitch": 18.170074,                            //镜头俯仰角(0~89)
  //   "yaw": 218,                              //镜头偏航角(0正北, 0~359)
  //   "attitude_easetype": "Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
  //   "time": 2,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
  //   "speed_easetype": "linear"             //镜头漫游速度类型(见下表)
  // },
  // {
  //   "coord": '116.95504,36.48687',        //路径坐标点 lng,lat
  //   "coord_z": 166.856445,                        //高度(单位:米, cad坐标无效)
  //   "coord_easetype": "linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
  //   "arm_distance": 11,                   //镜头与坐标点距离(单位:米)
  //   "pitch": 18.170074,                            //镜头俯仰角(0~89)
  //   "yaw": 218,                              //镜头偏航角(0正北, 0~359)
  //   "attitude_easetype": "Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
  //   "time": 2,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
  //   "speed_easetype": "linear"             //镜头漫游速度类型(见下表)
  // }
]

export { roamingCoords, xzRoamingCoords, roamingBoard }
