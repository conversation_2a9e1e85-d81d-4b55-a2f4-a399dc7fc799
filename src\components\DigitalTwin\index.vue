<template>
  <div class="ue4map" ref="Twin" v-loading="homeLoading"
    element-loading-text="演进数据加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.1)">
    <div id="player" @dblclick="dtDbClick"></div>
    <i
      class="el-icon-setting"
      @click="
        () => {
          showFuncBox = !showFuncBox;
        }
      "
      style="position: fixed; top: 70px; right: 15px; color: #fff; z-index: 9999"
    ></i>
    <div class="funcBtnBox" v-show="showFuncBox">
      <el-collapse accordion>
        <el-collapse-item title="WDP5功能测试">
          <el-button @click="getCameraInfo()">获取当前镜头信息</el-button>
        </el-collapse-item>
        <el-collapse-item title="地图标点">
          <el-button @click="startGetCoord(undefined, undefined, 2)"
            >开启地图标点</el-button
          >
          <el-button @click="endGetCoord()">关闭地图标点</el-button>
        </el-collapse-item>
        <el-collapse-item title="外设监控">
          <el-button @click="startKeyboard()">注册键盘事件</el-button>
          <el-button @click="removeKeyboard()">删除键盘事件</el-button>
        </el-collapse-item>
        <el-collapse-item title="镜头动作">
          <el-button @click="gateInnerInspection()">廊道巡查</el-button>
          <el-button @click="gateInnerPause()">暂停巡查</el-button>
          <el-button @click="gateInnerContinue()">继续巡查</el-button>
          <el-button @click="gateInnerStop()">停止巡查</el-button>
          <el-button @click="personInspection()">人工巡查</el-button>
          <el-button @click="inspectionPause()">暂停巡查</el-button>
          <el-button @click="inspectionContinue()">继续巡查</el-button>
          <el-button @click="inspectionStop()">停止巡查</el-button>
          <el-button @click="initUAVView()">初始化无人机巡查视角</el-button>
          <!-- <el-button @click="UAVInspection()">无人机巡航</el-button> -->

          <el-button @click="sceneRoam()">开始镜头漫游</el-button>
          <!-- <el-button @click="startCameraRoam()">开始镜头漫游</el-button> -->
          <el-button @click="setCameraRoamState('pause')">暂停镜头漫游</el-button>
          <el-button @click="setCameraRoamState('continue')">继续镜头漫游</el-button>
          <el-button @click="setCameraRoamState('stop')">结束镜头漫游</el-button>
          <el-button @click="setCameraRotate(50, 'clockwise')"
            >镜头绕场景中心旋转开始</el-button
          >
          <el-button @click="setCameraRotate(50, 'stop')"
            >镜头绕场景中心旋转结束</el-button
          >
          <el-button @click="getCameraParams()">获取当前镜头信息</el-button>
          <el-button @click="resetCameraSpace('default')">边界限制</el-button>
          <el-button @click="resetCameraSpace('free')">开发边界限制</el-button>
        </el-collapse-item>
        <el-collapse-item title="场景渲染">
          <el-button @click="initMap()">场景渲染</el-button>
          <el-button @click="stopRenderCloud()">停止渲染</el-button>
          <el-button @click="showHideUEFrameRate">显示/隐藏场景帧率</el-button>
          <el-button @click="setRenderQuality('low')">低场景渲染</el-button>
          <el-button @click="setRenderQuality('medium')">中场景渲染</el-button>
          <el-button @click="setRenderQuality('high')">高场景渲染</el-button>
          <el-button @click="setRenderQuality('epic')">超高场景渲染</el-button>
        </el-collapse-item>
        <el-collapse-item title="覆盖物">
          <el-button @click="addRoadHeat()">添加路径热力图</el-button>
          <el-button @click="updateRoadHeat()">更新路径热力图</el-button>
          <el-button @click="getFullSceenCoveringId()">获取屏幕内所有覆盖物ID</el-button>
          <el-button @click="updateCustomPOILabel()">更新label</el-button>
        </el-collapse-item>
        <el-collapse-item title="洪水演进">
          <el-button
            style="margin-left: 10px"
            @click="waterFloodFileLoadAndChangeGrid(name)"
            >1、网格下载及转置</el-button
          >
          <el-button @click="waterFloodDataUpload(1340, 20)"
            >2、淹没时序数据加载</el-button
          >
          <el-button @click="waterFloodPlay('floodsky')"
            >3、淹没过程播放水科院测试</el-button
          >
          <el-button @click="waterFloodPlay('flood40')">3、淹没过程播放40</el-button>
          <el-button @click="waterFloodPlay('flood200')">3、淹没过程播放200</el-button>
          <el-button @click="waterFloodPlayOne('true', 281, 'flood40')"
            >40展示281帧</el-button
          >
          <el-input
            v-model="firstZhenNum"
            style="margin-left: 10px; width: 80%"
          ></el-input>
          <el-button @click="waterFloodPlayOne('true', firstZhenNum, 'flood40')"
            >40展示以上帧</el-button
          >
          <el-button @click="waterFloodPlayOne('true', 281, 'flood200')"
            >200展示281帧</el-button
          >
          <el-input
            v-model="scendZhenNum"
            style="margin-left: 10px; width: 80%"
          ></el-input>
          <el-button @click="waterFloodPlayOne('true', scendZhenNum, 'floodsky')"
            >floodsky展示以上帧</el-button
          >
          <el-button @click="waterFloodDelateGrid(name)">删除转置网格</el-button>
          <!-- <el-button @click="addCustomPOI1(0)">添加label</el-button> -->
          <el-button @click="waterFlowSwitch(2)">水流开关</el-button>
          <el-button @click="waterFlowSwitch(3)">水流开关</el-button>
          <el-button @click="closeWaterFlowSwitch()">关闭水流开关</el-button>
          <el-button @click="gateSwitch()">闸门开关</el-button>
          <el-button @click="gateSwitchAndWaterFlowSwitch(true)"
            >闸门开启并开水花</el-button
          >
          <el-button @click="gateSwitchAndWaterFlowSwitch(false)"
            >闸门开启并关水花</el-button
          >
          <el-button @click="addWaterPolyline(0)">汛限水位线</el-button>
          <el-button @click="waterChange(38.5, 1)">抬升水面特定高度</el-button>
          <el-button @click="multiplePerspectives(0)">水库调度默认视角</el-button>
          <el-button @click="multiplePerspectives(1)">水库调度前方视角</el-button>
          <el-button @click="multiplePerspectives(2)">水库调度后方视角</el-button>
          <el-button @click="multiplePerspectives(3)">洪水演进默认视角</el-button>
          <el-button @click="multiplePerspectives(6)">库区默认视角</el-button>
          <el-button @click="deleteAllPoint()">清除所有点位</el-button>
          <el-button @click="openGateAndWater('gateid',true)">开启水闸及闸门效果</el-button>
          <el-button @click="openGateAndWater('gateid',false)">关闭水闸及闸门效果</el-button>
        </el-collapse-item>
        <el-collapse-item title="水面">
          <el-button @click="getCoveringInfo()">获取实体属性</el-button>
          <el-button @click="waterGoup()">水面上升</el-button>
          <el-button @click="WaterDrow()">水面下降</el-button>
        </el-collapse-item>
        <el-collapse-item title="闸门">

        </el-collapse-item>
        <el-collapse-item title="测量工具">
          <el-button @click="measureTool()">测量工具</el-button>
          <el-button @click="measureAngle()">测量角度工具</el-button>
          <el-button @click="measureLength()">测量长度工具</el-button>
          <el-button @click="measureArea()">测量面积工具</el-button>
          <el-button @click="showHideCompass(true)">显示指南针</el-button>
          <el-button @click="showHideCompass(false)">隐藏指南针</el-button>
        </el-collapse-item>
        <el-collapse-item title="水面">
          <el-button @click="getAESWaterObjects()">获取水实体EID</el-button>
          <el-button @click="getCoveringWaterInfo()">获取实体属性</el-button>
          <el-input
            v-model="waterObject.height"
            disabled
            placeholder="当前水面高度"
          ></el-input>
          <el-input v-model="waterObject.maxHeight" placeholder="水面最大高度"></el-input>
          <el-input v-model="waterObject.minHeight" placeholder="水面最小高度"></el-input>
          <p @click="setLocalStorage" class="localClass">本地缓存</p>
          <el-slider
            v-if="waterObject.height != ''"
            @change="sliderValChange"
            width="100px"
            style="margin-left: 20px"
            v-model="sliderVal"
            :min="Number(waterObject.minHeight)"
            :max="Number(waterObject.maxHeight)"
          ></el-slider>
          <!-- <el-button @click="waterGoup()">水面上升</el-button> -->
        </el-collapse-item>
        <!-- <el-collapse-item title="场景编辑">
             <el-button @click="getAESObjects()">获取实体EID</el-button>
         </el-collapse-item> -->
        <el-collapse-item title="区域轮廓">
          <el-button @click="geoRange(1)">添加geo区域轮廓1</el-button>
          <el-button @click="geoRange(2)">添加geo区域轮廓2</el-button>
          <!-- <el-button @click="handleGeoJsonFile()">处理geojson文件</el-button> -->
          <el-button @click="showHidePOI('geo_range_id1', 'range', true)"
            >显示Geo区域轮廓1</el-button
          >
          <el-button @click="showHidePOI('geo_range_id1', 'range', false)"
            >隐藏Geo区域轮廓1</el-button
          >
          <el-button @click="showHidePOI('geo_range_id2', 'range', true)"
            >显示Geo区域轮廓2</el-button
          >
          <el-button @click="showHidePOI('geo_range_id2', 'range', false)"
            >隐藏Geo区域轮廓2</el-button
          >
        </el-collapse-item>
        <el-collapse-item title="POI点">
          <el-row v-for="item in digitalCon.videoPoints" :key="item.id">
            <el-col :span="24" style="padding-left: 10px">
              <el-button @click="showHidePOI(item.id, 'poi', true)"
                >显示{{ item.label }}</el-button
              >
            </el-col>
            <el-col :span="24" style="padding-left: 10px">
              <el-button @click="showHidePOI(item.id, 'poi', false)"
                >隐藏{{ item.label }}</el-button
              >
            </el-col>
          </el-row>
        </el-collapse-item>
        <el-collapse-item title="天气">
          <el-button @click="switchWeather('Sunny')">晴天</el-button>
          <!-- <el-button @click="switchWeather('Cloudy')">多云</el-button> -->
          <!-- <el-button @click="switchWeather('PartlyCloudy')">少云</el-button> -->
          <el-button @click="switchWeather('Overcast')">阴天</el-button>
          <el-button @click="switchWeather('LightRain')">小雨</el-button>
          <!-- <el-button @click="switchWeather('ModerateRain')">中雨</el-button> -->
          <!-- <el-button @click="switchWeather('HeavyRain')">大雨</el-button> -->
          <el-button @click="switchWeather('LightSnow')">小雪</el-button>
          <!-- <el-button @click="switchWeather('ModerateSnow')">中雪</el-button> -->
          <!-- <el-button @click="switchWeather('HeavySnow')">大雪</el-button> -->
          <!-- <el-button @click="switchWeather('Foggy')">雾天</el-button> -->
          <!-- <el-button @click="switchWeather('Sand')">扬尘</el-button> -->
          <!-- <el-button @click="switchWeather('Haze')">雾霾</el-button> -->
          <!-- <el-button @click="switchWeather('auto')">实时天气</el-button> -->

        </el-collapse-item>
        <el-collapse-item title="时间">
          <el-button
            v-for="v in 24"
            :key="v"
            @click="switchDate((v === 24 ? 0 : v) + ':00')"
          >
            {{ v + ":00" }}
          </el-button>
        </el-collapse-item>
      </el-collapse>
    </div>
    <div v-if="showTree" style="right: 22%;top: 100px;transition: 0.8s;z-index: 100;position: fixed;">
      <layer-select
        :data="treeData"
        ref="layerSelectRef"
        :expandedKeys="treeExpandedKeys"
        :checkedKeys="treeCheckedKeys"
        @treeChangeSelect="treeChange"
        @treeNodeClick="treeNodeClick"
        @changeMapType="changeMapType"
      ></layer-select>
    </div>
    <div class="dt-title" v-if="showDtTitle">
      <div>数字孪生</div>
    </div>
    <div class="bj" v-if="loginAnimate">
      <div
        style="
          display: flex;
          flex-flow: column;
          align-items: center;
          justify-content: center;
          top: calc(50% - 135px);
          position: absolute;
          left: calc(50% - 130px);
          width: 260px;
        "
      >
        <div class="move">
          <img src="@/assets/images/digitalTwin/loading1.png" alt="" />
        </div>
        <div style="color: #fff; font-size: 1rem; width: 100%">
          <div class="loadingText">加载中...</div>
          <div class="loading">
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      :title="reconnectInfo.title"
      :visible.sync="reconnectInfo.open"
      width="450px"
      top="100px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form inline class="reconnect-class">
        <el-form-item>
          <label>{{ reconnectInfo.label }}</label>
        </el-form-item>
        <el-form-item label="服务地址">
          <el-select v-model="reconnectInfo.url">
            <el-option
              v-for="(u, index) in cloudUrls"
              :key="index"
              :label="u.label"
              :value="u.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button
          @click="
            () => {
              reconnectInfo.open = false;
            }
          "
          >取 消</el-button
        >
        <el-button type="primary" @click="reconnectRender">确 定</el-button>
      </div>
    </el-dialog>
    <videolog
      :data="infoLog.data"
      :id="infoLog.id"
      v-if="infoLog.resultshow"
      @escFullScreen="escFullScreen"
    ></videolog>
  </div>
</template>

<script>
import WdpApi from "wdpapi";
import cloudRenderer from "51superapi";
import Bus from "@/views/screenPage/bus.js";
import { digitalTwinApi, personModelInfo } from "@/components/DigitalTwin/digitalTwinUtils.js"
import axios from "axios";
import { getMapPointListByUrl, getDownStation } from "@/api/digitalTwin/digitalTwin";
import { listWvpDeviceChannel } from "@/api/admin/platformmanage/wvpDevice/wvpDevice";
import {
  roamingCoords,
  xzRoamingCoords,
} from "@/components/DigitalTwin/roamingCooord.js";
import { saveAs } from "file-saver";
import { listData } from "@/api/admin/platformmanage/section/section";
import szlcData from "/public/json/szlc.json";
// import { jsondataUp, uavInfoData } from "@/views/screen/components/common/Twinfun";
import {getSmtRasData } from "@/api/index";
import * as constants from "constants";
import LayerSelect from "@/views/map/components/mapViewer/LayerSelect.vue";
import commonDialogBox from "@/components/commonDialogBox/commonDialogBox.vue";
// import infoLog from "@/views/screen/polling/dialog/info";
// import videolog from "@/views/screen/polling/dialog/video";

import { getRenderList,getDataByUrl,getDataByUrlAndQuery} from "@/views/map/api/map/dt/common.js"
export default {
  name: "",
  components: {
    LayerSelect,
    commonDialogBox,
  },
  // mixins: [screenCommon],
  data() {
    return {
      homeLoading:false,
      scendZhenNum: 0,
      firstZhenNum: 0,
      controlManage: false,
      treeData: [],
      treeExpandedKeys: [],
      treeCheckedKeys: [],
      //视频弹窗
      videoOpen: false,
      //设备id
      deviceId: "",
      //通道id
      id: "",
      cloudRenderTimer: null, // 场景重新渲染定时器
      cloudRerenderSum: 0, // 重新渲染总次数 超过次数后不再重新渲染
      cloudRerenderCount: 0, // 单次重新渲染时请求次数 默认每次重连可请求三次
      cloudRender: null, //渲染对象
      shadeIss: true, //遮罩显示
      loginAnimate: true,
      percentage: 0,
      customColor: "#409eff",
      timer: "",
      showFuncBox: false,
      unInitPoiList: [],
      poiList: [],
      spdList: [],
      dmList: [],
      controlData: true,
      // 和风天气map
      weatherMap: {
        晴: "Sunny",
        晴天: "Sunny",
        多云: "Cloudy",
        少云: "PartlyCloudy",
        阴天: "Overcast",
        阴: "Overcast",
        小雨: "LightRain",
        中雨: "ModerateRain",
        大雨: "HeavyRain",
        雨夹雪: "LightSnow",
        小雪: "LightSnow",
        中雪: "ModerateSnow",
        大雪: "HeavySnow",
        雾天: "Foggy",
        雾: "Foggy",
        扬尘: "Sand",
        雾霾: "Haze",
      },
      // 和风天气城市名称
      weatherCityName: null,
      // 和风天气城市编码
      weatherCityCode: null,
      // 和风天气城市列表
      weatherCityList: [],
      // 镜头漫游点位数组
      cameraRoamPoints: [],
      zhamenheight: 8, //闸门初始上升高度
      zhamen_1_UpId: null,
      zhamen_1_DownId: null,
      zhamen_2_UpId: null,
      zhamen_2_DownId: null,
      zhamen_3_UpId: null,
      zhamen_3_DownId: null,
      zhamen_1_State: true,
      zhamen_2_State: true,
      zhamen_3_State: true,
      waterObject: {
        eid: "-9150751343265701460",
        lonlat: "",
        height: "",
        maxHeight: "",
        minHeight: "",
      },
      sliderVal: 0,
      oldSliderVal: 0,
      //闸门控制,
      value1: false,
      value2: false,
      value3: false,
      //管道控制
      value: [],
      poiId: [],
      optionsPipe: [
        {
          value: "pipeOperator",
          label: "管道操作",
          children: [
            {
              value: "pipeUp",
              label: "管道抬升",
            },
            {
              value: "pipeDown",
              label: "管道恢复",
            },
          ],
        },
        {
          value: "oldPipe",
          label: "旧管道",
          children: [
            {
              value: "oCsgs",
              label: "城市供水",
            },
            {
              value: "oNtgg",
              label: "农田灌溉",
            },
            {
              value: "oYCgg",
              label: "渔场供水",
            },
          ],
        },
        {
          value: "newPipe",
          label: "新管道",
          children: [
            {
              value: "nCsgs",
              label: "城市供水",
            },
            {
              value: "nNtgg",
              label: "农田灌溉",
            },
          ],
        },
        {
          value: "nsbd",
          label: "南水北调",
          children: [
            {
              value: "ns-gs",
              label: "双向供水",
            },
            {
              value: "ns-dgs",
              label: "单向供水",
            },
            {
              value: "xs-fs",
              label: "蓄放水",
            },
          ],
        },
        {
          value: "dm",
          label: "断面操作",
          children: [
            {
              value: "dmts",
              label: "断面提升",
            },
            {
              value: "dmhf",
              label: "断面恢复",
            },
          ],
        },
      ],
      options: {
        monitorsPoints: null,
      },
      visible: false,
      //控制放水洞显示
      state: false,
      holeShowFade: "放水洞隐藏",
      digitalCon: {},
      cloudUrls: [
        { label: "云服务", value: "https://vizservice-paas.51aes.com" },
        { label: "公司服务", value: "https://twin.zhywater.com:28889" },
        { label: "横山水库内网服务器", value: "http://*************:8889" },
        { label: "油车水库内网服务器", value: "http://************:8889" },
        { label: "区域专线服务器", value: "http://*************:8889" },
        { label: "区域专线连横山用", value: "http://************:8889" },
        { label: "区域专线连油车用", value: "http://************:8889" },
        { label: "本机地址https", value: "https://localhost:8889" },
        { label: "本机地址http", value: "http://localhost:8889" },
      ],
      reconnectInfo: {
        open: false,
        title: null,
        label: null,
        url: null,
      },
      personModelInfo: {
        eid: "-9150751364756233164",
        path: [],
        speed: 3,
        times: 1,
        cameraTrace: false,
        state: 0,
        distance: 10,
      },
      infoLog: {
        diaLogName: "infoLog",
        resultshow: false,
        dialogFull: false,
        title: "",
        width: "50%",
        data: {},
        type: "1",
        id: "vrvideo",
      },
      // 演进模型参数---已完成后端配置化
      floodParams: [
        {
          //有可能出现一个场景中加载两个水库洪水演进数据
          isPreLoad: true, //是否预加载
          preLoadItems: [
            {
              floodId: "flood",
              loaded: false,
              dataUrl: "",
              isLocalfloodBasicData: true, //是否是使用本地化还是网络化的shp和dem，默认是本地
              floodBasicData: {
                tifURL: "D:\\51TIFData\\floodData-smtsk\\smtdem84",
                shpURL: "D:\\51TIFData\\floodData-smtsk\\smtshp84", //本地地址
                shxURL: "D:\\51TIFData\\floodData-smtsk\\smtshp84", //本地地址
                prjURL: "D:\\51TIFData\\floodData-smtsk\\smtshp84", //本地地址
                dbfURL: "D:\\51TIFData\\floodData-smtsk\\smtshp84", //本地地址
              }
            }
          ],
        },
      ],
    };
  },
  props: {
    digitalProps: {
      type: Object,
      default: function () {
        return {};
      },
    },
    mapCon: {
      type: Object,
      default: function () {
        return {};
      },
    },
    showTree: {
      type: Boolean,
      default: true,
    },
    showDtTitle: {
      type: Boolean,
      default: false,
    },
    smtPid: {
      type: String,
      default: '12345678',
    }
  },
  watch: {
    mapCon(value) {
      if (value && value.treeCon && value.treeCon.treeData) {
        let list = [];
        value.treeCon.treeData.forEach((item) => {
          if (item.id == "ssjk") {
            // let children = item.children;
            // if (children) {
            //   // 去掉 实时监控中的 水库水文站、水质站、流量站 选项
            //   // 添加 水位站
            //   children = children.filter(
            //     (ci) => ci.id != "ssjk-skz" && ci.id != "ssjk-szz"
            //   );
            //   children.push({
            //     tParentId: 36,
            //     showIndex: false,
            //     showPicture: false,
            //     id: "ssjk-swz",
            //     label: "水位站",
            //     type: "2",
            //     tId: 999,
            //   });
            //   item.children = children;
            // }
            list.push(item);
          }
          if (item.id == "fybz") {
            list.push(item);
          }
        });
        this.treeData = list;
      }
    },
    "$store.getters.select51": {
      handler(newVal) {
        let activeSelect = JSON.parse(newVal);
        if (activeSelect.obj.id == "ssjk") {
          if (activeSelect.obj.children.length != 0) {
            for (let x = 0; x < activeSelect.obj.children.length; x++) {
              if (activeSelect.obj.children[x].id == "ssjk-spd") {
                for (let i = 0; i < this.spdList.length; i++) {
                  let jsondata = {
                    id: this.spdList[i], //覆盖物id
                    covering_type: "poi", //覆盖物类型, 详见下表
                    bshow: activeSelect.flag, //true:显示; false:隐藏
                  };
                  digitalTwinApi.ShowHideCovering(jsondata);
                }
              } else if (activeSelect.obj.children[x].id == "ssjk-dmd") {
                for (let i = 0; i < this.dmList.length; i++) {
                  let jsondata = {
                    id: this.dmList[i], //覆盖物id
                    covering_type: "poi", //覆盖物类型, 详见下表
                    bshow: activeSelect.flag, //true:显示; false:隐藏
                  };
                  digitalTwinApi.ShowHideCovering(jsondata);
                }
              }
            }
          }
        }
        if (activeSelect.obj.id == "ssjk-spd") {
          for (let i = 0; i < this.spdList.length; i++) {
            let jsondata = {
              id: this.spdList[i], //覆盖物id
              covering_type: "poi", //覆盖物类型, 详见下表
              bshow: activeSelect.flag, //true:显示; false:隐藏
            };
            digitalTwinApi.ShowHideCovering(jsondata);
          }
        } else if (activeSelect.obj.id == "ssjk-dmd") {
          for (let i = 0; i < this.dmList.length; i++) {
            let jsondata = {
              id: this.dmList[i], //覆盖物id
              covering_type: "poi", //覆盖物类型, 详见下表
              bshow: activeSelect.flag, //true:显示; false:隐藏
            };
            digitalTwinApi.ShowHideCovering(jsondata);
          }
        }
      },
      deep: true,
      immediate: false,
    },
  },
  created() {
    this.digitalCon = this.digitalProps;
    let waterObj = localStorage.waterObj;
    if (waterObj != "" && waterObj != null && waterObj != undefined) {
      this.waterObject = JSON.parse(waterObj);
      this.sliderVal = this.waterObject.height;
      this.oldSliderVal = this.waterObject.height;
    }
    //更新渲染地址列表
    getRenderList().then((res) => {
      if(res.code==200){
      this.cloudUrls=res.rows;
      }
    });
  },
  methods: {
    // 改变地图类型
    changeMapType() {
      this.$parent.changeMap2D3D('2D')
    },
    //未知--------貌似油车
    escFullScreen(data) {
      this.infoLog.resultshow = data;
    },
    //未知--------貌似油车
    initDiaLog(val) {
      this.infoLog.type = val?.type;
      this.infoLog.data = val.data;
      this.infoLog.title = val.title;
      this.infoLog.diaLogName = val.name;
      this.infoLog.resultshow = true;
      this.infoLog.dialogFull = false;
      this.infoLog.width = "60%";
    },
    //未知--------貌似油车
    pinClick(val) {},
    //结束弹窗
    dialogClose(data) {
      this.infoLog.resultshow = false;
    },
    //工具集--重新设置渲染窗口大小
    resetVideoSize() {
      if (this.cloudRender) {
        this.cloudRender.SuperAPI(
          "SetStylePlayer",
          "position:absolute;width:100%;height:100%;top:50%;left:0%;transform:translate(0,-50%);"
        );
        this.cloudRender.SuperAPI("ResetVideo");
      }
    },
    //未知--------貌似油车
    handleValue(v, time) {
      this.switchWeather(v);
      // this.switchDate(time);
    },
    //未知--------貌似油车
    dtDbClick() {
      if (personModelInfo.state != 0) {
        console.log(personModelInfo.state, "personModelInfo.state");
        if (personModelInfo.state == 1) {
          this.$store.commit("map/SET_PERSON_PATROL_STATE", 1);
          digitalTwinApi.personPatrolState(false);
        } else if (personModelInfo.state == 2) {
          this.$store.commit("map/SET_PERSON_PATROL_STATE", 2);
          digitalTwinApi.personPatrolState(true);
        }
      }
    },
    //未知--------貌似油车
    initPersonPatrolTempPoi(show, list) {
      if (show) {
        if (list && list.length > 0) {
          list.forEach((item) => {
            let obj = this.poiList.find((ite) => {
              return (
                ite.point_type == "alarm_temp_point" && ite.id == "pp_temp_" + item.id
              );
            });
            if (obj) {
            } else {
              let point = {
                id: "pp_temp_" + item.id,
                coord: "119.759697,31.217882",
                coord_z_type: 2,
                coord_z: "39.114384",
                state: "pp_temp_state",
                point_type: "alarm_temp_point",
                always_show_label: false,
                show_label_range: "0,60",
                marker: {
                  size: "50,50",
                  images: [
                    {
                      define_state: "pp_temp_state",
                      normal_url: "", //'http://www.zhywater.com:18190/static/sylogo/a1.png',
                      activate_url: "", // 'http://www.zhywater.com:18190/static/sylogo/a1.png'
                    },
                  ],
                },
                label: {
                  bg_size: "280,28",
                  bg_offset: "-60,15",
                  content: [
                    {
                      text: [item.label, "#ff0000", "18"],
                      text_offset: "5,5",
                      text_centered: false,
                      text_boxwidth: 280,
                      scroll_speed: 1,
                    },
                  ],
                },
              };
              this.poiList.push(point);
              digitalTwinApi.AddCustomPOI(point);
            }
          });
        }
      } else {
        let items = this.poiList.find((item) => {
          return item.point_type == "alarm_temp_point";
        });
        if (items && items.length > 0) {
          let ids = [];
          items.forEach((item) => {
            ids.push(item.id);
          });
          let jsondata = {
            id: ids, //覆盖物id
            covering_type: "poi", //覆盖物类型, 详见下表
          };
          this.cloudRender.SuperAPI("RemoveCovering", jsondata, (status) => {
            console.log(status); //成功、失败回调
            this.poiList = this.poiList.filter(
              (item) => item.point_type != "alarm_temp_point"
            );
          });
        }
      }
    },
    //未知--------貌似油车
    personPatrol() {
      let lnglat1 = roamingCoords[0].coord.split(",");
      let lnglat2 = roamingCoords[1].coord.split(",");
      let yaw = getAngle(
        Number(lnglat1[0]),
        Number(lnglat1[1]),
        Number(lnglat2[0]),
        Number(lnglat2[1])
      );
      console.log("getAngle", yaw);
      // 调整镜头朝向
      let cameraInfoJo = {
        coord_type: 0,
        cad_mapkey: "",
        coord_z: roamingCoords[0].coord_z,
        center_coord: roamingCoords[0].coord,
        arm_distance: this.personModelInfo.distance,
        pitch: 26,
        yaw: yaw,
        fly: false,
      };
      this.cloudRender.SuperAPI("SetCameraInfo", cameraInfoJo, (status) => {
        console.log(status); //成功、失败回调
      });
      // 调整人物模型朝向
      let jo = {
        eid: "-9150751364756233164",
        coord_type: 0,
        cad_mapkey: "",
        scale: 1,
        coord: roamingCoords[0]["coord"],
        coord_z_type: 2,
        coord_z: roamingCoords[0]["coord_z"],
        pitch: 0,
        roll: 0,
        yaw: yaw - 180,
      };
      this.cloudRender.SuperAPI("updateAESObjectTransform", jo, (status) => {
        console.log("updateAESObjectTransform", status); //成功、失败回调
        // 显示人物模型
        let showHideAESObjectJo = {
          eid: [this.personModelInfo.eid],
          bshow: true, //true:显示; false:隐藏
        };
        this.cloudRender.SuperAPI(
          "ShowHideAESObject",
          showHideAESObjectJo,
          (ShowHideAESObjectBack) => {
            console.log(ShowHideAESObjectBack); //成功、失败回调
            if (this.personModelInfo.path.length > 0) {
              // 删除之前路径
              let ids = [];
              this.personModelInfo.path.forEach((item) => {
                ids.push(item.id);
              });
              let removeCoveringJo = {
                id: ids, //覆盖物id
                covering_type: "path", //覆盖物类型, 详见下表
              };
              this.cloudRender.SuperAPI(
                "RemoveCovering",
                removeCoveringJo,
                (removeCoveringBack) => {
                  console.log(removeCoveringBack); //成功、失败回调
                  // 创建路径
                  let path = {
                    id: "person_patrol_path", //路径id
                    coord_type: 0, //路径中坐标类型(0:经纬度坐标, 1:cad坐标)
                    cad_mapkey: "", //CAD基准点Key值, 项目中约定
                    coord_z_type: 2, //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
                    type: "none", //路径样式类型(none, solid, arrow, arrow_dot, dashed_dot, arrow_dashed, flash, scan_line, brimless_arrow, railway, railway, square_pipe, dashed_line)
                    color: "", //路径颜色(HEX颜色值, 空值即透明; railway类型无效, 默认黑白色)
                    pass_color: "", //覆盖物移动经过路径颜色(HEX颜色值)
                    width: 1, //宽度(单位:米, 圆柱直径或方柱边长)
                    speedRate: 1,
                    points: roamingCoords,
                  };
                  this.personModelInfo.path.push(path);
                  this.cloudRender.SuperAPI("AddPath", path, (pathback) => {
                    console.log(pathback);
                    //模型动画
                    let animationPlayJo = {
                      eid: this.personModelInfo.eid,
                      clip_name: "AESFBX_Anim", //片段名称
                      start_and_end: "1,32", //动画起止帧（按动画默认总帧数计算）
                      //"12,"从第12帧播放到最后一帧；"12,12"停止到第12帧；","停止到最后一帧；"1,"从第一帧播放到最后一帧
                      play_rate: 1, //播放倍速
                      loop: true, //是否循环
                      reverse: false, //是否倒放
                    };
                    this.cloudRender.SuperAPI(
                      "SetAESObjectAnimationPlay",
                      animationPlayJo,
                      (animationPlayback) => {
                        console.log("动画开始: ", animationPlayback);
                      }
                    );
                    // 覆盖物沿路径移动
                    let coverToMoveDataJo = {
                      attach_id: this.personModelInfo.eid, //要移动的覆盖物id (POI, 场景特效; 需提前创建完毕)
                      attach_type: "aes_object", //要移动的覆盖物类型 (poi, scene_effect, viewshed)
                      be_attach_id: "person_patrol_path", //依附的覆盖物id (路径, 区域, 圆形区域; 需提前创建完毕)
                      be_attach_type: "path", //依附的覆盖物类型 (path, range, circular_range)
                      speed: this.personModelInfo.speed * this.personModelInfo.times, //移动速度 (单位:米/秒)
                      loop: false, //是否循环
                      reverse: false, //是否反向移动,
                      current_attitude: true, //元素姿态(true: 前一帧的姿态; 注: true优先, "pitch", "yaw", "roll" 无效)
                    };
                    this.cloudRender.SuperAPI(
                      "CoverToMove",
                      coverToMoveDataJo,
                      (mycallback) => {
                        console.log("覆盖物沿路径移动: ", mycallback);
                        this.personModelInfo.state = 1;
                        this.initPersonPatrolTempPoi(true, [
                          {
                            id: "1",
                            label: "告警：人员非法闯入！",
                          },
                        ]);
                        // 镜头跟随
                        let cameraTraceJo = {
                          trace_object_type: "aes_object", //支持类型：aes_object  ,poi，场景特效，可视域，3D文字，灯光
                          trace_object_id: this.personModelInfo.eid, //对象ID
                          arm_distance: this.personModelInfo.distance,
                          fly: true,
                        };
                        this.cloudRender.SuperAPI(
                          "CameraTrace",
                          cameraTraceJo,
                          (CameraTraceBack) => {
                            console.log(CameraTraceBack); //成功、失败回调
                          }
                        );
                      }
                    );
                  });
                }
              );
            } else {
              // 创建路径
              let path = {
                id: "person_patrol_path", //路径id
                coord_type: 0, //路径中坐标类型(0:经纬度坐标, 1:cad坐标)
                cad_mapkey: "", //CAD基准点Key值, 项目中约定
                coord_z_type: 2, //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
                type: "none", //路径样式类型(none, solid, arrow, arrow_dot, dashed_dot, arrow_dashed, flash, scan_line, brimless_arrow, railway, railway, square_pipe, dashed_line)
                color: "", //路径颜色(HEX颜色值, 空值即透明; railway类型无效, 默认黑白色)
                pass_color: "", //覆盖物移动经过路径颜色(HEX颜色值)
                width: 1, //宽度(单位:米, 圆柱直径或方柱边长)
                speedRate: 1,
                points: roamingCoords,
              };
              this.personModelInfo.path.push(path);
              this.cloudRender.SuperAPI("AddPath", path, (pathback) => {
                console.log(pathback);
                //模型动画
                let animationPlayJo = {
                  eid: this.personModelInfo.eid,
                  clip_name: "AESFBX_Anim", //片段名称
                  start_and_end: "1,32", //动画起止帧（按动画默认总帧数计算）
                  //"12,"从第12帧播放到最后一帧；"12,12"停止到第12帧；","停止到最后一帧；"1,"从第一帧播放到最后一帧
                  play_rate: 1, //播放倍速
                  loop: true, //是否循环
                  reverse: false, //是否倒放
                };
                this.cloudRender.SuperAPI(
                  "SetAESObjectAnimationPlay",
                  animationPlayJo,
                  (animationPlayback) => {
                    console.log("动画开始: ", animationPlayback);
                  }
                );
                // 覆盖物沿路径移动
                let coverToMoveDataJo = {
                  attach_id: this.personModelInfo.eid, //要移动的覆盖物id (POI, 场景特效; 需提前创建完毕)
                  attach_type: "aes_object", //要移动的覆盖物类型 (poi, scene_effect, viewshed)
                  be_attach_id: "person_patrol_path", //依附的覆盖物id (路径, 区域, 圆形区域; 需提前创建完毕)
                  be_attach_type: "path", //依附的覆盖物类型 (path, range, circular_range)
                  speed: this.personModelInfo.speed * this.personModelInfo.times, //移动速度 (单位:米/秒)
                  loop: false, //是否循环
                  reverse: false, //是否反向移动,
                  current_attitude: true, //元素姿态(true: 前一帧的姿态; 注: true优先, "pitch", "yaw", "roll" 无效)
                };
                this.cloudRender.SuperAPI(
                  "CoverToMove",
                  coverToMoveDataJo,
                  (mycallback) => {
                    console.log("覆盖物沿路径移动: ", mycallback);
                    this.personModelInfo.state = 1;
                    this.initPersonPatrolTempPoi(true, [
                      {
                        id: "1",
                        label: "告警：人员非法闯入！",
                      },
                    ]);
                    // 镜头跟随
                    let cameraTraceJo = {
                      trace_object_type: "aes_object", //支持类型：aes_object  ,poi，场景特效，可视域，3D文字，灯光
                      trace_object_id: this.personModelInfo.eid, //对象ID
                      arm_distance: this.personModelInfo.distance,
                      fly: true,
                    };
                    this.cloudRender.SuperAPI(
                      "CameraTrace",
                      cameraTraceJo,
                      (CameraTraceBack) => {
                        console.log(CameraTraceBack); //成功、失败回调
                      }
                    );
                  }
                );
              });
            }
          }
        );
      });
    },
    //未知--------貌似油车
    personPatrolState(state) {
      let jsondata = {
        id: this.personModelInfo.eid, //移动的覆盖物id
        type: "aes_object", //移动的覆盖物类型 见下表
        play: state, //true:继续移动; false:暂停移动;
      };
      this.cloudRender.SuperAPI("PlayCoverMoveState", jsondata, (status) => {
        this.personModelInfo.state = state ? 1 : 2;
        console.log(status); //成功、失败回调
      });
    },
    //未知--------渗压管异常
    syPipeException(exit) {
      let item = {
        itemId: "warningException",
        label: "渗压管异常",
      };
      if (exit) {
        let jsondata = {
          id: "pp_temp_1_" + item.itemId,
          always_show_label: 0, //是否永远显示label, true:显示label(默认), false:不显示label
          show_label_range: "0,6000", //此POI点显示label的范围(单位:米, 范围最小、最大距离; 在此范围内显示, 超出范围隐藏; 注意: always_show_label 属性优先于此属性)
          sort_order: false, //是否开启遮挡排序(根据POI点真实3D位置开启遮挡排序,
          //注1: 只与其他开启排序的POI之间进行排序; 注2: 开启此排序会消耗性能(最多60个POI点同时开启排序))
          state: "pp_temp_state1", //与marker之中images中的define_state对应
          animation_type: "bounce", //动画类型(bounce:弹出式; stretch:伸缩式; wipe:展开式)
          duration_time: 0.7, //规定完成动画所花费的时间(单位:秒)
        };
        window.cloudRender.SuperAPI("UpdateCustomPOIStyle", jsondata).then((_back) => {
          console.log(_back);
        });
      } else {
        let point = {
          id: "pp_temp_1_" + item.itemId,
          coord: "119.757874,31.220053", // '119.758171,31.220245', // '119.759697,31.217882',
          coord_z_type: 2,
          coord_z: "44.785", // '36.209991',
          state: "pp_temp_state1",
          point_type: "alarm_temp_point",
          always_show_label: false,
          show_label_range: "0,500",
          params: {
            ...item,
          },
          marker: {
            size: "50,50",
            images: [
              {
                define_state: "pp_temp_state1",
                normal_url:
                  "http://www.zhywater.com:18190/static/sylogo/a1.png",
                activate_url:
                  "http://www.zhywater.com:18190/static/sylogo/a1.png",
              },
            ],
          },
          label: {
            bg_size: item.label.length * 20 + 30 + ",32",
            bg_offset: "10,0",
            content: [
              {
                text: [item.label, "#ff0000", "18"],
                text_offset: "5,5",
                text_centered: false,
                text_boxwidth: 280,
                scroll_speed: 1,
              },
            ],
          },
        };
        digitalTwinApi.AddCustomPOI(point);
      }
    },
    //未知--------
    personPatrolCameraTraceUpdate(cameraTrace) {
      if (cameraTrace) {
        let jsondata = {
          trace_object_type: "aes_object", //支持类型：aes_object  ,poi，场景特效，可视域，3D文字，灯光
          trace_object_id: this.personModelInfo.eid, //对象ID
          arm_distance: 30,
          fly: true,
        };
        this.cloudRender.SuperAPI("CameraTrace", jsondata, (status) => {
          this.personModelInfo.cameraTrace = cameraTrace;
          console.log(status); //成功、失败回调
        });
      } else {
        this.cloudRender.SuperAPI("StopCameraTrace", null, (status) => {
          this.personModelInfo.cameraTrace = cameraTrace;
          console.log(status); //成功、失败回调
        });
      }
    },
    //配置化--图层树点击开关显隐poi
    treeChange(data, flag) {
      console.log("treeChange", data, flag);
      let types = [];
      if (data.children != undefined) {
        data.children.forEach((item) => {
          types.push(item.id);
        });
      } else {
        types = [data.id];
      }
      if (types.length > 0) {
        let ids = [];
        this.poiList.forEach((item) => {
          if (types.indexOf(item.point_type) >= 0) {
            ids.push(item.id);
          }
        });
        if (ids.length > 0) {
          let jsondata = {
            id: ids, //覆盖物id
            covering_type: "poi", //覆盖物类型, 详见下表
            bshow: flag, //true:显示; false:隐藏
          };
          this.cloudRender.SuperAPI("ShowHideCovering", jsondata, (status) => {
            console.log(status); //成功、失败回调
          });
        }
        if (flag && this.unInitPoiList.length > 0) {
          let itemIds = [];
          let items = [];
          this.unInitPoiList.forEach((item) => {
            if (types.indexOf(item.point_type) >= 0) {
              itemIds.push(item.id);
              items.push(item);
              this.poiList.push(item);
            }
          });
          if (itemIds.length > 0) {
            this.unInitPoiList = this.unInitPoiList.filter(
              (item) => itemIds.indexOf(item.id) < 0
            );
            digitalTwinApi.AddCustomPOI(items);
          }
        }
      }
    },
    //配置化--图层树点击节点
    treeNodeClick(data, node) {
      console.log("treeNodeClick", data, node);
    },
    //配置化--弹窗重连渲染
    reconnectRender() {
      // this.cloudRender.SuperAPI("StopRenderCloud")
      // 启动云渲染
      this.cloudRender = new cloudRenderer("player", 0);
      window.cloudRender = this.cloudRender;
      if (this.cloudRenderTimer != null) {
        this.cloudRerenderCount = this.cloudRerenderCount + 1;
      }
      this.GetUrlStartRenderCloud(this.reconnectInfo.url, this.digitalCon.orderID);
      this.reconnectInfo.open = false;
    },
    //工具集--设置水面对象缓存
    setLocalStorage() {
      localStorage.waterObj = JSON.stringify(this.waterObject);
    },
    //工具集--开启获取aes实体对象
    getAESWaterObjects() {
      //获取水面的Eid
      let that = this;
      let jsondata = {
        state: true, //true:开启获取EID; false:关闭获取EID
        highlight: true, //true:点击实体高亮; false:点击实体不高亮
        //"act_layers":[] //可被选中元素所在图层，删除该字段，全部元素可被选中
      };
      that.cloudRender.SuperAPI("StartGetEID", jsondata).then((_back) => {
        let data = JSON.parse(_back);
        that.waterObject.eid = data.args.eid;
      });
    },
    //工具集--获取eid对应实体的基本信息
    getCoveringWaterInfo(arr) {
      //根据eid获取实体类的属性
      let that = this;
      if (that.waterObject.eid == "") {
        return this.$message.error("请先确定水面EID");
      }
      let jsondata = {
        eid: [that.waterObject.eid],
      };
      window.cloudRender.SuperAPI("GetAESObjectDataWithEids", jsondata, (_back) => {
        let data = JSON.parse(_back);
        this.waterObject.height = Number(Number(data.args.data[0].altitude).toFixed(0));
        this.waterObject.maxHeight = this.waterObject.height + 10;
        this.waterObject.minHeight = this.waterObject.height - 10;
        this.waterObject.lonlat =
          data.args.data[0].coord.longitude + "," + data.args.data[0].coord.latitude;
        console.log("this.waterObject",this.waterObject);

        this.sliderVal = this.waterObject.height;
        this.oldSliderVal = this.waterObject.height;
        return data;
      });
    },
    //工具集--根据上面获取的waterObject改变水面高度
    sliderValChange() {
      let that = this;
      let waterId = "waterId" + Math.floor(Math.random() * (1 - 1000) + 1000);

      digitalTwinApi.addWaterPathSet(
        waterId,
        this.waterObject.lonlat,
        this.oldSliderVal,
        this.sliderVal
      );
      setTimeout(() => {
        this.oldSliderVal = this.sliderVal;
        that.waterMoveTo(that.waterObject.eid, waterId);
      }, 100);
    },
    //工具集--水面上升下降
    waterMoveTo(eid, id) {
      let that = this;

      let jsondata = {
        attach_id: eid, //要移动的覆盖物id
        attach_type: "aes_object", //要移动的覆盖物类型 见下表
        be_attach_id: id, //依附的覆盖物id
        be_attach_type: "path", //依附的覆盖物类型 见下表
        speed: 0.1, //移动速度 (单位:米/秒)
        loop: false, //是否循环
        current_attitude: true,
        reverse: false, //是否反向移动
      };
      this.cloudRender.SuperAPI("CoverToMove", jsondata, (status) => {
        console.log(status); //成功、失败回调
      });
    },
    //配置化--进度条进行更新，用于假的进度标识
    changePercentage() {
      if (this.percentage == 50) {
        this.percentage;
        return;
      }
      this.percentage++;
    },
    // 配置化--初始化渲染51场景
    async initMap() {
      console.log("孪生配置：",this.digitalCon)
      //let cloudurl = '/51worldServer', // 'http://************:8890', //云渲染服务地址 (Cloud rendering service address) ①本地IP: http://***********:8889, ②域名: https://vizservice.51hitech.com
      // this.digitalCon.cloudUrl="http://*************:8889";
      // this.digitalCon.cloudUrl = "http://***********:30080/service";
      // this.digitalCon.orderID = "fc7af02b11118e4378750bbce3386772";
      console.log(this.digitalCon.cloudUrl, this.digitalCon.orderID)
      //  orderID = '36a47E82'; //渲染口令, 在云渲染客户端上获得 (Project ID, obtained on the cloud rendering client)
      if (this.digitalCon.cloudUrl && this.digitalCon.orderID) {
        window.cloudRender = new WdpApi({
          id: "player", // 渲染容器dom id
          url: this.digitalCon.cloudUrl, // [可选] 渲染服务地址
          order: this.digitalCon.orderID, // [可选] 渲染口令
          resolution: [window.innerWidth, window.innerHeight], // [可选] 场景输出分辨率;
          debugMode: "normal", // [可选] none:不打印日志, normal:普通日志
          keyboard: { // [可选]
            normal: false, // [可选]  键盘事件(wasd方向)开启关闭
            func: false // [可选]  浏览器F1~F12功能键开启关闭
          }
        });
        try {
          //启动云渲染
           window.cloudRender.Renderer.Start().then(async(res) => {
             if (res.success) {
               //register event
               this.registerEvent()
               // Register scene events
               await this.handlerSceneRegisterEvent()
             }
           })
        }catch(error){
          console.error('Error during initialization:', error);
        }

        // 启动云渲染
        // this.cloudRender = new cloudRenderer("player", 0);
        // window.cloudRender = this.cloudRender;
        // if (this.cloudRenderTimer != null) {
        //   this.cloudRerenderCount = this.cloudRerenderCount + 1;
        // }
        // this.reconnectInfo.url = this.digitalCon.cloudUrl;
        // this.GetUrlStartRenderCloud(this.digitalCon.cloudUrl, this.digitalCon.orderID);
      } else {
        this.$modal.msgWarning("渲染信息不能为空！");
      }
    },
    // 云渲染事件
    registerEvent() {
      //云渲染视频流监听
      window.cloudRender.Renderer.RegisterEvent([
        {
          name: 'onVideoReady', func: (res) => {
            // 视频流链接成功
            console.log('云服务连接成功, 视频流链接成功');
          }
        },
        {
          name: 'onStopedRenderCloud', func: (res) => {
            // 渲染服务中断
            console.log('云渲染关闭或通信息中断, 渲染服务中断');
          }
        }
      ]);
    },//场景事件注册
    async handlerSceneRegisterEvent() {
      window.cloudRender.Renderer.RegisterSceneEvent([
        {
          name: 'OnWdpSceneIsReady', func: async (res) => {
            // { "event_name": "OnWdpSceneIsReady", "result": { "progress": 100 } }
            if (res.result.progress === 100) {
              // 场景加载完成
              console.log('场景加载成功');
              this.setCameraPose("default")
              // 加载poi数据
              // this.initData();
              // this.initMapScatterInfo()
              // setTimeout(async () => {
                this.loginAnimate = false
              //   // this.panelShow = true
                this.$modal.msgSuccess('场景加载完成')
              //   //添加poi点
              //   // this.addCustomPoi()
              //   //添加管理范围线
              //   await this.addRangeByPath("/json/guanlirange.json")
              //   //添加3D文字
              //   this.AddText3D(this.poiArr)
              //   this.addEffectPoint("htq")
              //   this.addEffectPoint("gxq")
              //   //开启实时光照和实时天气
              //   // 获取时间
              //   const time = await window.cloudRender.Environment.GetSkylightTime();
              //   // 设置时间
              //   // await window.cloudRender.Environment.SetSkylightTime(time.result.skylightTime, 1.5, true); //参数1：切换到的时间点；参数2：切换效果的持续时间；参数3：应用实时时间
              //   // 获取天气
              //   // await window.cloudRender.Environment.GetSceneWeather();
              //   // // 设置天气
              //   // await window.cloudRender.Environment.SetSceneWeather('auto', 30, true);   //参数1：切换到的天气；参数2：切换效果的持续时间；参数3：应用实时天气
              //
              // }, 1000);
            }
          }
        },
        {
          name: 'OnWdpSceneChanged', func: async (res) => {
            // 实体对象操作后回调；
            // res.result --> {added[object]，updated[object]，removed[object]}
          }
        },
        {
          name: 'OnMouseEnterEntity', func: async (res) => {

          }
        },
        {
          name: 'OnMouseOutEntity', func: async (res) => {
            // 鼠标滑出实体事件回调; 包含数据信息与实体对象
          }
        },
        {
          name: 'OnEntityClicked', func: async (res) => {
            // 覆盖物被点击事件回调; 包含数据信息与实体对象
            console.log(res, 'res OnEntityClicked');
            if (res?.success) {
              this.$emit("digitalPoiClick", JSON.parse(res.result.object.customData))
            }
          }
        },
        {
          name: 'OnWebJSEvent', func: async (res) => {
            // 接收widnow内嵌页面发送的数据
            // { "event_name": "OnWebJSEvent", "result": { "name": "自定义name", "args": "自定义数据" }}
          }
        },
        {
          name: 'MeasureResult', func: async (res) => {
            // 测量工具数据回调
          }
        },
        {
          name: 'OnMoveAlongPathEndEvent', func: (res) => {
            // 覆盖物移动结束信息回调
          }
        },
        {
          name: 'OnCameraMotionStartEvent', func: async (res) => {
            // 相机运动开始信息回调
          }
        },
        {
          name: 'OnCameraMotionEndEvent', func: async (res) => {
            // 相机运动结束信息回调
            // this.GetCameraPose()
            if (this.roamingStatus === "play" && this.roamingMap[this.areaId][5] == this.roaming) {
              if (this.roamingMap[this.areaId][1].length > this.roamingIndex) {
                this.typeWriting(this.areaId, this.roamingIndex)

                setTimeout(async () => {
                  this.typeShow = false
                  const data = {
                    frames: this.roamingMap[this.areaId][1][++this.roamingIndex]
                  }
                  await this.entityObj.Update(data)
                  this.updateRatio(this.areaId)
                  await window.cloudRender.CameraControl.PlayCameraRoam(this.entityObj, this.roamingMap[this.areaId][4]);
                }, 10000);
              } else {
                await window.cloudRender.CameraControl.Stop()
                this.roamingIndex = 0
              }
            }

          }
        },
        {
          name: 'PickPointEvent', func: async function (res) {
            // 取点工具取点数据回调
            console.log('地图标点', res);
            // this.$emit()
          }
        },
        {
          name: 'OnEntitySelectionChanged', func: async function (res) {
            // 实体被选取[框选]、数据回调
          }
        },
        {
          name: 'OnEntityNodeSelectionChanged', func: async function (res) {
            // 模型node选择状态变化数据回调
          }
        },
        {
          name: 'OnEntityReady', func: async function (res) {
            // 3DTilesEntity，WMSEntity，WMTSEntity 加载完成;
            // {success: true, message: '', result: { object: 对象, progress: 100 }}
          }
        },
        {
          name: 'OnCreateGeoLayerEvent', func: async function (res) {
            // 用于GisApi； WMS,WMTS 添加 报错回调
          }
        },
        {
          name: 'OnGeoLayerFeatureClicked', func: async function (res) {
            // 用于GisApi；点击实体回调
          }
        }
      ]);
      // 场景事件注册/获取/删除
      await window.cloudRender.Renderer.GetRegisterSceneEvents()
    },
    // 获取当前视角
    getCameraInfo(){
     let cameraInfo= window.cloudRender.CameraControl.GetCameraInfo()
      console.log("获取当前视角:",cameraInfo)
    },
    //镜头跳转
    async setCameraPose(type) {
      const cameraMap = {
        default: {
          "location": [
            119.08071061772375, 36.62166779548944, 112.9788443713652
          ],
          "rotation": {
            "pitch":  -19.050466537475586,
            "yaw": 74.99987030029297
          },
          // "flyTime": 1 //过渡时长(单位:秒)
        },
        jzz: {
          "location": [119.22850689536251, 36.90869422529727, 18.5036634348362],
          "rotation": {
            pitch: -29.65965461730957, yaw: -145.043212890625
          },
          // "flyTime": 1 //过渡时长(单位:秒)
        },
        xjb: {
          "location": [119.22032530512827, 36.778193129593191, 39.854391639103632],
          "rotation": {
            pitch: -29.928838729858398, yaw: -85.660957336425781
          }
        }
      }
      const jsonData = cameraMap[type]
      const res = await window.cloudRender.CameraControl.SetCameraPose(jsonData);
    },
    //配置化--链接渲染
    GetUrlStartRenderCloud(cloudurl, orderID) {
      console.log("cloudurl",cloudurl);
      let that = this;
      fetch(`${cloudurl}/Renderers/Any/order`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          order: orderID,
          width: window.innerWidth,
          height: window.innerHeight,
        }),
        //① order 渲染口令必填; ② width, height: 设置云渲染输出分辨率(此设置为固定分辨率,可选; 默认以云渲染客户端设置的分辨率输出)
      })
        .then((res) => {
          if (!res.ok) {
            throw Error(res.statusText);
            // that.refreshCloudRender(true)
          }
          return res.json();
        })
        .then((json) => {
          if (json.errCode != null && json.errCode !== undefined) {
            // if (json.message != null && json.message !== undefined) {
            //   this.$modal.msgWarning(json.message)
            //   return
            // } else {
            //   let text = constants[json.errCode]
            //   if (text) {
            //     this.$modal.msgWarning(text)
            //     return
            //   }
            // }
            that.reconnectInfo.title = "重新连接";
            that.reconnectInfo.label = json.message
              ? json.message
              : constants[json.errCode];
            that.reconnectInfo.open = true;
            return;
          }
          if (json.url) {
            that.refreshCloudRender(false);
            //启动云渲染
            //that.cloudRender.SuperAPI("StartRenderCloud", json.url);
            // 开启WASD方向键事件
            that.cloudRender.SuperAPI("StartRenderCloud", json.url, "keyboard");
            //事件注册
            that.cloudRender.SuperAPI(
              "RegisterCloudResponse",
              that.myHandleResponseFunction
            );

            that.cloudRender.SuperAPI_onRenderCloudError = function () {
              console.log("云渲染异常");
            };

            that.cloudRender.SuperAPI_onUnavailableRender = function () {
              console.log("未获取到渲染资源.");
            };

            that.cloudRender.SuperAPI_onStopedRenderCloud = function () {
              console.log("云渲染关闭或通信息中断");
              that.loginAnimate = true;
              that.loginAnimate = true;
              that.reconnectInfo.title = "重新连接";
              that.reconnectInfo.title = "云渲染关闭或通信息中断";
              that.reconnectInfo.open = true;
              // that.refreshCloudRender(true)
            };

            that.cloudRender.SuperAPI_onRenderCloudConnected = function () {
              console.log("云服务连接成功");
            };

            // setTimeout(() => {
            //   // 设置视角
            //   that.setDefaultView();
            // }, 10000);
          }
        })
        .catch((error) => {
          console.error("51 Error: ", error);
          // that.refreshCloudRender(true)
        });
    },
    //配置化--刷新渲染
    refreshCloudRender(state) {
      let that = this;
      if (state) {
        console.log("重新渲染总次数：" + this.cloudRerenderSum);
        console.log("重新渲染次数：" + this.cloudRerenderCount);
        if (this.cloudRerenderSum > 3) {
          clearTimeout(this.cloudRenderTimer);
          this.cloudRenderTimer = null;
          console.log(
            "重新渲染总次数超过3次后将不再重新渲染，如需渲染页面请重新刷新页面"
          );
        } else if (this.cloudRerenderCount > 3) {
          console.log("51场景重新渲染失败");
          this.$modal.msgWarning("51场景渲染失败! 请检查网络或刷新页面。");
          clearTimeout(this.cloudRenderTimer);
          this.cloudRenderTimer = null;
          this.cloudRerenderCount = 0;
        } else {
          this.cloudRerenderSum = this.cloudRerenderSum + 1;
          console.log("51场景重新渲染：" + this.cloudRerenderCount);
          clearTimeout(this.cloudRenderTimer);
          this.cloudRenderTimer = null;
          this.cloudRenderTimer = setTimeout(() => {
            that.initMap();
          }, 5000);
        }
      } else {
        console.log("清除重新渲染场景定时任务");
        clearTimeout(this.cloudRenderTimer);
        this.cloudRenderTimer = null;
        this.cloudRerenderCount = 0;
      }
    },
    //配置化--设置场景视角
    setDefaultView(jsondata) {
      if (jsondata != null) {
        this.cloudRender.SuperAPI("SetCameraInfo", jsondata).then((_back) => {
        });
      }
    },
    //配置化--根据配置的各个视角的code值进行场景视角设置
    resetDtViewer(code) {
      let viewer = this.digitalCon.mapViewerOptions.find((item) => {
        return code == item.code;
      });
      if (viewer) {
        this.setDefaultView(viewer);
      }
    },
    // 油车特有--设置镜头视角并旋转 待优化
    setCameraViewerAndRotate(code, time, direction) {
      let viewer = this.digitalCon.mapViewerOptions.find((item) => {
        return code == item.code;
      });
      if (viewer) {
        console.log("viewer", viewer);
        this.cloudRender.SuperAPI("SetCameraInfo", viewer).then((_back) => {
          //开始旋转
          let jo = {
            time: time || 50,
            direction:
              direction == 1 ? "clockwise" : direction == 1 ? "anticlockwise" : "stop",
          };
          this.cloudRender.SuperAPI("SetCameraRotate", jo, (e) => {});
        });
      }
    },
    //工具集--51场景的各种回调事件
    myHandleResponseFunction(data) {
      let that = this;
      let jsonObject = JSON.parse(data);
      switch (jsonObject.func_name) {
        case "APIAlready":
          // 3D世界加载完成 do Something

          //setTimeout(()=>{that.smtGetDownStation();},50000);
          setTimeout(()=>{that.switchDate("07:25");},10000);
          that.resetCameraSpace('free');
          let videoDom = document.getElementById("streamingVideo");
          videoDom.style.objectFit = "fill";
          videoDom.style.height = "100%";
          this.cloudRender.SuperAPI(
            "SetStylePlayer",
            "position:absolute;width:100%;height:100%;top:50%;left:0%;transform:translate(0,-50%);"
          );
          this.cloudRender.SuperAPI("ResetVideo");
          this.cloudRender.SuperAPI("SetResolution", {
            width: window.innerWidth,
            height: window.innerHeight,
          });
          window.addEventListener("resize", () => {
            this.cloudRender.SuperAPI(
              "SetStylePlayer",
              "position:absolute;width:100%;height:100%;top:50%;left:0%;transform:translate(0,-50%);"
            );
            this.cloudRender.SuperAPI("ResetVideo");
            this.cloudRender.SuperAPI("SetResolution", {
              width: window.innerWidth,
              height: window.innerHeight,
            });
          });
          this.cloudRender.SuperAPI("keyboard");
          let MapViewerOption = that.digitalCon.firstMapViewerOption;
          personModelInfo.mapViewerOptions = this.digitalCon.mapViewerOptions;
          let windowHref = window.location.href;
          if (windowHref.indexOf("youche") > -1) {
            // 油车加载时镜头特殊处理
            if (windowHref.indexOf("/screen/youche/indexs") > -1) {
              // 首页 远眺视角加旋转
              this.setCameraViewerAndRotate("ycCamareRotate", 50, "1");
            } else if (windowHref.indexOf("/screen/youche/Ysizhi") > -1) {
              // 四制 油车水库管理所视角
              this.resetDtViewer("ycgls");
            } else {
              that.setDefaultView(MapViewerOption); // 默认视角
            }
          } else {
            that.setDefaultView(MapViewerOption);
          }
          setTimeout(() => {
            that.loginAnimate = false;
          }, 1000);
          // that.removeKeyboard();
          //加载网格下载及转置
          setTimeout(() => {
            if (that.digitalCon.floodParams) {
              this.floodParams=that.digitalCon.floodParams;
              console.log('后端预加载参数',this.floodParams);
              for (let i = 0; i < this.floodParams.length; i++) {
                if (this.floodParams[i].isPreLoad) {
                  this.waterFloodFileLoadAndChangeGrid(
                    this.floodParams[i].preLoadItems[0].floodId,
                    this.floodParams[i].preLoadItems[0]
                  );
                  this.floodParams[i].preLoadItems[0].loaded = true;
                }
              }
            }
          }, 10000);
          //添加汛限水位线
          // this.addWaterPolyline(0)
          if(that.digitalCon.waterElevation){
            that.setWaterFaceElevation(that.digitalCon.waterElevation);
          }
          that.setCameraSpace();
          // that.initData();//需要区分油车和横山
          if (
            that.digitalCon.projectIndex == "JXCSK" ||
            that.digitalCon.projectIndex == "YCSK"
          ) {
            that.initData();
          } else {
            that.initMapPointByUrl();
          }
          // 添加横山漫游特效点
          that.initHSSKCameraRoamingPoi();
          // 根据当前开闭闸情况添加水花效果
          // that.initGateStatus("/product/screen/gateData",{projectId:this.$store.state.map.projectId});
          break;
        case "beginPlay":
          setTimeout(function () {
            that.switchWeather("LightRain");
            that.switchDate("07:25");
            let labelTimer = null;
            //初始化完成后回调
            that.$emit("afterInitDigitalTwin");
            Bus.$on("boardCoord", (e) => {
              if (e) {
                try {
                  const { latitudeWgs84, longitudeWgs84 } = e;
                  e.coord = Number(longitudeWgs84) + "," + Number(latitudeWgs84);
                  e.coord_z = 33.720001220703125;
                  e.altitude = 33.720001220703125;
                  e.time = new Date().getTime();
                  //作位置偏移调整
                  e.coord =
                    Number(e.longitudeWgs84 - 0.000019) +
                    "," +
                    Number(e.latitudeWgs84 - 0.000051);
                  //为了避免重新更新标签，加上时间限制
                  if ((e.time - labelTimer) / 1000 > 4) {
                    digitalTwinApi.addWaterCoord(e);
                    labelTimer = new Date().getTime();
                  }
                  digitalTwinApi.boardSocketCopy(e, e.deviceId);
                  // digitalTwinApi.boardSocketCopy2(e, e.deviceId)
                  // digitalTwinApi.boardSocketCopy3(e, e.deviceId)
                  // digitalTwinApi.boardSocketCopy4(e, e.deviceId)
                } catch (error) {
                  // throw new Error(error)
                  console.log(error, "error");
                }
              }
            });
            let jsondata = {
              state: true, //true:开启获取EID; false:关闭获取EID
              highlight: false, //true:点击实体高亮; false:点击实体不高亮
            };
            window.cloudRender.SuperAPI("StartGetEID", jsondata).then((_back) => {
              console.log(_back);
            });
          }, 10000);

          break;
        case "OnSuperAPIInputActionStart":
          break;
        case "OnAddCustomPOISuccess":
          //console.log('OnAddCustomPOISuccess', jsonObject)
          break;
        case "OnPathClick":
          //console.log('OnPathClick', '覆盖物点击事件', jsonObject)
          break;
        case "OnStartGetEIDResult":
          if (jsonObject.args.eid == "-9150751357353827118") {
            window.open(
              "http://192.168.1.30:7100/jumpPage?userName=yixing&password=VPscNcQcMdrr6ZQs+tgc7j4SEkerT2OymFkr5GWdU1msxUbQGEBv+hSCWLjmq2ZGvV1Tz1F2EUt5rjBqdP0BboezR6VpSsJ1EEmziHv1GV5F2ddSX6tQtttvRqkrAxAUuk2hRWV/Z6w9Gj8VCDvSjGu0oVlp47fhLB5PKC8Hvww=",
              "_blank"
            );
          } else if (jsonObject.args.eid == "-9150751360376084702") {
            digitalTwinApi.waterLabelShow = true;
            window.cloudRender
              .SuperAPI("ShowHideCovering", {
                id: "09db4280e29045b9a375b14a94cb2c04", //覆盖物id
                covering_type: "poi", //覆盖物类型, 详见下表
                bshow: digitalTwinApi.waterLabelShow, //true:显示; false:隐藏
              })
              .then((_back) => {
                console.log(_back);
              });
          }
          break;
        case "OnPOIClick":
          //点击事件；
          console.log("OnPOIClick", "POI点击事件", jsonObject);

          if (jsonObject.args && jsonObject.args.id) {
            // console.log(this.poiList);
            let point = this.poiList.find((item) => {
              return (
                item.id == jsonObject.args.id || item.deviceCode === jsonObject.args.id
              );
            });
            if (point) {
              console.log("点击点位信息", point, point.point_type);

              // 向上返回点击回调信息
              this.$emit("OnPOIClick", point);
              // 隐藏POI的label
              let jo = {
                id: point.id,
                always_show_label: false,
                show_label_range: point.show_label_range,
                sort_order: false,
                state: point.state,
                animation_type: point.animation_type,
                duration_time: 0,
              };
              digitalTwinApi.UpdateCustomPOIStyle(jo);
            }
          }
          break;
        //模拟指定类型的覆盖物点击
        case "OnSimClickCoveringSuccess":
          let jsondata = {
            id: "aes_object", //覆盖物id
            selected_state: true, //覆盖物是否被选中; true:选中; false:未选中
            covering_type: "path", //覆盖物类型, 详见下表
          };
          window.cloudRender.SuperAPI("SimClickCovering", jsondata).then((_back) => {
            console.log(_back);
          });
          break;
        case "OnPOIHover":
          //POI点击事件；
          break;
        case "OnPOIUnHover":
          //POI点击事件；
          break;
        case "OnCustomWebJsEvent": //window与3D世界通信
          //点击事件；
          //this.$emit('OnCustomWebJsEvent', jsonObject);
          break;
        case "OnOpenMapSuccess":
          //this.$emit('OnOpenMapSuccess');
          break;
        case "OnPOILabelClick": //鼠标点击自定义POI label 回调通知
          console.log("OnPOILabelClick", "鼠标点击自定义POI label 回调通知", jsonObject);

          if (jsonObject.args && jsonObject.args.id) {
            let point = this.poiList.find((item) => {
              return item.id == jsonObject.args.id;
            });
            if (point) {
              this.$emit("OnPOIClick", point);
            }
            // 告警点
            let alarmPoint = personModelInfo.poiList.find((item) => {
              return item.id == jsonObject.args.id;
            });
            if (alarmPoint) {
              this.$emit("OnPOIClick", alarmPoint);
            }
          }
          break;
        case "OnFocusEffectEnd": //Focus动作开始, 结束 回调通知
          //this.$emit('OnFocusEffectEnd');
          break;
        case "OnFocusAllEffectEnd": //Focus动作开始, 结束 回调通知
          //this.$emit('OnFocusAllEffectEnd');
          break;
        case "OnFocusPOIEnd": //聚焦完毕回调；
          //this.$emit('OnFocusPOIEnd');
          break;
        case "OnAnimationFinished": //动画放完；
          //this.$emit('OnAnimationFinished');
          break;
        case "OnCloseMapSuccess": //关闭地图回调
          //this.$emit('OnCloseMapSuccess');
          break;
        case "OnHomeButtonClicked": //返回按钮   点击池州九华
          //this.$emit('OnHomeButtonClicked');
          break;
        case "OnCameraRoamingProStart": //漫游开始
          console.log("1111111");
          //this.$emit('OnCameraRoamingProStart');
          break;
        case "OnCameraRoamingProEnd": //漫游结束
          // this.cameraMoveState();
          //this.$emit('OnCameraRoamingProEnd');
          break;
        case "OnCoverToMoveSuccess": // 覆盖物移动
          //this.$emit('OnCoverToMoveSuccess', jsonObject);
          // 判断是否是人工巡查 是的话 定时查询当前人物模型位置信息
          if (personModelInfo.state != 0) {
            /*let videoPointList = this.poiList.filter(
              (item) =>
                item.point_type == "ssjk-spd" &&
                // && item.params.label != '闸阀室内'
                item.params.label != "溢洪闸看2号门"
            );*/
            let sywyPointList = this.poiList.filter(
              (item) => item.point_type == "ssjk-wy" || item.point_type == "ssjk-sy"
            );
            if (personModelInfo.timer == null) {
              personModelInfo.timer = setInterval(() => {
                let jo = {
                  eid: [personModelInfo.eid],
                };
                this.cloudRender.SuperAPI("GetAESObjectDataWithEids", jo, (_back) => {
                  let data = JSON.parse(_back);
                  let coordInfo = data.args.data[0].bounding_center_coord;
                  /*if (videoPointList && videoPointList.length > 0 && coordInfo) {
                  let min = 0;
                  let vd = null;
                  videoPointList.forEach((item, index) => {
                    let lnglat = item.coord.split(",");
                    let distance = this.GetDistance(
                      Number(lnglat[1]),
                      Number(lnglat[0]),
                      Number(coordInfo.y),
                      Number(coordInfo.x)
                    );
                    //console.log('distance',item.label.content[0].text[0], distance)
                    if (index === 0) {
                      min = distance;
                      vd = item;
                    } else {
                      if (min > distance) {
                        min = distance;
                        vd = item;
                      }
                    }
                  });
                  this.$store.commit("map/SET_PERSON_PATROL_VIDEO_POINT_INFO", vd);
                }*/
                  if (sywyPointList && sywyPointList.length > 0 && coordInfo) {
                    let list = [];
                    sywyPointList.forEach((item, index) => {
                      let lnglat = item.coord.split(",");
                      let distance = this.GetDistance(
                        Number(lnglat[1]),
                        Number(lnglat[0]),
                        Number(coordInfo.y),
                        Number(coordInfo.x)
                      );
                      if (distance <= 50) {
                        list.push(item);
                      }
                    });
                    this.$store.commit("map/SET_PERSON_PATROL_WYSY_POINT_LIST", list);
                  }
                });
              }, 2000);
            }
          }
          break;
        case "OnUpdateEffectCoordSuccess": // 场景特效更新坐标
          //this.$emit('OnCoverToMoveSuccess', jsonObject);
          break;
        case "OnCameraToMoveEnd": //镜头移动动作结束；
          this.cloudRender
            .SuperAPI("RemoveCovering", {
              id: digitalTwinApi.poiId || this.poiId, //覆盖物id
              covering_type: "poi", //覆盖物类型, 详见下表
            })
            .then((_back) => {
              console.log(_back);
            });
          window.cloudRender
            .SuperAPI("RemoveCovering", {
              id: ["path_xhz", "patrol", "1747452955821404160", "100", "100"], //覆盖物id
              covering_type: "path", //覆盖物类型, 详见下表
            })
            .then((_back) => {
              console.log("删除路径", _back);
            });
          this.cameraMoveState();
          this.$emit("showOpen", false);
          //隐藏小人模型
          window.cloudRender
            .SuperAPI("ShowHideAESObject", {
              eid: ["-9111626350604376433"],
              bshow: false, //true:显示; false:隐藏
            })
            .then((_back) => {
              console.log(_back);
            });
          break;
        case "OnSwitchChinaMapSuccess": //开启中国地图成功
          //this.$emit('OnSwitchChinaMapSuccess');
          break;
        case "OnSceneEffectClick": //5G圆形特效点击事件
          //this.$emit('OnSceneEffectClick');
          if (jsonObject.args && jsonObject.args.id) {
            if (jsonObject.args.id == "youche_zm_effect_id") {
              // this.initDiaLog({
              //   data: "http://192.168.1.30:7100/minio/uavfile/2024/VR.mp4",
              //   title: "VR线上培训",
              //   name: "videolog",
              // });
              // this.dialogFull = true;
              this.infoLog.resultshow = true;
              this.infoLog.id = "vrvideo";
              this.infoLog.data = "http://192.168.1.30:7100/minio/uavfile/2024/VR.mp4";
              console.log(videolog, "videolog");
            }
            if (jsonObject.args.id == "hssk_cameraroaming_effect_id") {
              this.hsskCamreraRoamingPlay();
            }
          }
          break;
        //  场景响应鼠标、键盘操作 操作开始
        case "OnSuperAPIInputActionStart":
          if (jsonObject.args) {
            this.inputActionStartFunction(jsonObject.args);
          }
          break;
        //  场景响应鼠标、键盘操作 操作结束
        case "OnSuperAPIInputActionEnd":
          if (jsonObject.args) {
            this.inputActionEndFunction(jsonObject.args);
          }
          break;
        case "OnStartGetCoordSuccess":
          console.log("OnStartGetCoordSuccess", "开启地图点位信息成功", jsonObject);
          break;
        case "OnGetCoord":
          console.log("OnGetCoord", "获取地图点位信息成功", jsonObject);
          if (jsonObject.args) {
            let args = jsonObject.args;
            if (args.coord_result && args.coord_result.length > 0) {
              let coords = [];
              args.coord_result.forEach((item) => {
                coords.push({
                  coord: item.coord,
                });
              });
              console.log("coords", coords);
              this.$store.commit("common/SET_POINT", coords.pop());
            }
          }
          break;
        case "OnCoverToMoveStart":
          break;
        case "OnCoverToMoveEnd":
          break;
        case "OnWimWFSFileDownLoadSuccess":
          console.log(
            "OnWimWFSFileDownLoadSuccess, 加载演进初始数据",
            jsonObject,
            // jsonObject.args.GridID.replace(/[^0-9]/gi, "")
            jsonObject.args.GridID.substring(5)
          );
          this.waterFloodChangeGrid(
            // jsonObject.args.GridID.replace(/[^0-9]/gi, ""),
            jsonObject.args.GridID.substring(5),
            jsonObject.args.GridID
          );
          break;
      }
      return data;
    },
    //场景初始化-根据数据设置水花
    initGateStatus(gateStatusInterfaceUrl,query){
      getDataByUrlAndQuery(gateStatusInterfaceUrl,query).then((res) => {
        console.log('查询到的闸门开度信息',res);
        if(res.code==200) {
          let totalFlow=0.0
          for(let i=0;i<res.data.length;i++){
            if(res.data[i].flow>0.0){
              totalFlow=totalFlow+res.data[i].flow;
            }
          }
          if(totalFlow>0.0){
            let flowArray=[3, 2, 1];
            if(totalFlow<=100){
              flowArray=[3, 2, 1];
            }else if(100<totalFlow<200){
              flowArray=[9, 7, 8];
            }else if(totalFlow>=200){
              flowArray=[4, 5, 6];
            }
            //调用水花效果
              this.gateSwitchAndWaterFlowSwitch(true,flowArray);
          }
        }
      });
    },
    //配置化-设置水面高度
    setWaterFaceElevation(list){
      for(let i=0;i<list.length;i++){
        if(list[i].isEnable){
        //查找该对象并进行移动至某高度
        switch(list[i].heightDataWay){
          case '1':
            list[i].waterEleValue=Number(list[i].staticValue);
            digitalTwinApi.getWaterFaceInfoAndMove(list[i]);
            break;
          case '2'://查询接口形式
            // 查询展点信息
            let interfaceUrl = list[i].httpAddress;
            let interfaceParams = list[i].urlParam;
            if (interfaceParams && interfaceParams.length > 0) {
              let ja = JSON.parse(interfaceParams);
              if (ja && ja.length > 0) {
                let x = interfaceUrl.substr(interfaceUrl.length - 1);
                if (x != "?") {
                  interfaceUrl += "?";
                }
                ja.forEach((param, index) => {
                  if (index > 0) {
                    interfaceUrl += "&";
                  }
                  interfaceUrl += param["key"] + "=" + param["value"];
                });
              }
            }
            getDataByUrl(interfaceUrl).then((response) => {
              if(response.code==200&&response.data[list[i].returnName]){
              list[i].waterEleValue=Number(response.data[list[i].returnName]);
              console.log('接口水位值',list[i].waterEleValue)
              digitalTwinApi.getWaterFaceInfoAndMove(list[i]);
             }
            });
            break;
          case '3'://ws形式 未应用暂不开发建议使用以上两种先
            break;
        }
      }
      }
    },
    // 初始化数据(油车)
    initData() {
      let windowHref = window.location.href;
      let isYouCheIndex = false;
      if (windowHref.indexOf("/screen/youche/indexs") > -1) {
        // 油车加载时镜头特殊处理
        isYouCheIndex = true;
      }
      // 闸门开度
      let zmPoint = [
        {
          index: "1",
          coord: "119.757942,31.219967",
          coord_z: "44.561256",
          label: ["开度(m): 0", "流量(m³/s): 0"],
        },
        {
          index: "2",
          coord: "119.757919,31.220007",
          coord_z: "44.540531",
          label: ["开度(m): 0", "流量(m³/s): 0"],
        },
        {
          index: "3",
          coord: "119.757889,31.220043",
          coord_z: "44.543877",
          label: ["开度(m): 0", "流量(m³/s): 0"],
        },
      ];
      let cList = [];
      zmPoint.forEach((item) => {
        let point = {
          id: "zmkd_" + item.index,
          coord: item.coord,
          coord_z_type: 2,
          coord_z: item.coord_z,
          point_type: "ssjk-zmkd", // item.type,
          state: "zmkd_state",
          always_show_label: false,
          show_label_range: "0,30",
          marker: {
            size: "1,1",
            images: [
              {
                define_state: "zmkd_state",
                normal_url: "",
                activate_url: "",
              },
            ],
          },
          label: {
            bg_size: "120,50",
            bg_offset: "-60,15",
            content: [
              {
                text: [item.label[0], "#fff", "12"],
                text_offset: "10,5",
                text_centered: false,
                text_boxwidth: 120,
                scroll_speed: 1,
              },
              {
                text: [item.label[1], "#fff", "12"],
                text_offset: "10,25",
                text_centered: false,
                text_boxwidth: 120,
                scroll_speed: 1,
              },
            ],
          },
        };
        cList.push(point);
        this.poiList.push(point);
      });
      if (cList.length > 0) {
        //console.log(" 添加位移信息", customPoiList);
        digitalTwinApi.AddCustomPOI(cList);
      }
      // 位移点
      if (this.digitalCon.monitorsPoints && this.digitalCon.monitorsPoints.length > 0) {
        this.digitalCon.monitorsPoints.forEach((monitorsPoint) => {
          if (monitorsPoint.httpAddress != null) {
            getMapPointListByUrl(monitorsPoint.httpAddress).then((res) => {
              if (res.code == 200 && res.data && res.data.length > 0) {
                this.treeCheckedKeys.push("ssjk-wy");
                // this.treeExpandedKeys.push('ssjk-wy')
                let data = res.data;
                let customPoiList = [];
                data.forEach((item, index) => {
                  if (item.coord != null && item.coord != "") {
                    let content = [];
                    let label = item.label;
                    content.push({
                      text: ["测点：" + label, "#fff", "12"],
                      text_offset: "10,5",
                      text_centered: false,
                      text_boxwidth: 220,
                      scroll_speed: 1,
                    });
                    if (item.dvariationPlaneX != null) {
                      content.push({
                        text: [
                          "位移(水平)：" + item.dvariationPlaneX + "mm",
                          "#fff",
                          "12",
                        ],
                        text_offset: "10,25",
                        text_centered: false,
                        text_boxwidth: 220,
                        scroll_speed: 1,
                      });
                    }
                    if (item.dvariationPlaneH != null) {
                      content.push({
                        text: [
                          "位移(垂直)：" + item.dvariationPlaneH + "mm",
                          "#fff",
                          "12",
                        ],
                        text_offset: "10," + (content.length * 20 + 5),
                        text_centered: false,
                        text_boxwidth: 220,
                        scroll_speed: 1,
                      });
                    }
                    /*if (item.time != null) {
                      content.push({
                        text: ['时间：' + item.time, "#fff", "12"],
                        text_offset: "10," + (content.length * 20 + 5),
                        text_centered: false,
                        text_boxwidth: 220,
                        scroll_speed: 1,
                      })
                    }*/
                    let point = {
                      id: monitorsPoint.markerState + "_" + index,
                      coord: item.coord,
                      coord_z_type:
                        monitorsPoint.coordZType != null ? monitorsPoint.coordZType : 0,
                      coord_z: item.coord_z ? item.coord_z : 0,
                      point_type: "ssjk-wy", // item.type,
                      state: monitorsPoint.markerState,
                      always_show_label: false,
                      show_label_range: "0,50",
                      params: {
                        ...item,
                      },
                      marker: {
                        size: "30,30",
                        images: [
                          {
                            define_state: monitorsPoint.markerState,
                            normal_url: monitorsPoint.markerNormalUrl,
                            activate_url: monitorsPoint.markerActivateUrl,
                          },
                        ],
                      },
                      label: {
                        bg_size: "220," + (content.length * 20 + 5),
                        bg_offset: "12,35",
                        content: content,
                      },
                    };
                    customPoiList.push(point);
                    if (isYouCheIndex) {
                      this.unInitPoiList.push(point);
                    } else {
                      this.poiList.push(point);
                    }
                  }
                });
                if (customPoiList.length > 0 && !isYouCheIndex) {
                  digitalTwinApi.AddCustomPOI(customPoiList);
                }
              }
            });
          }
        });
      }
      // 渗压点
      if (
        this.digitalCon.percolationsPoints &&
        this.digitalCon.percolationsPoints.length > 0
      ) {
        this.digitalCon.percolationsPoints.forEach((percolationsPoint) => {
          if (percolationsPoint.httpAddress != null) {
            getMapPointListByUrl(percolationsPoint.httpAddress).then((res) => {
              if (res.code == 200 && res.data && res.data.length > 0) {
                this.treeCheckedKeys.push("ssjk-sy");
                // this.treeExpandedKeys.push('ssjk-sy')
                let data = res.data;
                let customPoiList = [];
                data.forEach((item, index) => {
                  if (item.coord != null && item.coord != "") {
                    let content = [];
                    let label = item.label;
                    content.push({
                      text: ["测点：" + label, "#fff", "12"],
                      text_offset: "10,5",
                      text_centered: false,
                      text_boxwidth: 220,
                      scroll_speed: 1,
                    });
                    /*if (item.pressure != null) {
                      content.push({
                        text: ["压强：" + item.pressure + "kPa", "#fff", "12"],
                        text_offset: "10,25",
                        text_centered: false,
                        text_boxwidth: 220,
                        scroll_speed: 1,
                      });
                    }*/
                    if (item.waterElevation != null) {
                      content.push({
                        text: ["水位高程：" + item.waterElevation + "m", "#fff", "12"],
                        text_offset: "10," + (content.length * 20 + 5),
                        text_centered: false,
                        text_boxwidth: 220,
                        scroll_speed: 1,
                      });
                    }
                    /*if (item.time != null) {
                      content.push({
                        text: ['时间：' + item.time, "#fff", "12"],
                        text_offset: "10," + (content.length * 20 + 5),
                        text_centered: false,
                        text_boxwidth: 220,
                        scroll_speed: 1,
                      })
                    }*/
                    let point = {
                      id: percolationsPoint.markerState + "_" + index,
                      coord: item.coord,
                      coord_z_type:
                        percolationsPoint.coordZType != null
                          ? percolationsPoint.coordZType
                          : 0,
                      coord_z: item.coord_z ? item.coord_z : 0,
                      point_type: "ssjk-sy", // item.type,
                      state: percolationsPoint.markerState,
                      always_show_label: false,
                      show_label_range: "0,50",
                      params: {
                        ...item,
                      },
                      marker: {
                        size: "30,30",
                        images: [
                          {
                            define_state: percolationsPoint.markerState,
                            normal_url: percolationsPoint.markerNormalUrl,
                            activate_url: percolationsPoint.markerActivateUrl,
                          },
                        ],
                      },
                      label: {
                        bg_size: "220," + (content.length * 20 + 5),
                        bg_offset: "12,35",
                        content: content,
                      },
                    };
                    customPoiList.push(point);
                    if (isYouCheIndex) {
                      this.unInitPoiList.push(point);
                    } else {
                      this.poiList.push(point);
                    }
                  }
                });
                if (customPoiList.length > 0 && !isYouCheIndex) {
                  digitalTwinApi.AddCustomPOI(customPoiList);
                }
              }
            });
          }
        });
      }
      // 物资仓库
      if (this.digitalCon.warehousePoints && this.digitalCon.warehousePoints.length > 0) {
        this.digitalCon.warehousePoints.forEach((warehousePoint) => {
          if (warehousePoint.httpAddress != null) {
            getMapPointListByUrl(warehousePoint.httpAddress).then((res) => {
              if (res.code == 200 && res.data && res.data.length > 0) {
                this.treeCheckedKeys.push("fybz-wzck");
                // this.treeExpandedKeys.push('fybz-wzck')
                let data = res.data;
                let customPoiList = [];
                data.forEach((item, index) => {
                  if (
                    item.lon != null &&
                    item.lon != "" &&
                    item.lat != null &&
                    item.lat != ""
                  ) {
                    let content = [];
                    let label = item.name;
                    content.push({
                      text: [label, "#fff", "12"],
                      text_offset: "10,5",
                      text_centered: false,
                      text_boxwidth: label.length * 16,
                      scroll_speed: 1,
                    });
                    let point = {
                      id: warehousePoint.markerState + "_" + index,
                      coord: item.lon + "," + item.lat,
                      coord_z_type:
                        warehousePoint.coordZType != null ? warehousePoint.coordZType : 0,
                      coord_z: item.coord_z ? item.coord_z : 0,
                      point_type: "fybz-wzck", //warehousePoint.type,
                      state: warehousePoint.markerState,
                      always_show_label: false,
                      show_label_range: "0,50",
                      params: {
                        path: item.path,
                        vtourUrl: item.path,
                      },
                      marker: {
                        size: "30,30",
                        images: [
                          {
                            define_state: warehousePoint.markerState,
                            normal_url: warehousePoint.markerNormalUrl,
                            activate_url: warehousePoint.markerActivateUrl,
                          },
                        ],
                      },
                      label: {
                        bg_size: label.length * 16 + 26 + "," + content.length * 25,
                        bg_offset: "12,35", //整个label左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
                        content: content,
                      },
                    };
                    customPoiList.push(point);
                    if (isYouCheIndex) {
                      this.unInitPoiList.push(point);
                    } else {
                      this.poiList.push(point);
                    }
                  }
                });
                if (customPoiList.length > 0 && !isYouCheIndex) {
                  digitalTwinApi.AddCustomPOI(customPoiList);
                }
              }
            });
          }
        });
      }
      // 无人机机场
      if (this.digitalCon.dronePoints && this.digitalCon.dronePoints.length > 0) {
        this.digitalCon.dronePoints.forEach((dronePoint) => {
          if (dronePoint.httpAddress != null) {
            getMapPointListByUrl(dronePoint.httpAddress).then((res) => {
              if (res.code == 200 && res.data && res.data.length > 0) {
                this.treeCheckedKeys.push("ssjk-wrjjc");
                // this.treeExpandedKeys.push('ssjk-wrjjc')
                let data = res.data;
                let customPoiList = [];
                data.forEach((item, index) => {
                  if (
                    item.lon != null &&
                    item.lon != "" &&
                    item.lat != null &&
                    item.lat != ""
                  ) {
                    let content = [];
                    let label = item.name;
                    content.push({
                      text: [label, "#fff", "12"],
                      text_offset: "10,5",
                      text_centered: false,
                      text_boxwidth: label.length * 16,
                      scroll_speed: 1,
                    });
                    let point = {
                      id: dronePoint.markerState + "_" + index,
                      coord: item.lon + "," + item.lat,
                      coord_z_type:
                        dronePoint.coordZType != null ? dronePoint.coordZType : 0,
                      coord_z: item.coord_z ? item.coord_z : 0,
                      point_type: "ssjk-wrjjc", //dronePoint.type,
                      state: dronePoint.markerState,
                      always_show_label: false,
                      show_label_range: "0,50",
                      params: {
                        path: item.path,
                      },
                      marker: {
                        size: "30,30",
                        images: [
                          {
                            define_state: dronePoint.markerState,
                            normal_url: dronePoint.markerNormalUrl,
                            activate_url: dronePoint.markerActivateUrl,
                          },
                        ],
                      },
                      label: {
                        bg_size: label.length * 16 + 26 + "," + content.length * 25,
                        bg_offset: "12,35", //整个label左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
                        content: content,
                      },
                    };
                    customPoiList.push(point);
                    if (isYouCheIndex) {
                      this.unInitPoiList.push(point);
                    } else {
                      this.poiList.push(point);
                    }
                  }
                });
                if (customPoiList.length > 0 && !isYouCheIndex) {
                  digitalTwinApi.AddCustomPOI(customPoiList);
                }
              }
            });
          }
        });
      }
      // 全景点
      if (this.digitalCon.photosPoints && this.digitalCon.photosPoints.length > 0) {
        this.digitalCon.photosPoints.forEach((photosPoint) => {
          if (photosPoint.httpAddress != null) {
            getMapPointListByUrl(photosPoint.httpAddress).then((res) => {
              if (res.code == 200 && res.data && res.data.length > 0) {
                this.treeCheckedKeys.push("ssjk-qjd");
                // this.treeExpandedKeys.push('ssjk-qjd')
                let data = res.data;
                let customPoiList = [];
                data.forEach((item, index) => {
                  if (
                    item.lon != null &&
                    item.lon != "" &&
                    item.lat != null &&
                    item.lat != ""
                  ) {
                    let content = [];
                    let label = item.name;
                    content.push({
                      text: [label, "#fff", "12"],
                      text_offset: "10,5",
                      text_centered: false,
                      text_boxwidth: label.length * 16,
                      scroll_speed: 1,
                    });
                    let point = {
                      id: photosPoint.markerState + "_" + index,
                      coord: item.lon + "," + item.lat,
                      coord_z_type:
                        photosPoint.coordZType != null ? photosPoint.coordZType : 0,
                      coord_z: item.coord_z ? item.coord_z : 0,
                      point_type: "ssjk-qjd", //photosPoint.type,
                      state: photosPoint.markerState,
                      always_show_label: false,
                      show_label_range: "0,50",
                      params: {
                        path: item.path,
                        vtourUrl: item.path,
                      },
                      marker: {
                        size: "30,30",
                        images: [
                          {
                            define_state: photosPoint.markerState,
                            normal_url: photosPoint.markerNormalUrl,
                            activate_url: photosPoint.markerActivateUrl,
                          },
                        ],
                      },
                      label: {
                        bg_size: label.length * 16 + 26 + "," + content.length * 25,
                        bg_offset: "12,35", //整个label左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
                        content: content,
                      },
                    };
                    customPoiList.push(point);
                    if (isYouCheIndex) {
                      this.unInitPoiList.push(point);
                    } else {
                      this.poiList.push(point);
                    }
                  }
                });
                if (customPoiList.length > 0 && !isYouCheIndex) {
                  digitalTwinApi.AddCustomPOI(customPoiList);
                }
              }
            });
          }
        });
      }
      // 视频点
      if (this.digitalCon.videoPoints && this.digitalCon.videoPoints.length > 0) {
        this.digitalCon.videoPoints.forEach((videoPoint) => {
          if (videoPoint.httpAddress != null) {
            getMapPointListByUrl(videoPoint.httpAddress).then((res) => {
              if (res.code == 200 && res.data && res.data.length > 0) {
                this.treeCheckedKeys.push("ssjk-spd");
                // this.treeExpandedKeys.push('ssjk-spd')
                let data = res.data;
                let customPoiList = [];
                data.forEach((item, index) => {
                  if (
                    item.lon != null &&
                    item.lon != "" &&
                    item.lat != null &&
                    item.lat != ""
                  ) {
                    let content = [];
                    let label = item.label;
                    content.push({
                      text: [label, "#fff", "12"],
                      text_offset: "10,5",
                      text_centered: false,
                      text_boxwidth: label.length * 16,
                      scroll_speed: 1,
                    });
                    let point = {
                      id: videoPoint.markerState + "_" + index,
                      coord: item.lon + "," + item.lat,
                      coord_z_type:
                        videoPoint.coordZType != null ? videoPoint.coordZType : 0,
                      coord_z: item.coord_z ? item.coord_z : 0,
                      point_type: "ssjk-spd", //videoPoint.type,
                      state: videoPoint.markerState,
                      always_show_label: false,
                      show_label_range: "0,50",
                      params: {
                        ...item,
                      },
                      marker: {
                        size: "30,30",
                        images: [
                          {
                            define_state: videoPoint.markerState,
                            normal_url: videoPoint.markerNormalUrl,
                            activate_url: videoPoint.markerActivateUrl,
                          },
                        ],
                      },
                      label: {
                        bg_size: label.length * 16 + 26 + "," + content.length * 25,
                        bg_offset: "12,35", //整个label左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
                        content: content,
                      },
                      // window: {
                      //   "url": "http://192.168.1.38/videoPointPlayHtm?inputType=" + item.inputType
                      //     + "&deviceNumber=" + item.deviceNumber + "&channelNumber=" + item.channelNumber + "&mainProtocol=" + item.mainProtocol + "&videoCoding=" + item.videoCoding,
                      //   //本地地址一: "file:///D:/xxx/echarts.html",    D: 在线席位所在盘符
                      //   "size":"400,240",      //window大小(宽,高 单位:像素)
                      //   "offset":"-200,270"      //window左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
                      // }
                    };
                    customPoiList.push(point);
                    if (isYouCheIndex) {
                      this.unInitPoiList.push(point);
                    } else {
                      this.poiList.push(point);
                    }
                  }
                });
                if (customPoiList.length > 0 && !isYouCheIndex) {
                  digitalTwinApi.AddCustomPOI(customPoiList);
                }
              }
            });
          }
        });
      }
      // 雨量站
      if (this.digitalCon.stationPoints && this.digitalCon.stationPoints.length > 0) {
        this.digitalCon.stationPoints.forEach((stationPoint) => {
          if (stationPoint.httpAddress != null) {
            getMapPointListByUrl(stationPoint.httpAddress).then((res) => {
              if (res.code == 200 && res.data && res.data.length > 0) {
                this.treeCheckedKeys.push("ssjk-ylz");
                // this.treeExpandedKeys.push('ssjk-ylz')
                let data = res.data;
                let customPoiList = [];
                data.forEach((item, index) => {
                  if (
                    item.lgtd != null &&
                    item.lgtd != "" &&
                    item.lttd != null &&
                    item.lttd != ""
                  ) {
                    let content = [];
                    let label = item.stnm;
                    content.push({
                      text: [label, "#fff", "12"],
                      text_offset: "10,5",
                      text_centered: false,
                      text_boxwidth: 220,
                      scroll_speed: 1,
                    });
                    if (item.drp != null && item.tm != null) {
                      content.push({
                        text: ["雨量：" + item.drp + "mm", "#fff", "12"],
                        text_offset: "10,25",
                        text_centered: false,
                        text_boxwidth: 220,
                        scroll_speed: 1,
                      });
                      content.push({
                        text: ["时间：" + item.tm, "#fff", "12"],
                        text_offset: "10,45",
                        text_centered: false,
                        text_boxwidth: 220,
                        scroll_speed: 1,
                      });
                    }
                    let point = {
                      id: stationPoint.markerState + "_" + index,
                      coord: item.lgtd + "," + item.lttd,
                      coord_z_type:
                        stationPoint.coordZType != null ? stationPoint.coordZType : 0,
                      coord_z: item.coord_z ? item.coord_z : 0,
                      point_type: "ssjk-ylz", //stationPoint.type,
                      state: stationPoint.markerState,
                      always_show_label: false,
                      show_label_range: "0,50",
                      params: {
                        stcd: item.stcd,
                        stnm: item.stnm,
                        type: "PP",
                      },
                      marker: {
                        size: "30,30",
                        images: [
                          {
                            define_state: stationPoint.markerState,
                            normal_url: stationPoint.markerNormalUrl,
                            activate_url: stationPoint.markerActivateUrl,
                          },
                        ],
                      },
                      label: {
                        bg_size: "220," + (content.length * 20 + 5),
                        bg_offset: "12,35", //整个label左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
                        content: content,
                      },
                    };
                    customPoiList.push(point);
                    if (isYouCheIndex) {
                      this.unInitPoiList.push(point);
                    } else {
                      this.poiList.push(point);
                    }
                  }
                });
                if (customPoiList.length > 0 && !isYouCheIndex) {
                  digitalTwinApi.AddCustomPOI(customPoiList);
                }
              }
            });
          }
        });
      }
      // 水位站
      if (
        this.digitalCon.waterLevelPoints &&
        this.digitalCon.waterLevelPoints.length > 0
      ) {
        this.digitalCon.waterLevelPoints.forEach((waterLevelPoint) => {
          if (waterLevelPoint.httpAddress != null) {
            getMapPointListByUrl(waterLevelPoint.httpAddress).then((res) => {
              if (res.code == 200 && res.data && res.data.length > 0) {
                this.treeCheckedKeys.push("ssjk-swz");
                // this.treeExpandedKeys.push('ssjk-swz')
                let data = res.data;
                let customPoiList = [];
                data.forEach((item, index) => {
                  if (
                    item.lgtd != null &&
                    item.lgtd != "" &&
                    item.lttd != null &&
                    item.lttd != ""
                  ) {
                    let content = [];
                    let label = "测站：" + item.stnm;
                    content.push({
                      text: [label, "#fff", "12"],
                      text_offset: "10,5",
                      text_centered: false,
                      text_boxwidth: 220,
                      scroll_speed: 1,
                    });
                    if (item.rz != null && item.tm != null) {
                      content.push({
                        text: ["水位：" + item.rz + "m", "#fff", "12"],
                        text_offset: "10,25",
                        text_centered: false,
                        text_boxwidth: 220,
                        scroll_speed: 1,
                      });
                      content.push({
                        text: ["时间：" + item.tm, "#fff", "12"],
                        text_offset: "10,45",
                        text_centered: false,
                        text_boxwidth: 220,
                        scroll_speed: 1,
                      });
                    }
                    let point = {
                      id: waterLevelPoint.markerState + "_" + index,
                      coord: item.lgtd + "," + item.lttd,
                      coord_z_type:
                        waterLevelPoint.coordZType != null
                          ? waterLevelPoint.coordZType
                          : 0,
                      coord_z: item.coord_z ? item.coord_z : 0,
                      point_type: "ssjk-swz", // waterLevelPoint.type,
                      state: waterLevelPoint.markerState,
                      always_show_label: false,
                      show_label_range: "0,50",
                      params: {
                        stcd: item.stcd,
                        stnm: item.stnm,
                        type: "RR",
                      },
                      marker: {
                        size: "30,30",
                        images: [
                          {
                            define_state: waterLevelPoint.markerState,
                            normal_url: waterLevelPoint.markerNormalUrl,
                            activate_url: waterLevelPoint.markerActivateUrl,
                          },
                        ],
                      },
                      label: {
                        bg_size: "220," + (content.length * 20 + 5),
                        bg_offset: "12,35", //整个label左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
                        content: content,
                      },
                    };
                    customPoiList.push(point);
                    if (isYouCheIndex) {
                      this.unInitPoiList.push(point);
                    } else {
                      this.poiList.push(point);
                    }
                  }
                });
                if (customPoiList.length > 0 && !isYouCheIndex) {
                  digitalTwinApi.AddCustomPOI(customPoiList);
                }
              }
            });
          }
        });
      }
      let that = this;
      setTimeout(() => {
        this.$nextTick(() => {
          if (isYouCheIndex) {
            // 更新数字孪生树勾选
            that.$refs.layerSelectRef.setCheckedKeys([]);
          } else {
            // 更新数字孪生树勾选
            that.$refs.layerSelectRef.setCheckedKeys(this.treeCheckedKeys);
          }
        });
      }, 2000);
      // 添加油车闸门上方VR教培特效
      let effectData = {
        id: "youche_zm_effect_id",
        coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
        cad_mapkey: "", //CAD基准点Key值, 项目中约定
        type: "circle_outside", //样式类型(见下表)
        scale: 1, //半径(单位:米; "adaptive":true 时含义为倍率)
        adaptive: false, //true:自适应大小;false:默认
        coord: "119.757812,31.219952", //坐标点 lng,lat
        coord_z: 50.266725, //高度(单位:米)
        coord_z_type: 2, //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
        pitch: 0, //俯仰角, 参考(-90~90)
        roll: 0, //翻滚角, 参考(0~360)
        yaw: 0, //偏航角, 参考(0正北, 0~360)
        text: ["VR教培,009AFF,4"], //文本内容, 富文本内容
        title_offset: "0,16", //文本偏移(单位:米; 东西向为x轴进行偏移)
        title_face_to_camera: true, //顶部文字是否跟踪朝向摄像机(注: true优先, "pitch", "roll", "yaw" 无效)
        title_text_portrait: false, //顶部文字排列方向(true: 纵向, false: 横向)
      };
      digitalTwinApi.initEffect(effectData);
    },
    // 配置化-初始化poi数据(复用地图展点接口)
    initMapPointByUrl() {
      // 位移点
      if (this.digitalCon.monitorsPoints && this.digitalCon.monitorsPoints.length > 0) {
        this.digitalCon.monitorsPoints.forEach((monitorsPoint) => {
          if (monitorsPoint.httpAddress != null) {
            getMapPointListByUrl(monitorsPoint.httpAddress).then((res) => {
              if (res.code == 200 && res.data && res.data.length > 0) {
                let data = res.data;
                let customPoiList = [];
                data.forEach((item, index) => {
                  if (item.lat != null && item.lat != "") {
                    let content = [];
                    let label = "位移:" + item.deviceCode;
                    content.push({
                      text: [label, "#fff", "12"],
                      text_offset: "10,5",
                      text_centered: false,
                      text_boxwidth: label.length * 16,
                      scroll_speed: 1,
                    });
                    let point = {
                      id: monitorsPoint.markerState + "_" + index,
                      coord: item.lon + "," + item.lat,
                      coord_z_type:
                        monitorsPoint.coordZType != null ? monitorsPoint.coordZType : 0,
                      coord_z: 0,
                      point_type: item.type, // item.type,
                      state: monitorsPoint.markerState,
                      always_show_label: false,
                      show_label_range: "0,80",
                      params: {
                        id: item.deviceCode,
                        type: item.type,
                        label: item.label,
                      },
                      marker: {
                        size: "30,30",
                        images: [
                          {
                            define_state: monitorsPoint.markerState,
                            normal_url: monitorsPoint.markerNormalUrl,
                            activate_url: monitorsPoint.markerActivateUrl,
                          },
                        ],
                      },
                      label: {
                        bg_size: label.length * 16 + 26 + "," + content.length * 25,
                        bg_offset: "12,35",
                        content: content,
                      },
                    };
                    customPoiList.push(point);
                    this.poiList.push(point);
                  }
                });
                if (customPoiList.length > 0) {
                  //console.log(" 添加位移信息", customPoiList);
                  digitalTwinApi.AddCustomPOI(customPoiList);
                }
              }
            });
          }
        });
      }
      // 渗压点
      if (
        this.digitalCon.percolationsPoints &&
        this.digitalCon.percolationsPoints.length > 0
      ) {
        this.digitalCon.percolationsPoints.forEach((percolationsPoint) => {
          if (percolationsPoint.httpAddress != null) {
            getMapPointListByUrl(percolationsPoint.httpAddress).then((res) => {
              if (res.code == 200 && res.data && res.data.length > 0) {
                let data = res.data;
                let customPoiList = [];
                data.forEach((item, index) => {
                  if (item.lat != null && item.lat != "") {
                    let content = [];
                    let label = "渗压:" + item.deviceCode;
                    content.push({
                      text: [label, "#fff", "12"],
                      text_offset: "10,5",
                      text_centered: false,
                      text_boxwidth: label.length * 16,
                      scroll_speed: 1,
                    });
                    let point = {
                      id: percolationsPoint.markerState + "_" + index,
                      coord: item.lon + "," + item.lat,
                      coord_z_type:
                        percolationsPoint.coordZType != null
                          ? percolationsPoint.coordZType
                          : 0,
                      coord_z: 0,
                      point_type: item.type, // item.type,
                      state: percolationsPoint.markerState,
                      always_show_label: false,
                      show_label_range: "0,80",
                      params: {
                        id: item.deviceCode,
                        type: item.type,
                        label: item.label,
                      },
                      marker: {
                        size: "30,30",
                        images: [
                          {
                            define_state: percolationsPoint.markerState,
                            normal_url: percolationsPoint.markerNormalUrl,
                            activate_url: percolationsPoint.markerActivateUrl,
                          },
                        ],
                      },
                      label: {
                        bg_size: label.length * 16 + 26 + "," + content.length * 25,
                        bg_offset: "12,35",
                        content: content,
                      },
                    };
                    customPoiList.push(point);
                    this.poiList.push(point);
                  }
                });
                if (customPoiList.length > 0) {
                  //console.log(" 添加渗压信息", customPoiList);
                  digitalTwinApi.AddCustomPOI(customPoiList);
                }
              }
            });
          }
        });
      }
      // 物资仓库
      if (this.digitalCon.warehousePoints && this.digitalCon.warehousePoints.length > 0) {
        this.digitalCon.warehousePoints.forEach((warehousePoint) => {
          if (warehousePoint.httpAddress != null) {
            getMapPointListByUrl(warehousePoint.httpAddress).then((res) => {
              if (res.code == 200 && res.data && res.data.length > 0) {
                let data = res.data;
                let customPoiList = [];
                data.forEach((item, index) => {
                  if (
                    item.lon != null &&
                    item.lon != "" &&
                    item.lat != null &&
                    item.lat != ""
                  ) {
                    let content = [];
                    let label = item.label;
                    content.push({
                      text: [label, "#fff", "12"],
                      text_offset: "10,5",
                      text_centered: false,
                      text_boxwidth: label.length * 16,
                      scroll_speed: 1,
                    });
                    let point = {
                      id: warehousePoint.markerState + "_" + index,
                      coord: item.lon + "," + item.lat,
                      coord_z_type:
                        warehousePoint.coordZType != null ? warehousePoint.coordZType : 0,
                      coord_z: item.coord_z ? item.coord_z : 0,
                      point_type: item.type, //warehousePoint.type,
                      state: warehousePoint.markerState,
                      always_show_label: false,
                      show_label_range: "0,80",
                      params: {
                        path: item.vtourUrl,
                        vtourUrl: item.vtourUrl,
                      },
                      marker: {
                        size: "30,30",
                        images: [
                          {
                            define_state: warehousePoint.markerState,
                            normal_url: warehousePoint.markerNormalUrl,
                            activate_url: warehousePoint.markerActivateUrl,
                          },
                        ],
                      },
                      label: {
                        bg_size: label.length * 16 + 26 + "," + content.length * 25,
                        bg_offset: "12,35", //整个label左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
                        content: content,
                      },
                    };
                    customPoiList.push(point);
                    this.poiList.push(point);
                  }
                });
                if (customPoiList.length > 0) {
                  //console.log(" 添加仓库信息", customPoiList);
                  digitalTwinApi.AddCustomPOI(customPoiList);
                }
              }
            });
          }
        });
      }
      // 无人机机场
      if (this.digitalCon.dronePoints && this.digitalCon.dronePoints.length > 0) {
        this.digitalCon.dronePoints.forEach((dronePoint) => {
          if (dronePoint.httpAddress != null) {
            getMapPointListByUrl(dronePoint.httpAddress).then((res) => {
              if (res.code == 200 && res.data && res.data.length > 0) {
                let data = res.data;
                let customPoiList = [];
                data.forEach((item, index) => {
                  if (
                    item.lon != null &&
                    item.lon != "" &&
                    item.lat != null &&
                    item.lat != ""
                  ) {
                    let content = [];
                    let label = item.name;
                    content.push({
                      text: [label, "#fff", "12"],
                      text_offset: "10,5",
                      text_centered: false,
                      text_boxwidth: label.length * 16,
                      scroll_speed: 1,
                    });
                    let point = {
                      id: dronePoint.markerState + "_" + index,
                      coord: item.lon + "," + item.lat,
                      coord_z_type:
                        dronePoint.coordZType != null ? dronePoint.coordZType : 0,
                      coord_z: item.coord_z ? item.coord_z : 0,
                      point_type: item.type, //dronePoint.type,
                      state: dronePoint.markerState,
                      always_show_label: false,
                      show_label_range: "0,80",
                      params: {
                        path: item.path,
                      },
                      marker: {
                        size: "30,30",
                        images: [
                          {
                            define_state: dronePoint.markerState,
                            normal_url: dronePoint.markerNormalUrl,
                            activate_url: dronePoint.markerActivateUrl,
                          },
                        ],
                      },
                      label: {
                        bg_size: label.length * 16 + 26 + "," + content.length * 25,
                        bg_offset: "12,35", //整个label左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
                        content: content,
                      },
                    };
                    customPoiList.push(point);
                    this.poiList.push(point);
                  }
                });
                if (customPoiList.length > 0) {
                  //console.log(" 添加无人机机场信息", customPoiList);
                  digitalTwinApi.AddCustomPOI(customPoiList);
                }
              }
            });
          }
        });
      }
      // 全景点
      if (this.digitalCon.photosPoints && this.digitalCon.photosPoints.length > 0) {
        this.digitalCon.photosPoints.forEach((photosPoint) => {
          if (photosPoint.httpAddress != null) {
            getMapPointListByUrl(photosPoint.httpAddress).then((res) => {
              if (res.code == 200 && res.data && res.data.length > 0) {
                let data = res.data;
                let customPoiList = [];
                data.forEach((item, index) => {
                  if (
                    item.lon != null &&
                    item.lon != "" &&
                    item.lat != null &&
                    item.lat != ""
                  ) {
                    let content = [];
                    let label = item.label;
                    content.push({
                      text: [label, "#fff", "12"],
                      text_offset: "10,5",
                      text_centered: false,
                      text_boxwidth: label.length * 16,
                      scroll_speed: 1,
                    });
                    let point = {
                      id: photosPoint.markerState + "_" + index,
                      coord: item.lon + "," + item.lat,
                      coord_z_type:
                        photosPoint.coordZType != null ? photosPoint.coordZType : 0,
                      coord_z: item.coord_z ? item.coord_z : 0,
                      point_type: item.type, //photosPoint.type,
                      state: photosPoint.markerState,
                      always_show_label: false,
                      show_label_range: "0,80",
                      params: {
                        path: item.vtourUrl,
                        vtourUrl: item.vtourUrl,
                      },
                      marker: {
                        size: "30,30",
                        images: [
                          {
                            define_state: photosPoint.markerState,
                            normal_url: photosPoint.markerNormalUrl,
                            activate_url: photosPoint.markerActivateUrl,
                          },
                        ],
                      },
                      label: {
                        bg_size: label.length * 16 + 26 + "," + content.length * 25,
                        bg_offset: "12,35", //整个label左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
                        content: content,
                      },
                    };
                    customPoiList.push(point);
                    this.poiList.push(point);
                  }
                });
                if (customPoiList.length > 0) {
                  //console.log(" 添加全景点信息", customPoiList);
                  digitalTwinApi.AddCustomPOI(customPoiList);
                }
              }
            });
          }
        });
      }
      // 视频点
      if (this.digitalCon.videoPoints && this.digitalCon.videoPoints.length > 0) {
        this.digitalCon.videoPoints.forEach((videoPoint) => {
          if (videoPoint.httpAddress != null) {
            getMapPointListByUrl(videoPoint.httpAddress).then((res) => {
              if (res.code == 200 && res.data && res.data.length > 0) {
                let data = res.data;
                let customPoiList = [];
                data.forEach((item, index) => {
                  if (
                    item.lon != null &&
                    item.lon != "" &&
                    item.lat != null &&
                    item.lat != ""
                  ) {
                    let content = [];
                    let label = item.label;
                    content.push({
                      text: [label, "#fff", "12"],
                      text_offset: "10,5",
                      text_centered: false,
                      text_boxwidth: label.length * 16,
                      scroll_speed: 1,
                    });
                    let point = {
                      id: videoPoint.markerState + "_" + index,
                      coord: item.lon + "," + item.lat,
                      coord_z_type:
                        videoPoint.coordZType != null ? videoPoint.coordZType : 0,
                      coord_z: item.coord_z ? item.coord_z : 0,
                      point_type: item.type, //videoPoint.type,
                      state: videoPoint.markerState,
                      always_show_label: false,
                      show_label_range: "0,80",
                      params: {
                        ...item,
                      },
                      marker: {
                        size: "30,30",
                        images: [
                          {
                            define_state: videoPoint.markerState,
                            normal_url: videoPoint.markerNormalUrl,
                            activate_url: videoPoint.markerActivateUrl,
                          },
                        ],
                      },
                      label: {
                        bg_size: label.length * 16 + 26 + "," + content.length * 25,
                        bg_offset: "12,35", //整个label左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
                        content: content,
                      },
                      // window: {
                      //   "url": "http://192.168.1.30:4273/login?redirect=%2Findex",
                      //   //本地地址一: "file:///D:/xxx/echarts.html",    D: 在线席位所在盘符
                      //   "size":"520,350",      //window大小(宽,高 单位:像素)
                      //   "offset":"50,180"      //window左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
                      // }
                    };
                    customPoiList.push(point);
                    this.poiList.push(point);
                  }
                });
                if (customPoiList.length > 0) {
                  //console.log(" 添加视频点信息", customPoiList);
                  digitalTwinApi.AddCustomPOI(customPoiList);
                }
              }
            });
          }
        });
      }
      // 雨量站
      if (this.digitalCon.stationPoints && this.digitalCon.stationPoints.length > 0) {
        this.digitalCon.stationPoints.forEach((stationPoint) => {
          if (stationPoint.httpAddress != null) {
            getMapPointListByUrl(stationPoint.httpAddress).then((res) => {
              if (res.code == 200 && res.data && res.data.length > 0) {
                let data = res.data;
                let customPoiList = [];
                data.forEach((item, index) => {
                  if (
                    item.lat != null &&
                    item.lat != "" &&
                    item.lon != null &&
                    item.lon != ""
                  ) {
                    let content = [];
                    let label = item.label;
                    content.push({
                      text: [label, "#fff", "12"],
                      text_offset: "10,5",
                      text_centered: false,
                      text_boxwidth: label.length * 16,
                      scroll_speed: 1,
                    });
                    let point = {
                      id: stationPoint.markerState + "_" + index,
                      coord: item.lon + "," + item.lat,
                      coord_z_type:
                        stationPoint.coordZType != null ? stationPoint.coordZType : 0,
                      coord_z: item.coord_z ? item.coord_z : 0,
                      point_type: item.type, //stationPoint.type,
                      state: stationPoint.markerState,
                      always_show_label: false,
                      show_label_range: "0,80",
                      params: {
                        stcd: item.id,
                        stnm: item.label,
                      },
                      marker: {
                        size: "30,30",
                        images: [
                          {
                            define_state: stationPoint.markerState,
                            normal_url: stationPoint.markerNormalUrl,
                            activate_url: stationPoint.markerActivateUrl,
                          },
                        ],
                      },
                      label: {
                        bg_size: label.length * 16 + 26 + "," + content.length * 25,
                        bg_offset: "12,35", //整个label左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
                        content: content,
                      },
                    };
                    customPoiList.push(point);
                    this.poiList.push(point);
                  }
                });
                if (customPoiList.length > 0) {
                  //console.log(" 添加雨量站信息", customPoiList);
                  digitalTwinApi.AddCustomPOI(customPoiList);
                }
              }
            });
          }
        });
      }
      // 水位站
      if (
        this.digitalCon.waterLevelPoints &&
        this.digitalCon.waterLevelPoints.length > 0
      ) {
        debugger
        this.digitalCon.waterLevelPoints.forEach((waterLevelPoint) => {
          if (waterLevelPoint.httpAddress != null) {
            getMapPointListByUrl(waterLevelPoint.httpAddress).then((res) => {
              if (res.code == 200 && res.data && res.data.length > 0) {
                let data = res.data;
                let customPoiList = [];
                data.forEach((item, index) => {
                  if (
                    item.lgtd != null &&
                    item.lgtd != "" &&
                    item.lttd != null &&
                    item.lttd != ""
                  ) {
                    let content = [];
                    let label = item.stnm;
                    content.push({
                      text: [label, "#fff", "12"],
                      text_offset: "10,5",
                      text_centered: false,
                      text_boxwidth: label.length * 16,
                      scroll_speed: 1,
                    });
                    let point = {
                      id: waterLevelPoint.markerState + "_" + index,
                      coord: item.lgtd + "," + item.lttd,
                      coord_z_type:
                        waterLevelPoint.coordZType != null
                          ? waterLevelPoint.coordZType
                          : 0,
                      coord_z: item.coord_z ? item.coord_z : 0,
                      point_type: item.type, // waterLevelPoint.type,
                      state: waterLevelPoint.markerState,
                      always_show_label: false,
                      show_label_range: "0,80",
                      params: {
                        stcd: item.stcd,
                        stnm: item.stnm,
                      },
                      marker: {
                        size: "30,30",
                        images: [
                          {
                            define_state: waterLevelPoint.markerState,
                            normal_url: waterLevelPoint.markerNormalUrl,
                            activate_url: waterLevelPoint.markerActivateUrl,
                          },
                        ],
                      },
                      label: {
                        bg_size: label.length * 16 + 26 + "," + content.length * 25,
                        bg_offset: "12,35", //整个label左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
                        content: content,
                      },
                    };
                    customPoiList.push(point);
                    this.poiList.push(point);
                  }
                });
                if (customPoiList.length > 0) {
                  //console.log(" 添加水位站信息", customPoiList);
                  digitalTwinApi.AddCustomPOI(customPoiList);
                }
              }
            });
          }
        });
      }
    },
    // 添加横山漫游特效点
    initHSSKCameraRoamingPoi() {
      let jsondata = {
        id: "hssk_cameraroaming_effect_id",
        coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
        cad_mapkey: "", //CAD基准点Key值, 项目中约定
        type: "circle_outside", //样式类型(见下表)
        scale: 2, //半径(单位:米; "adaptive":true 时含义为倍率)
        adaptive: false, //true:自适应大小;false:默认
        coord: "119.566124,31.246096", //坐标点 lng,lat
        coord_z: 42.29125, //高度(单位:米)
        coord_z_type: 2, //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
        pitch: 0, //俯仰角, 参考(-90~90)
        roll: 0, //翻滚角, 参考(0~360)
        yaw: 0, //偏航角, 参考(0正北, 0~360)
        title_text: "", //文本内容, 富文本内容
        title_offset: "0,0", //文本偏移(单位:米; 东西向为x轴进行偏移)
        title_face_to_camera: false, //顶部文字是否跟踪朝向摄像机(注: true优先, "pitch", "roll", "yaw" 无效)
        title_text_portrait: false, //顶部文字排列方向(true: 纵向, false: 横向)
      };
      this.cloudRender.SuperAPI("AddEffect", jsondata, (status) => {
        console.log(status); //成功、失败回调
      });
    },
    // 横山水库镜头漫游
    hsskCamreraRoamingPlay() {
      digitalTwinApi.hsskCamreraRoamingPlay();
    },
    //获取场景相机参数
    getCameraParams() {
      let that = this;
      let jsondata = {
        coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
        cad_mapkey: "", //CAD基准点Key值, 项目中约定
      };
      that.cloudRender.SuperAPI("GetCameraInfo", jsondata).then((e) => {
        console.log("相机参数", e);
        let jo = JSON.parse(e);
        let args = jo.args;
        console.log("相机参数 args", args);
        if (args != null) {
          that.digitalCon.firstMapViewerOption = args;
          that.digitalCon.firstMapViewerOption.fly = false;
        }
      });
    },
    //工具集--镜头自动旋转
    cameraRotate() {
      let headRotate = 0;
      let timer = setInterval((i) => {
        for (let i = 0; i < 359; i++) {
          headRotate++;
        }
      }, 1000);
      return headRotate;
    },
    // 工具集--指南针显示隐藏
    showHideCompass(show) {
      digitalTwinApi.showHideCompass({ bshow: show });
    },
    // 工具集--获取屏幕内覆盖物ID
    getFullSceenCoveringId(covering_type) {
      let jo = {
        covering_type: "all",
      };
      if (covering_type != null) {
        jo.covering_type = covering_type;
      }
      this.cloudRender.SuperAPI("GetFullSceenCoveringId", jo, (state) => {
        console.log("GetFullSceenCoveringId", state);
      });
    },
    // 工具集--修改场景天气
    switchWeather(weather) {
      if (weather) {
        this.cloudRender.SuperAPI("SetEnvWeather", { env_weather: weather }, (statue) => {
          console.log("SetEnvWeather", weather, statue);
        });
      } else {
        if (this.digitalCon.weatherCityCode) {
          this.getWeatherInfo(this.digitalCon.weatherCityCode);
        } else {
          this.cloudRender.SuperAPI(
            "SetEnvWeather",
            { env_weather: "Sunny" },
            (statue) => {
              console.log("SetEnvWeather", "Sunny", statue);
            }
          );
        }
      }
    },
    // 工具集--修改场景时间
    switchDate(date) {
      if (date != null) {
        this.cloudRender.SuperAPI("SetEnvTime", { env_time: date }, (status) => {
          console.log("SetEnvTime", date, status); //成功、失败回调
        });
      } else {
        let now = new Date();
        let dateStr = now.getHours() + ":" + now.getMinutes();
        this.cloudRender.SuperAPI("SetEnvTime", { env_time: dateStr }, (status) => {
          console.log("SetEnvTime", dateStr, status); //成功、失败回调
        });
      }
    },
    // 工具集--限制场景镜头视界
    setCameraSpace() {
      if (
        this.digitalCon.cameraSpace &&
        this.digitalCon.cameraSpace.points &&
        this.digitalCon.cameraSpace.points.length > 0
      ) {
        digitalTwinApi.setCameraSpace(this.digitalCon.cameraSpace);
      }
    },
    // 工具集--解除场景镜头视界限制
    resetCameraSpace(state) {
      digitalTwinApi.resetCameraSpace(state);
    },
    // 工具集--开启地图标点  coordType: 坐标类型(0:经纬度坐标, 1:cad坐标)
    startGetCoord(coordType, cadMapKey, coordZType, coordinateShow, iconShow) {
      let jsondata = {
        coord_type: coordType || 0,
        cad_mapkey: cadMapKey || "",
        coord_z_type: coordZType || 0,
        coordinate_show: coordinateShow || false,
        icon_show: iconShow || false,
      };
      this.cloudRender.SuperAPI("StartGetCoord", jsondata).then((e) => {
        console.log("StartGetCoord", e);
      });
    },
    // 工具集--结束地图标点
    endGetCoord() {
      this.cloudRender.SuperAPI("EndGetCoord");
    },
    // 工具集--设置场景渲染质量 low medium high epic
    setRenderQuality(quality) {
      let jo = {
        quality: quality,
      };
      this.cloudRender.SuperAPI("SetRenderQuality", jo, (statue) => {
        console.log("SetRenderQuality", statue);
      });
    },
    // 工具集--设置镜头绕场景中心旋转 time 相机绕一周时间(秒) direction clockwise顺时针  anticlockwise:逆时针  stop:停止旋转
    setCameraRotate(time, direction) {
      let jo = {
        time: time,
        direction: direction,
      };
      this.cloudRender.SuperAPI("SetCameraRotate", jo, (e) => {});
    },
    // 工具集--显示隐藏场景帧率
    showHideUEFrameRate() {
      this.cloudRender.SuperAPI("ShowHideUEFrameRate", (statue) => {
        console.log("ShowHideUEFrameRate", statue);
      });
    },
    // 工具集--注册键盘事件
    startKeyboard() {
      this.cloudRender.SuperAPI("keyboard");
    },
    // 工具集--删除键盘事件
    removeKeyboard() {
      this.cloudRender.SuperAPI("removekeyboard");
    },
    //工具集--镜头移动
    moveCamera() {
      let jsondata = {
        state: "front", //视口移动方向(front: 前; back: 后; left: 左; right:右)
        step: 100, //步长设定
      };
      this.cloudRender.SuperAPI("SetCameraStepMove", jsondata).then((_back) => {
        console.log("镜头移动事件：" + _back);
      });
    },
    //工具集--场景漫游
    sceneRoam() {
      digitalTwinApi.roamingMove(
        xzRoamingCoords,
        "path_xhz"
      );
    },
    //沙沟廊道巡查
    gateInnerInspection(){
      let jsondata = {
        "coord_type": 0,            //坐标类型(0:经纬度坐标, 1:cad坐标)
        "cad_mapkey": "",           //CAD基准点Key值, 项目中约定
        "coord_z_type": 2,          //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
        "subsidiary_show":false,     //是否显示辅助线(true:显示; false:不显示)
        "points": [
            {
                "coord":"113.553999,33.282832",        //路径坐标点 lng,lat
                "coord_z":96.636925,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":3.455802,                   //镜头与坐标点距离(单位:米)
                "pitch":8.0,                           //镜头俯仰角(0~89)
                "yaw":124.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":10,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)
            },
            {
                "coord":"113.554079,33.282780",        //路径坐标点 lng,lat
                "coord_z":95.993553,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":2.358676,                   //镜头与坐标点距离(单位:米)
                "pitch":8.0,                           //镜头俯仰角(0~89)
                "yaw":127.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":10,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)
            },
            {
                "coord":"113.554201,33.282706",        //路径坐标点 lng,lat
                "coord_z":91.567581,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":1.0,                   //镜头与坐标点距离(单位:米)
                "pitch":32.683167,                           //镜头俯仰角(0~89)
                "yaw":126.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":10,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)
            },
            {
                "coord":"113.554449,33.282553",        //路径坐标点 lng,lat
                "coord_z":80.609894,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":1.0,                   //镜头与坐标点距离(单位:米)
                "pitch":32.683167,                           //镜头俯仰角(0~89)
                "yaw":126.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":15,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)
            },
            {
                "coord":"113.554554,33.282488",        //路径坐标点 lng,lat
                "coord_z":80.609894,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":1.0,                   //镜头与坐标点距离(单位:米)
                "pitch":8.0,                           //镜头俯仰角(0~89)
                "yaw":127.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":15,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)
            },
            {
                "coord":"113.554746,33.282369",        //路径坐标点 lng,lat
                "coord_z":80.609894,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":1.0,                   //镜头与坐标点距离(单位:米)
                "pitch":8.0,                           //镜头俯仰角(0~89)
                "yaw":127.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":15,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)
            },
            {
                "coord":"113.554925,33.282258",        //路径坐标点 lng,lat
                "coord_z":80.609894,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":1.0,                   //镜头与坐标点距离(单位:米)
                "pitch":8.0,                           //镜头俯仰角(0~89)
                "yaw":127.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":15,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)
            },
            {
                "coord":"113.555108,33.282145",        //路径坐标点 lng,lat
                "coord_z":80.609894,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":1.0,                   //镜头与坐标点距离(单位:米)
                "pitch":8.0,                           //镜头俯仰角(0~89)
                "yaw":127.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":15,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)
            },
            {
                "coord":"113.555347,33.281996",        //路径坐标点 lng,lat
                "coord_z":80.609894,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":1.0,                   //镜头与坐标点距离(单位:米)
                "pitch":8.0,                           //镜头俯仰角(0~89)
                "yaw":127.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":15,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)
            },
            {
                "coord":"113.555631,33.281822",        //路径坐标点 lng,lat
                "coord_z":80.609894,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":1.0,                   //镜头与坐标点距离(单位:米)
                "pitch":8.0,                           //镜头俯仰角(0~89)
                "yaw":127.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":15,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)
            },
            {
                "coord":"113.555884,33.281665",        //路径坐标点 lng,lat
                "coord_z":80.609894,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":1.0,                   //镜头与坐标点距离(单位:米)
                "pitch":8.0,                           //镜头俯仰角(0~89)
                "yaw":127.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":15,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)
            },
            {
                "coord":"113.556076,33.281546",        //路径坐标点 lng,lat
                "coord_z":80.609894,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":1.0,                   //镜头与坐标点距离(单位:米)
                "pitch":8.0,                           //镜头俯仰角(0~89)
                "yaw":127.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":15,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)
            },
            {
                "coord":"113.556555,33.281251",        //路径坐标点 lng,lat
                "coord_z":80.609894,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":1.0,                   //镜头与坐标点距离(单位:米)
                "pitch":8.0,                           //镜头俯仰角(0~89)
                "yaw":127.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":10,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)//转弯了
            },
            {
                "coord":"113.556640,33.281202",        //路径坐标点 lng,lat
                "coord_z":80.609894,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":1.0,                   //镜头与坐标点距离(单位:米)
                "pitch":8.0,                           //镜头俯仰角(0~89)
                "yaw":127.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":10,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)//
            },
            {
                "coord":"113.556767,33.281132",        //路径坐标点 lng,lat
                "coord_z":80.609894,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":1.0,                   //镜头与坐标点距离(单位:米)
                "pitch":8.0,                           //镜头俯仰角(0~89)
                "yaw":127.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":10,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)//
            },
            {
                "coord":"113.556868,33.281087",        //路径坐标点 lng,lat
                "coord_z":80.609894,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":1.0,                   //镜头与坐标点距离(单位:米)
                "pitch":8.0,                           //镜头俯仰角(0~89)
                "yaw":127.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":10,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)//
            },
            {
                "coord":"113.556936,33.281060",        //路径坐标点 lng,lat
                "coord_z":80.609894,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":1.0,                   //镜头与坐标点距离(单位:米)
                "pitch":8.0,                           //镜头俯仰角(0~89)
                "yaw":127.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":10,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)//
            },
            {
                "coord":"113.557041,33.281023",        //路径坐标点 lng,lat
                "coord_z":80.609894,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":1.0,                   //镜头与坐标点距离(单位:米)
                "pitch":8.0,                           //镜头俯仰角(0~89)
                "yaw":127.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":10,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)//
            },
            {
                "coord":"113.557115,33.280999",        //路径坐标点 lng,lat
                "coord_z":80.609894,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":1.0,                   //镜头与坐标点距离(单位:米)
                "pitch":8.0,                           //镜头俯仰角(0~89)
                "yaw":127.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":10,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)//
            },
            {
                "coord":"113.557190,33.280982",        //路径坐标点 lng,lat
                "coord_z":80.609894,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":1.0,                   //镜头与坐标点距离(单位:米)
                "pitch":8.0,                           //镜头俯仰角(0~89)
                "yaw":127.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":10,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)//
            },
            {
                "coord":"113.557281,33.280963",        //路径坐标点 lng,lat
                "coord_z":80.609894,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":1.0,                   //镜头与坐标点距离(单位:米)
                "pitch":8.0,                           //镜头俯仰角(0~89)
                "yaw":127.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":10,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)//
            },
            {
                "coord":"113.557412,33.280938",        //路径坐标点 lng,lat
                "coord_z":80.609894,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":1.0,                   //镜头与坐标点距离(单位:米)
                "pitch":8.0,                           //镜头俯仰角(0~89)
                "yaw":103.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":10,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)//
            },
            {
                "coord":"113.557531,33.280923",        //路径坐标点 lng,lat
                "coord_z":80.609894,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":1.0,                   //镜头与坐标点距离(单位:米)
                "pitch":8.0,                           //镜头俯仰角(0~89)
                "yaw":98.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":10,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)//
            },
            {
                "coord":"113.557654,33.280917",        //路径坐标点 lng,lat
                "coord_z":80.609894,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":1.0,                   //镜头与坐标点距离(单位:米)
                "pitch":8.0,                           //镜头俯仰角(0~89)
                "yaw":93.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":10,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)//
            },
            {
                "coord":"113.557823,33.280909",        //路径坐标点 lng,lat
                "coord_z":85.785255,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":1.0,                   //镜头与坐标点距离(单位:米)
                "pitch":8.0,                           //镜头俯仰角(0~89)
                "yaw":94.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":15,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)//
            },
            {
                "coord":"113.558179,33.280889",        //路径坐标点 lng,lat
                "coord_z":96.13633,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":1.0,                   //镜头与坐标点距离(单位:米)
                "pitch":8.0,                           //镜头俯仰角(0~89)
                "yaw":95.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":10,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)//
            },
            {
                "coord":"113.558245,33.280887",        //路径坐标点 lng,lat
                "coord_z":96.13633,                        //高度(单位:米, cad坐标无效)
                "coord_easetype":"linear",            //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                "arm_distance":1.0,                   //镜头与坐标点距离(单位:米)
                "pitch":8.0,                           //镜头俯仰角(0~89)
                "yaw":98.0,                             //镜头偏航角(0正北, 0~359)
                "attitude_easetype":"Linear",         //镜头漫游至下一坐标点缓动姿态类型(见下表)
                "time":10,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                "speed_easetype":"linear"             //镜头漫游速度类型(见下表)//
            }
        ]
      }
      this.cloudRender.SuperAPI("SetCameraRoamingPro", jsondata, (status) => {
          console.log(status); //成功、失败回调
      })
    },
    gateInnerPause(){
      let jsondata = {
          "state":"pause"      //pause:暂停移动; continue:继续移动; stop:停止移动, 释放焦点
      }
      cloudRender.SuperAPI("SetCameraRoamingProState", jsondata, (status) => {
          console.log(status); //成功、失败回调
      })
    },
    gateInnerContinue(){
      let jsondata = {
          "state":"continue"      //pause:暂停移动; continue:继续移动; stop:停止移动, 释放焦点
      }
      cloudRender.SuperAPI("SetCameraRoamingProState", jsondata, (status) => {
          console.log(status); //成功、失败回调
      })
    },
    gateInnerStop(){
      let jsondata = {
          "state":"stop"      //pause:暂停移动; continue:继续移动; stop:停止移动, 释放焦点
      }
      cloudRender.SuperAPI("SetCameraRoamingProState", jsondata, (status) => {
          console.log(status); //成功、失败回调
      })
    },
    //油车虚拟巡查--人工巡查
    personInspection() {
      let jo = {
        eid: "-9150751364756233164",
        coord_type: 0,
        cad_mapkey: "",
        scale: 1,
        coord: roamingCoords[0]["coord"],
        coord_z_type: 2,
        coord_z: roamingCoords[0]["coord_z"],
        pitch: 0,
        roll: 0,
        yaw: 148.0,
      };
      this.cloudRender.SuperAPI("updateAESObjectTransform", jo, (status) => {
        console.log("updateAESObjectTransform", status); //成功、失败回调
        digitalTwinApi.roamingMoveEffect(
          roamingCoords,
          {
            coord: roamingCoords[0]["coord"],
            coord_z: roamingCoords[0]["coord_z"],
            modelEid: "-9150751364756233164",
            pathId: "path_move_id",
            coord_z_type: 2,
          },
          "path_move_id",
          "-9150751364756233164"
        );
      });
    },
    //油车虚拟巡查--巡查暂停
    inspectionPause() {
      digitalTwinApi.moveState("patrol", "pause");
    },
    //油车虚拟巡查--巡查继续
    inspectionContinue() {
      digitalTwinApi.moveState("patrol", "continue");
    },
    //油车虚拟巡查--巡查停止
    inspectionStop() {
      digitalTwinApi.moveState("patrol", "stop");
      let jsondata = {
        id: "path_move_id", //覆盖物id
        covering_type: "path", //覆盖物类型, 详见下表
      };
      this.cloudRender.SuperAPI("RemoveCovering", jsondata, (status) => {
        console.log(status); //成功、失败回调
      });
    },
    //油车无人机巡查--初始化无人机视角
    initUAVView() {
      let jsondataCamera = {
        coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
        cad_mapkey: "", //CAD基准点Key值, 项目中约定
        coord_z: "17.126308", //海拔高度(单位:米)
        center_coord: "119.565318,31.248091", //中心点的坐标 lng,lat
        arm_distance: 10, //镜头距中心点距离(单位:米)
        pitch: 44.159996, //镜头俯仰角(5~89)
        yaw: 107.0, //镜头偏航角(0正北, 0~359)
        fly: false, //true: 飞行动画(有一个短暂飞行动画,并按照arm_distance,pitch,yaw设置镜头);
        //false: 立刻跳转过去(瞬移)
      };
      window.cloudRender.SuperAPI("SetCameraInfo", jsondataCamera).then((_back) => {
        console.log("场景镜头视界行为", _back);
      });
    },
    //油车无人机巡查--无人机巡查
    UAVInspection(res, platformId) {
      platformId = 3;
      let indexObj = uavInfoData.find((item) => item.platformId == platformId);
      console.log(indexObj, "indexObj");
      indexObj.path = [];
      // digitalTwinApi.ShowHideAESObject({
      //     eid: [indexObj.eid],
      //     bshow: true, //true:显示; false:隐藏
      //   });
      //通过接口获取数据
      axios.get("/json/UAVData.json").then((response) => {
        console.log(response, "response");
        // debugger

        res = response.data.args.coord_result;
        console.log(res, "res123");
        res.setTimeInLoop((msg, index) => {
          console.log(msg, index);
          // digitalTwinApi.ShowHideAESObject({
          //   eid: [indexObj.eid],
          //   bshow: true, //true:显示; false:隐藏
          // });
          // if (index > 20) {
          debugger;
          if (msg?.msgType == "1") {
            if (msg && Object.keys(msg).length) {
              // 无人机模型 飞行
              if (msg.longitudeWgs84 && msg.latitudeWgs84 && msg.altitude) {
                let altitude = msg.altitude;
                msg.coord = msg.longitudeWgs84 + "," + msg.latitudeWgs84;
                if (altitude > 0) {
                  msg.coord_z = Number(msg.altitude).toFixed(2);
                }
                this.uavFlyCopy(msg, msg.deviceId); // 无人机遥测信息 第几个无人机
              }
            }
          }
          // } else {
          //   digitalTwinApi.ShowHideAESObject({
          //     eid: [indexObj.eid],
          //     bshow: true, //true:显示; false:隐藏
          //   });
          //   window.cloudRender
          //     .SuperAPI("SetCameraInfo", {
          //       coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
          //       cad_mapkey: "", //CAD基准点Key值, 项目中约定
          //       coord_z: "177.539993", //海拔高度(单位:米)
          //       center_coord: "116.967059,36.493067", //中心点的坐标 lng,lat
          //       arm_distance: 117.472321, //镜头距中心点距离(单位:米)
          //       pitch: 41.964966, //镜头俯仰角(5~89)
          //       yaw: 16.0, //镜头偏航角(0正北, 0~359)
          //       fly: true, //true: 飞行动画(有一个短暂飞行动画,并按照arm_distance,pitch,yaw设置镜头);
          //       //false: 立刻跳转过去(瞬移)
          //     })
          //     .then((_back) => {
          //       console.log(_back);
          //     });
          // }
        }, 1000);
      });
    },
    //进行无人机巡查
    // 油车无人机巡查--无人机移动（无人机有速度）
    uavFlyCopy(data, platformId) {
      debugger;
      console.log(data, platformId, "wurenji");
      let index = uavInfoData.findIndex((item) => item.platformId == platformId);
      if (index == -1) return;
      let obj = uavInfoData[index];
      console.log(obj);
      let i = index + 1;
      if (obj) {
        let state = obj.state;
        if (state != null && (new Date().getTime() - state.time.getTime()) / 1000 < 5) {
          return;
        }
        if (obj.state == null) {
          obj.state = data;
          obj.state.time = new Date();
          // this.updateUAVLoactionCopy(platformId, undefined, 0.1, true);
        } else if (data.coord_z >= 0) {
          console.log("data.coord_z111", data.coord_z);
          data.time = new Date();
          if (data.coord == state.coord && data.coord_z == state.coord_z) {
            obj.state = data;
            return;
          }
          let path = obj.path.find((item) => {
            return item.id == "uavFlyPath" + i;
          });
          let sCoord = state.coord;
          let dCoord = data.coord;
          let h = state.relativeAlt - data.relativeAlt;
          let distance = this.getCameraAndPOIDistance(sCoord, dCoord, h);
          let c =
            Number(data.pitch) -
            Number(state.pitch) +
            (Number(data.yaw) - Number(state.yaw)) +
            (Number(data.roll) - Number(state.roll));
          debugger;
          let jo = {
            attach_id: String(obj.eid), //要移动的覆盖物id
            attach_type: "aes_object", //要移动的覆盖物类型 见下表
            be_attach_id: "uavFlyPath" + i, //依附的覆盖物id
            be_attach_type: "path", //依附的覆盖物类型 见下表
            speed: Math.ceil(
              Number(distance) / ((data.time.getTime() - state.time.getTime()) / 1000)
            ), // state.vel != null?Number(state.vel).toFixed(0):Math.ceil(Number(distance)/((data.time.getTime() - state.time.getTime())/1000)), //移动速度 (单位:米/秒)
            current_attitude: Number(data.coord_z) - Number(state.coord_z) !== 0, //元素姿态(true: 前一帧的姿态; 注: true优先, "pitch", "yaw", "roll" 无效)
            pitch: 0, //俯仰角, 参考值(-90~90)
            yaw: state.yaw, //偏航角, 参考值(0~360)
            roll: 0, //翻滚角, 参考值(0~360)
          };
          let cameraRoaming = {
            coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
            cad_mapkey: "", //CAD基准点Key值, 项目中约定
            coord_z_type: 2, //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
            subsidiary_show: false, //是否显示辅助线(true:显示; false:不显示)
            points: [
              {
                coord: state.coord, //路径坐标点 lng,lat
                coord_z: state.coord_z > 30 ? state.coord_z : 30, //高度(单位:米, cad坐标无效)
                coord_easetype: "linear", //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                arm_distance: obj.arm_distance, //镜头与坐标点距离(单位:米)
                pitch:
                  state.gimbalPitch - 1 > 0
                    ? state.gimbalPitch - 1
                    : state.gimbalPitch - 1 == 0
                    ? 1
                    : -state.gimbalPitch - 1, //镜头俯仰角(0~89)
                yaw: state.yaw >= 0 ? state.yaw : state.yaw + 360, //镜头偏航角(0正北, 0~359)
                attitude_easetype: "Linear", //镜头漫游至下一坐标点缓动姿态类型(见下表)
                time: (data.time.getTime() - state.time.getTime()) / 1000, //镜头漫游至下一坐标点所花费的时间(单位:秒)
                speed_easetype: "linear", //镜头漫游速度类型(见下表)
              },
              {
                coord: data.coord, //路径坐标点 lng,lat
                coord_z: data.coord_z > 30 ? data.coord_z : 30, //高度(单位:米, cad坐标无效)
                coord_easetype: "linear", //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
                arm_distance: obj.arm_distance, //镜头与坐标点距离(单位:米)
                pitch:
                  data.gimbalPitch - 1 > 0
                    ? data.gimbalPitch - 1
                    : data.gimbalPitch - 1 == 0
                    ? 1
                    : -data.gimbalPitch - 1, //镜头俯仰角(0~89)
                yaw: data.yaw >= 0 ? data.yaw : data.yaw + 360, //镜头偏航角(0正北, 0~359)
                attitude_easetype: "Linear", //镜头漫游至下一坐标点缓动姿态类型(见下表)
                // "time": 1,                             //镜头漫游至下一坐标点所花费的时间(单位:秒)
                speed_easetype: "linear", //镜头漫游速度类型(见下表)
              },
            ],
          };
          if (path != null) {
            path.points = [
              {
                coord: sCoord,
                coord_z: Number(state.coord_z), // coord_z: Number(state.relativeAlt),
              },
              {
                coord: dCoord,
                coord_z: Number(data.coord_z), // coord_z: Number(data.relativeAlt),
              },
            ];
            obj.state = data;
            // if (obj.cameraToUav && data.coord_z - obj.camera_z > 0) {
            digitalTwinApi.SetCameraRoamingPro(cameraRoaming);
            // window.cloudRender
            //   .SuperAPI("CameraTrace", {
            //     trace_object_type: "aes_object", //支持类型：aes_object  ,poi，场景特效，可视域，3D文字，灯光
            //     trace_object_id: obj.eid, //对象ID
            //     arm_distance: 100,
            //     fly: true,
            //   })
            //   .then((_back) => {
            //     console.log(_back);
            //   });
            // }
            window.cloudRender.SuperAPI("UpdatePathCoord", path, (status) => {
              console.log(status);
              digitalTwinApi.CoverToMove(jo);
            });
          } else {
            path = {
              id: "uavFlyPath" + i,
              advancedSetting: {
                smoothnessOfCorners: "extremelyHigh", //设置路径边角的平滑度(extremelyHigh:极高; high:高; middle:中; low:低;)
              },
              coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
              coord_z_type: 2, //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
              cad_mapkey: "", //CAD基准点Key值, 项目中约定
              type: "solid", //样式类型; 注①
              color: "", //颜色(HEX颜色值, 空值即透明; railway类型无效, 默认黑白色)
              pass_color: "", //覆盖物移动经过路径颜色(HEX颜色值)
              width: 1, //宽度(单位:米, 圆柱直径或方柱边长；当类型为“adaptive_solid”，含义为倍率)
              speedRate: 1,
              points: [
                {
                  coord: sCoord,
                  coord_z: Number(state.coord_z),
                },
                {
                  coord: dCoord,
                  coord_z: Number(data.coord_z),
                },
              ],
            };
            obj.state = data;
            obj.path.push(path);
            // if (obj.cameraToUav) {
            digitalTwinApi.SetCameraRoamingPro(cameraRoaming);
            // window.cloudRender
            //   .SuperAPI("CameraTrace", {
            //     trace_object_type: "aes_object", //支持类型：aes_object  ,poi，场景特效，可视域，3D文字，灯光
            //     trace_object_id: obj.eid, //对象ID
            //     arm_distance: 100,
            //     fly: true,
            //   })
            //   .then((_back) => {
            //     console.log(_back);
            //   });
            // }
            window.cloudRender.SuperAPI("AddPath", path, (status) => {
              console.log(status); //成功、失败回调
              digitalTwinApi.CoverToMove(jo);
            });
          }
        } else {
          console.log("data.coord_z222", data.coord_z);
          obj.state = null;
          this.updateUAVLoactionCopy(platformId, undefined, 0, false);
        }
      }
    },
    // 油车无人机巡查--
    updateUAVLoactionCopy(platformId, jsonData, scale, play) {
      let obj = uavInfoData.find((item) => {
        return item.platformId == platformId;
      });
      digitalTwinApi.ShowHideAESObject({
        eid: [obj.eid],
        bshow: true, //true:显示; false:隐藏
      });
      console.log(obj);
      if (obj != null) {
        let jo = {
          eid: obj.eid,
          coord_type: 0,
          cad_mapkey: "",
          scale: scale !== undefined ? scale : obj.scale,
          coord: jsonData !== undefined ? jsonData.coord : obj.coord,
          coord_z_type: 2,
          coord_z:
            jsonData !== undefined ? Number(jsonData.coord_z) : Number(obj.coord_z),
          pitch: jsonData !== undefined ? jsonData.pitch : Number(obj.pitch),
          roll: jsonData !== undefined ? jsonData.roll : Number(obj.roll),
          yaw: jsonData !== undefined ? jsonData.yaw : Number(obj.yaw),
        };
        window.cloudRender.SuperAPI("updateAESObjectTransform", jo, (status) => {
          console.log("updateAESObjectTransform", status); //成功、失败回调
          if (play != null && play !== undefined) {
            // 控制无人机模型螺旋桨动画播放
            this.setAESObjectAnimationPlay(obj.eid, play);
          }
        });
      }
    },
    // 油车无人机巡查--
    getCameraAndPOIDistance(point, cameraLocation, b) {
      let p = point.split(",");
      let c = cameraLocation.split(",");
      let a = this.GetDistance(Number(p[1]), Number(p[0]), Number(c[1]), Number(c[0]));
      return Math.sqrt(Number(a) * Number(a) + Number(b) * Number(b));
    },
    // 油车无人机巡查--
    GetDistance(lat1, lng1, lat2, lng2) {
      var radLat1 = this.Rad(lat1);
      var radLat2 = this.Rad(lat2);
      var a = radLat1 - radLat2;
      var b = this.Rad(lng1) - this.Rad(lng2);
      var s =
        2 *
        Math.asin(
          Math.sqrt(
            Math.pow(Math.sin(a / 2), 2) +
              Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)
          )
        );
      s = s * 6378.137; // 地球半径;
      s = Math.round(s * 10000) / 10; //输出为米
      return s;
    },
    // 油车无人机巡查--
    Rad(d) {
      return (d * Math.PI) / 180.0;
    },
    //油车无人机巡查--设置aes对象动画片段播放
    setAESObjectAnimationPlay(eid, play) {
      if (play) {
        let jo = {
          eid: String(eid),
          clip_name: "AESFBX_Anim", //片段名称
          start_and_end: "1,", //动画起止帧（按动画默认总帧数计算）
          //"12,"从第12帧播放到最后一帧；"12,12"停止到第12帧；","停止到最后一帧；"1,"从第一帧播放到最后一帧
          play_rate: 2, //播放倍速
          loop: true, //是否循环
          reverse: false, //是否倒放
        };
        window.cloudRender.SuperAPI("SetAESObjectAnimationPlay", jo, (status) => {
          console.log(status); //成功、失败回调
        });
      } else {
        let jsondata = {
          eid: String(eid),
          state: "stop",
          //pause:暂停播放; continue:从暂停处继续播放，播放设置沿用暂停前的设置;
          //stop，中止播放，模型重置回初始状态，continue此时无效
        };
        window.cloudRender.SuperAPI(
          "SetAesObjectAnimationPlayState",
          jsondata,
          (status) => {
            console.log(status); //成功、失败回调
          }
        );
      }
    },
    // 工具集--镜头漫游
    startCameraRoam() {
      if (this.cameraRoamPoints.length > 0) {
        let jo = {
          subsidiary_show: true,
          points: [],
        };
        this.cameraRoamPoints.forEach((item) => {
          let p = {
            coord: item.coord,
            coord_z: item.coord_z,
            pitch: item.pitch,
            yaw: item.yaw,
            time: item.time,
            arm_distance: item.arm_distance,
          };
          jo.points.push(p);
        });
        digitalTwinApi.SetCameraRoamingPro();
      } else {
        this.$modal.msgWarning("请设置镜头漫游点位");
      }
    },
    // 工具集--初始化镜头漫游
    initCameraRoam() {
      let jsondata = {
        coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
        cad_mapkey: "", //CAD基准点Key值, 项目中约定
        coord_z_type: 0, //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
        subsidiary_show: true, //是否显示辅助线(true:显示; false:不显示)
        points: [
          {
            coord: "116.961861,36.487324", //路径坐标点 lng,lat
            coord_z: 0, //高度(单位:米, cad坐标无效)
            coord_easetype: "linear", //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
            arm_distance: 50, //镜头与坐标点距离(单位:米)
            pitch: 30, //镜头俯仰角(0~89)
            yaw: 65, //镜头偏航角(0正北, 0~359)
            attitude_easetype: "Linear", //镜头漫游至下一坐标点缓动姿态类型(见下表)
            time: 40, //镜头漫游至下一坐标点所花费的时间(单位:秒)
            speed_easetype: "linear", //镜头漫游速度类型(见下表)
          },
          {
            coord: "116.965309,36.491207", //路径坐标点 lng,lat
            coord_z: 0, //高度(单位:米, cad坐标无效)
            // coord_easetype: "linear", //镜头漫游至下一坐标点缓动类型(linear:线型, curve:曲线型)
            arm_distance: 10, //镜头与坐标点距离(单位:米)
            pitch: 30, //镜头俯仰角(0~89)
            yaw: 65, //镜头偏航角(0正北, 0~359)
            // attitude_easetype: "Linear", //镜头漫游至下一坐标点缓动姿态类型(见下表)
            // time: 60, //镜头漫游至下一坐标点所花费的时间(单位:秒)
            // speed_easetype: "linear", //镜头漫游速度类型(见下表)
          },
        ],
      };
      this.cloudRender.SuperAPI("SetCameraRoamingPro", jsondata).then((_back) => {
        console.log("初始化镜头漫游" + _back);
      });
    },
    // 工具集--修改镜头漫游状态
    setCameraRoamState(state) {
      digitalTwinApi.SetCameraRoamingProState(state);
    },
    // 工具集--显示隐藏POI点
    showHidePOI(id, type, show) {
      let jo = {
        id: id,
        covering_type: type,
        bshow: show,
      };
      digitalTwinApi.ShowHideCovering(jo);
    },
    // 图层控制树--显示隐藏poi点
    showHideAllPoiAndChangeTree(show) {
      let keyset = new Set();
      let ids = [];
      this.poiList.forEach((item) => {
        keyset.add(item.point_type);
        ids.push(item.id);
      });
      if (show) {
        let jsondata = {
          id: ids, //覆盖物id
          covering_type: "poi", //覆盖物类型, 详见下表
          bshow: true, //true:显示; false:隐藏
        };
        this.cloudRender.SuperAPI("ShowHideCovering", jsondata, (status) => {
          console.log(status); //成功、失败回调
        });
        if (this.unInitPoiList.length > 0) {
          let itemIds = [];
          let items = [];
          this.unInitPoiList.forEach((item) => {
            itemIds.push(item.id);
            items.push(item);
            this.poiList.push(item);
            keyset.add(item.point_type);
          });
          if (itemIds.length > 0) {
            this.unInitPoiList = [];
            digitalTwinApi.AddCustomPOI(items);
          }
        }
        this.$nextTick(() => {
          this.$refs.layerSelectRef.setCheckedKeys(Array.from(keyset));
        });
      } else {
        let jsondata = {
          id: ids, //覆盖物id
          covering_type: "poi", //覆盖物类型, 详见下表
          bshow: false, //true:显示; false:隐藏
        };
        this.cloudRender.SuperAPI("ShowHideCovering", jsondata, (status) => {
          console.log(status); //成功、失败回调
        });
        this.$nextTick(() => {
          this.$refs.layerSelectRef.setCheckedKeys([]);
        });
      }
    },
    // 工具集--场景响应鼠标、键盘操作 start
    inputActionStartFunction(args) {
      let action = args.action;
      switch (action) {
        case "key_move":
          console.log("按键移动Start", args);
          break;
        case "mouse_wheel":
          console.log("鼠标滚轮Start", args);
          break;
        case "mouse_swipe":
          console.log("鼠标左键拖拽Start", args);
          break;
        case "mouse_doubleclick":
          window.console.log("鼠标左键双击Start", args);
          break;
        case "mouse_rotation":
          console.log("鼠标中键/右键旋转Start", args);
          break;
        case "camera_lag":
          console.log("鼠标拖拽, 旋转, 滚轮操作场景结束后, 镜头运动停止Start", args);
          break;
      }
    },
    // 工具集--场景响应鼠标、键盘操作 end
    inputActionEndFunction(args) {
      let action = args.action;
      switch (action) {
        case "key_move":
          console.log("按键移动End", args);
          break;
        case "mouse_wheel":
          console.log("鼠标滚轮End", args);
          break;
        case "mouse_swipe":
          console.log("鼠标左键拖拽End", args);
          break;
        case "mouse_doubleclick":
          console.log("鼠标左键双击End", args);
          break;
        case "mouse_rotation":
          console.log("鼠标中键/右键旋转End", aaxiorgs);
          break;
        case "camera_lag":
          console.log("鼠标拖拽, 旋转, 滚轮操作场景结束后, 镜头运动停止End", args);
          this.getCameraParams();
          break;
      }
    },
    //工具集--关闭渲染
    stopRenderCloud() {
      this.cloudRender.SuperAPI("StopRenderCloud"); //关闭云渲染, 释放资源 (此处是关键。单页应用释放资源请注意)
    },
    //工具集--获取天气信息
    getWeatherInfo(code) {
      axios({
        url:
          "https://api.qweather.com/v7/weather/now?key=55d19ed093804712b20d7faeb4d803f5&location=" +
          code,
        method: "get",
        responseType: "json",
      }).then((res) => {
        console.log("天气", res);
        if (res.status == 200) {
          let weatherText = res.data.now.text;
          let e = "Sunny";
          let e1 = this.weatherMap[weatherText];
          if (e1) {
            e = e1;
          }
          this.cloudRender.SuperAPI("SetEnvWeather", { env_weather: e }, (state) => {
            console.log("SetEnvWeather 获取到和风天气", weatherText, e1, e, state);
          });
        } else {
          this.cloudRender.SuperAPI(
            "SetEnvWeather",
            { env_weather: "Sunny" },
            (state) => {
              console.log("SetEnvWeather 未获取到和风天气", state);
            }
          );
        }
      });
    },
    //工具集--获取AES实体EID
    getAESObjects() {
      let that = this;
      let jsondata = {
        state: true, //true:开启获取EID; false:关闭获取EID
        highlight: true, //true:点击实体高亮; false:点击实体不高亮
        //"act_layers":[] //可被选中元素所在图层，删除该字段，全部元素可被选中
      };
      that.cloudRender.SuperAPI("StartGetEID", jsondata).then((_back) => {
        console.log("AES_EID:" + _back);
      });
    },
    //工具集--获取AES实体属性
    getCoveringInfo(arr) {
      digitalTwinApi.getCoveringInfo();
      // let that = this;
      // let jsondata = ['-9214927692375098736']
      // that.cloudRender.SuperAPI('GetAESObjectDataWithEids', jsondata).then((_back) => {
      // console.log(_back)
      // })
    },
    // 工具集--测量工具
    measureTool() {
      let that = this;
      let jsondata = {
        coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
        cad_mapkey: "", //CAD基准点Key值, 项目中约定
      };
      that.cloudRender.SuperAPI("StartMeasureTool", jsondata, (e) => {
        console.log(e);
      });
    },
    // 工具集--测量角度工具
    measureAngle() {
      let jsondata = {
        coord_type: 0, //坐标类型(0:经纬度, 1:cad)
        cad_mapkey: "default", //CAD基准点Key值, 项目中约定
        coord_z_type: 0, //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
        projection: true, //是否显示投影面以及角度测量结果(true, false)
        accuracy: 2, //精度,最大精确4位(0~4)
        angle: true, //是否显示测量面以及角度测量结果(true, false)
        adsorption_mode: false, //自动吸附(true/false; 注: "adsorption_mode": true 只对激活后的BIM模型有效)
      };
      digitalTwinApi.measureAngle(jsondata);
    },
    // 工具集--测量长度工具
    measureLength() {
      let jsondata = {
        coord_type: 0, //坐标类型(0:经纬度, 1:cad)
        cad_mapkey: "default", //CAD基准点Key值, 项目中约定
        coord_z_type: 0, //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
        projection: true, //是否开启投影线(true:开启; false:不开启)
        projection_value: true, //是否开启投影线数值(true:开启; false:不开启)
        unit: "m", //单位(km:千米; m:米; cm:厘米; mm:毫米)
        thickness: 2, //线宽(单位:像素)
        accuracy: 2, //精度,最大精确4位(0~4)
        adsorption_mode: false, //自动吸附(true/false; 注: "adsorption_mode": true 只对激活后的BIM模型有效)
      };
      digitalTwinApi.measureLength(jsondata);
    },
    // 工具集--测量面积工具
    measureArea() {
      let jsondata = {
        coord_type: 0, //坐标类型(0:经纬度, 1:cad)
        cad_mapkey: "default", //CAD基准点Key值, 项目中约定
        coord_z_type: 0, //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
        projection: true, //是否开启投影(true:开启; false:不开启)
        accuracy: 2, //精度,最大精确4位(0~4)
        surface: true, //是否显示三角剖分面以及面积测量结果(true, false)
        length: true, //是否显示边线以及周长测量结果(true, false)
        unit: "m2", //单位(km2:平方千米; m2:平方米; cm2:平方厘米; mm2:平方毫米)
        adsorption_mode: false, //自动吸附(true/false; 注: "adsorption_mode": true 只对激活后的BIM模型有效)
      };
      digitalTwinApi.measureArea(jsondata);
    },
    // 工具集--开启测量工具
    openMeasureTool() {
      let jsondata = {
        coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
        cad_mapkey: "", //CAD基准点Key值, 项目中约定
      };
      that.cloudRender.SuperAPI("StartMeasureTool", jsondata, (e) => {
        console.log(e);
      });
    },
    //水面上升调取的方法--待处理
    waterGoup() {
      // digitalTwinApi.addWaterPath("water1","119.552248,31.228746","26.229774","40");
      digitalTwinApi.addWaterPath(35, 10);
      // setTimeout(() => {
      //   digitalTwinApi.waterMoveTo();
      // }, 100);
    },
    //水面下降--待处理
    WaterDrow() {
      // digitalTwinApi.addWaterPath("water2","119.552248,31.228746","40","26.229774");
      digitalTwinApi.addWaterPath(28, 10);
      // setTimeout(() => {
      //   digitalTwinApi.waterMoveTo();
      // }, 100);
    },
    // 工具集--geojson区域轮廓
    geoRange(val) {
      let jsondata = {
        id: "geo_range_id" + val,
        color: "blue", // "ff00004c",              //轮廓颜色(HEXA颜色值)
        range_height: 10, //围栏高度(单位:米)
        id_field_name: "FID_fx_res", //指定geojson中的id字段
        //支持json或文件形式、二选一
        geojson:
          val == 1
            ? "https://www.zhywater.com:16700/prod-api/profile/mapData/json/fxsy-ManagePolygon-gcj02.geojson"
            : "https://www.zhywater.com:16700/prod-api/profile/mapData/json/fxsy-ManagePolygon.geojson",
      };
      digitalTwinApi.AddGeoRange(jsondata);
    },
    //工具集--模型剖切
    modalClipe() {
      let jsondata = {
        type: "cube", //剖切矩体类型(cube:六面体剖切)
        coord_type: 0, //(0:经纬度坐标, 1:cad坐标)
        cad_mapkey: "", //CAD基准点Key值, 项目中约定
        coord: "113.237768,23.128523",
        coord_z: 300, //高度(单位:米, cad坐标无效)
        coord_z_type: 2, //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
        cube: {
          size_ns: "50", //南北宽度(单位:米)
          size_we: "50", //东西宽度(单位:米)
          size_ud: "1", //高度(单位:米)
        },
        pitch: 0, //俯仰角
        yaw: 0, //偏航角(0正北)
        roll: 0, //翻滚角
        invert: true, //内外剖切;true:剖切内部; false:剖切外部
        color: "00ffff", //被切物体描边颜色(HEX颜色值)
        weight: 1, //被切物体描边宽度(0~1)
        filter: [
          //参与剖切的层实体, 注:内容为空, 则所有元素参与剖切; 通过"获取图层名称API -- GetAESLayersName"获取
          "terrain",
          "building",
          "road",
          "tree",
          "water",
          "district",
          "building_instance",
          "road_instance",
          "district_instance",
          "detector",
          "default",
        ],
      };
      this.cloudRender.SuperAPI("AddSection", jsondata).then((_back) => {
        console.log(_back);
      });
    },
    //工具集--按钮禁用事件
    buttongDisabled(id) {
      const button = document.querySelector(`"#${id}"`);
      button.addEventListener("click", (e) => {
        console.log(e);
        console.log(e.target);
      });
    },
    //工具集--场景镜头移动状态(停止移动, 释放焦点, 删除路径)
    cameraMoveState() {
      digitalTwinApi.cameraMoveState();
    },
    //加载数字流场
    addDigitalFlood() {
      let jsondata = {
        id: "digitalFloodPathsId", //路径id
        advancedSetting: {
          smoothnessOfCorners: "extremelyHigh", //设置路径边角的平滑度(extremelyHigh:极高; high:高; middle:中; low:低;)
        },
        coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
        cad_mapkey: "default", //CAD基准点Key值, 项目中约定
        coord_z: 30, //高度(单位:米, cad坐标无效)
        coord_z_type: 0, //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
        type: "scan_line_solid", //路径样式类型(fit_solid(贴合地面), adaptive_solid(等宽路径), none, solid, arrow, arrow_dot, dashed_dot, arrow_dashed, flash, scan_line, brimless_arrow, railway, round_pipe, square_pipe, dashed_line)
        color: "00BFFF", //路径颜色(HEX颜色值, 空值即透明)
        pass_color: "", //覆盖物移动经过路径颜色(HEX颜色值)
        width: 30, //路径宽度(单位:米; 当类型为"adaptive_solid", 含义为倍率)
        speedRate: 0.5, //用于水流倍率(取值范围[0,10])
        id_field_name: "id", //指定geojson中的id字段
        geojson: szlcData,
      };
      digitalTwinApi.addDigitalFlood(jsondata);
    },
    //移除流场
    removeDigitalFlood() {
      for (let i = 0; i < 323; i++) {
        let jsondata = {
          id: "digitalFloodPathsId_" + i, //路径id
          covering_type: "path",
        };
        digitalTwinApi.removeDigitalFlood(jsondata);
      }
    },
    //工具集--添加路径热力图
    addRoadHeat() {
      let jsondata = {
        id: "roadheatmap_id",
        coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
        coord_z_type: 0, //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
        cad_mapkey: "", //CAD基准点Key值, 项目中约定
        width: 50, //宽度(单位:米)
        roadheatmap_define: [
          //定义颜色组
          {
            level: 1, //颜色级别名称
            color: "ff0000", //颜色(HEX颜色值)
          },
          {
            level: 2,
            color: "009900",
          },
          {
            level: 3,
            color: "0066cc",
          },
        ],
        points: [
          {
            coord: "119.566322,31.255095", //数据点坐标 lng,lat
            coord_z: 17, //高度(单位:米)
            level: 1, //采用哪一级颜色(与roadheatmap_define中定义的level对应)
          },
          {
            coord: "119.567032,31.261841",
            coord_z: 6.5,
            level: 2,
          },
          {
            coord: "119.566544,31.272932",
            coord_z: 4.5,
            level: 3,
          },
          {
            coord: "119.565849,31.277826",
            coord_z: 3.5,
            level: 3,
          },
        ],
      };
      window.cloudRender.SuperAPI("AddRoadHeatMap", jsondata, (status) => {
        console.log(status); //成功、失败回调
        console.log(5555555);
      });
    },
    //工具集--更新路径热力图
    updateRoadHeat() {
      let jsondata = {
        id: "roadheatmap_id",
        coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
        coord_z_type: 0, //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
        cad_mapkey: "", //CAD基准点Key值, 项目中约定
        is_append: true, //true: 追加路径热力图数据(注意顺序); false: 重建路径热力图数据
        points: [
          {
            coord: "119.566322,31.255095", //数据点坐标 lng,lat
            coord_z: 17, //高度(单位:米)
            level: 3, //采用哪一级颜色(与roadheatmap_define中定义的level对应)
          },
          {
            coord: "119.567032,31.261841",
            coord_z: 6.5,
            level: 1,
          },
          {
            coord: "119.566544,31.272932",
            coord_z: 4.5,
            level: 2,
          },
          {
            coord: "119.565849,31.277826",
            coord_z: 3.5,
            level: 2,
          },
        ],
      };
      window.cloudRender.SuperAPI("UpdateRoadHeatMapCoord", jsondata, (status) => {
        console.log(status); //成功、失败回调
        console.log(66666);
      });
    },
    // 淹没演进工具集  1.配置文件下载
    waterFloodFileLoadAndChangeGrid(name, option) {
      let that = this;
      let jsondata = {
        GridID: name, //网格id，自主命名
        TifURL: option.floodBasicData.tifURL + ".tif", //本地或在线地址
        ShpURL: option.floodBasicData.shpURL + ".shp", //本地或在线地址
        ShxURL: option.floodBasicData.shxURL + ".shx", //本地或在线地址
        PrjURL: option.floodBasicData.prjURL + ".prj", //本地或在线地址
        DbfURL: option.floodBasicData.dbfURL + ".dbf", //本地或在线地址
        isLocalPath: option.isLocalfloodBasicData, //是否本地路径，true为本地，false为非本地，上述地址两种方式只取一种
      };
      console.log("文件下载", jsondata);
      this.cloudRender.SuperAPI("WimWFSFileDownLoad", jsondata, (_back) => {
        // let data = JSON.parse(_back);
        console.log(_back);
      });
    },
    // 淹没演进工具集  2.淹没格网转置
    waterFloodChangeGrid(data, name) {
      let jsondata = {
        GridID: name, //网格id，自主命名，需与下载id一致
      };
      this.cloudRender.SuperAPI("WimWFSSetData", jsondata, (e) => {
        // console.log("淹没格网转置:", e); //成功、失败回调
        let WimWFSSetData = JSON.parse(e);
        this.waterFloodDataUpload();
      });
    },
    changeLoading(){
      this.homeLoading=false;
    },
    // 淹没演进工具集 3.网格数据加载 走接口
    async waterFloodDataUpload() {
      await getSmtRasData({pid:this.smtPid}).then((res) => {
        if (res.code == 200 && res.data.length>0) {
           for(let index = 0; index < res.data.length; index++) {
             this.cloudRender.SuperAPI("WimWFSDataLoad", res.data[index], (status) => {console.log(status.slice(20,30))
              if(status.indexOf('"TimeIndex":"133"')>-1){
                this.homeLoading=false;
              }
             });
          }

          //循环预加载需要预加载的数据
          // for (let jj = 0; jj < this.floodParams.length; jj++) {
          //   if (this.floodParams[jj].isPreLoad) {
          //     for (let kk = 0; kk < this.floodParams[jj].preLoadItems.length; kk++) {
          //       if (!this.floodParams[jj].preLoadItems[kk].loaded) {
          //         this.waterFloodFileLoadAndChangeGrid(
          //           this.floodParams[jj].preLoadItems[kk].floodId,
          //           this.floodParams[jj].preLoadItems[kk]
          //         );
          //         this.floodParams[jj].preLoadItems[kk].loaded = true;
          //         break;
          //       }
          //     }
          //   }
          // }
        }else{
          this.$modal.msgWarning("本次调度没有下游淹没数据!")
          this.homeLoading=false;
        }
        let jsondata1 = {
          GridID: "flood",

          DataMax: "8.0",

          DataMin: "0.0",

          MatBlur: "0.004",

          MatOpacity: "0.8", //热力透明度范围为0-1，0为全透明，1为全不透明
        };

        this.cloudRender.SuperAPI("WimWFSSetFlood", jsondata1, (_back) => {
          console.log(_back);
        });

        let jsondata2 = {
          GridID: "flood",

          addPositionX: "0",

          addPositionY: "0",

          addScaleX: "0",

          addScaleY: "0",

          addRotation: "0",

          addHeight: "100",
        };

        this.cloudRender.SuperAPI("WimWFSSetPosition", jsondata2, (_back) => {
          console.log(_back);
        });
      });
    },
    // 淹没演进工具集 4.淹没效果设置
    waterFloodEffectSet(option, name) {
      let jsondata = {
        GridID: name, //网格id，自主命名，需与下载id一致
      };
      if (option.DataMax) {
        jsondata.DataMax = option.DataMax;
      }
      if (option.DataMin) {
        jsondata.DataMin = option.DataMin;
      }
      if (option.MatBlur) {
        jsondata.MatBlur = option.MatBlur;
      }
      if (option.MatOpacity) {
        jsondata.MatOpacity = option.MatOpacity;
      }
      this.cloudRender.SuperAPI("WimWFSSetFlood", jsondata, (e) => {
        console.log("淹没效果设置:", e); //成功、失败回调
      });
    },
    // 淹没演进工具集 5.网格偏移设置
    waterFloodGridChangeSet(name) {
      let jsondata = {
        GridID: name, //网格id，自主命名，需与下载id一致
        addPositionX: "10", //X轴方向的偏移量，cm
        addPositionY: "10", //Y轴方向的偏移量，cm
        addScaleX: "1", //X轴方向的缩放倍率
        addScaleY: "1", //Y轴方向的缩放倍率
        addRotation: "10", //旋转角度数值，角度
        addHeight: "10", //高度的偏移量，cm
      };
      this.cloudRender.SuperAPI("WimWFSSetPosition", jsondata).then((e) => {
        console.log("网格偏移设置:", e); //成功、失败回调
      });
    },
    // 淹没演进工具集 一帧一帧跑
    waterFloodPlayOne(control, data, name) {
      console.log("跑数据的名称", name, "时序数", data);
      let jsondata4 = {
        GridID: name, //网格id，自主命名，需与下载id一致
        waterMatIndex: "0", //水体材质样式，8种，编号0-7
        HeatMapMatIndex: "0", //热力图样式， 0正常，1等值线
        PlaySpeed: "2", //播放速度设定,1为8s一帧，2为4s一帧
        PlayIndex: data, //从指定index开始播放
        Play: "false", //播放true or 暂停false，暂停为暂停在index帧
        HeatMap: control, //是否是热力图，true为是，false为否
      };
      this.cloudRender.SuperAPI("WimWFSPlayFlood", jsondata4, (_back) => {
      });
    },
    // 淹没演进工具集 6.淹没过程顺序播放
    waterFloodPlay(name) {
      let jsondata = {
        GridID: name, //网格id，自主命名，需与下载id一致
        waterMatIndex: "0", //水体材质样式，8种，编号0-7
        HeatMapMatIndex: "0", //热力图样式， 0正常，1等值线
        PlaySpeed: "2", //播放速度设定,1为8s一帧，2为4s一帧
        PlayIndex: "1", //从指定index开始播放
        Play: "true", //播放true or 暂停false，暂停为暂停在index帧
        HeatMap: "true", //是否是热力图，true为是，false为否
      };
      this.cloudRender.SuperAPI("WimWFSPlayFlood", jsondata, (_back) => {
        console.log("播放", _back);
      });
      let jsondata1 = {
        GridID: name,
        DataMax: "4.0",
        DataMin: "0.0",
        MatBlur: "0.008",
        MatOpacity: "0.7", //热力透明度范围为0-1，0为全透明，1为全不透明
      };

      this.cloudRender.SuperAPI("WimWFSSetFlood", jsondata1, (_back) => {
        console.log(_back);
      });

      let jsondata2 = {
        GridID: name,
        addPositionX: "0",
        addPositionY: "0",
        addScaleX: "0",
        addScaleY: "0",
        addRotation: "0",
        addHeight: "20",
      };
      this.cloudRender.SuperAPI("WimWFSSetPosition", jsondata2, (_back) => {
        console.log(_back);
      });
    },
    // 淹没演进工具集 7.指定帧率播放
    waterFloodChoosePlayFrame(PlayIndex, name) {
      let jsondata = {
        GridID: name, //网格id，自主命名，需与下载id一致
        PlayIndex: PlayIndex, //从指定index开始播放
      };
      this.cloudRender.SuperAPI("WimWFSPlayAtIndex", jsondata).then((e) => {
        console.log("指定帧率播放:", e); //成功、失败回调
      });
    },
    // 淹没演进工具集 8.淹没效果清除
    waterFloodClear(name) {
      let jsondata = {
        GridID: name, //清除效果的网格id
      };
      this.cloudRender.SuperAPI("WimWFSClearFloodEffect", jsondata).then((e) => {
        console.log("淹没效果清除:", e); //成功、失败回调
        this.waterFloodDelateGrid(name);
      });
    },
    // 淹没演进工具集 9.删除转置网格
    waterFloodDelateGrid(name) {
      let jsondata = {
        GridID: name, //删除网格的id
      };
      this.cloudRender.SuperAPI("WimWFSRemoveGrid", jsondata).then((e) => {
        console.log("删除转置网格:", e); //成功、失败回调
      });
    },
    //工具集--迁移箭头
    addMigrationArrows(date1, date2) {
      let jsondata = {
        id: "strategymap_id",
        coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
        cad_mapkey: "", //CAD基准点Key值, 项目中约定
        coord_z_type: 0, //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
        type: 1, //样式类型(1:箭头型)
        is_gather: true, //true: 战略图向外扩展, false: 战略图向内聚集
        animation_type: 1, //动画类型,1:全部出现, 2:逐个出现
        start_coord: "119.565689,31.252628", //起点坐标 lng,lat
        start_coord_z: 5, //起点高度(单位:米)
        target_data: [
          {
            target_coord: date1, //目标点坐标 lng,lat (顺序影响逐个动画播放出现的顺序)
            target_coord_z: 50, //目标点高度(单位:米)
            color: "ff0000", //颜色(HEX颜色值)
          },
          {
            target_coord: date2,
            target_coord_z: 50,
            color: "00ff00",
          },
        ],
      };
      cloudRender.SuperAPI("AddStrategyMap", jsondata, (status) => {
        console.log(status); //成功、失败回调
      });
    },
    //工具集--修改迁徙箭头
    updateMigrationArrows() {
      let jsondata = {
        id: "strategymap_id",
        coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
        cad_mapkey: "", //CAD基准点Key值, 项目中约定
        coord_z_type: 0, //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
        start_coord: "119.565689,31.252628", //起点坐标 lng,lat
        start_coord_z: 5, //起点高度(单位:米)
        target_data: [
          {
            target_coord: "119.546471,31.252821", //目标点坐标
            target_coord_z: 35, //目标点高度(单位:米)
          },
          {
            target_coord: "119.547915,31.263529",
            target_coord_z: 35,
          },
        ],
      };
      cloudRender.SuperAPI("UpdateStrategyMapCoord", jsondata, (status) => {
        console.log(status); //成功、失败回调
      });
    },
    //工具集--删除迁徙图
    deleteMigrationArrows() {
      let jsondata = {
        id: "strategymap_id", //覆盖物id
        covering_type: "strategy_map", //覆盖物类型, 详见下表
      };
      cloudRender.SuperAPI("RemoveCovering", jsondata, (status) => {
        console.log(status); //成功、失败回调
      });
    },
    //视角跳转--待合并
    perspectiveJump(data) {
      let jsondata = {
        coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
        cad_mapkey: "", //CAD基准点Key值, 项目中约定
        coord_z: "28.021563", //海拔高度(单位:米)
        center_coord: data, //中心点的坐标 lng,lat
        arm_distance: 477.125, //镜头距中心点距离(单位:米)
        pitch: 47.723511, //镜头俯仰角(5~89)
        yaw: 356, //镜头偏航角(0正北, 0~359)
        fly: true, //true: 飞行动画(有一个短暂飞行动画,并按照arm_distance,pitch,yaw设置镜头);
        //false: 立刻跳转过去(瞬移)
      };
      this.setDefaultView(jsondata);
    },
    //洪水演进视角跳转--待合并
    HSYJPerspectiveJump() {
      let jsondata = {
        coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
        cad_mapkey: "", //CAD基准点Key值, 项目中约定
        coord_z: "28.021563", //海拔高度(单位:米)
        center_coord: "119.565771,31.247482", //中心点的坐标 lng,lat
        arm_distance: 520.569275, //镜头距中心点距离(单位:米)
        pitch: 28.387573, //镜头俯仰角(5~89)
        yaw: 350.0, //镜头偏航角(0正北, 0~359)
        fly: false, //true: 飞行动画(有一个短暂飞行动画,并按照arm_distance,pitch,yaw设置镜头);
        //false: 立刻跳转过去(瞬移)
      };
      this.setDefaultView(jsondata);
    },
    //工具集--水面变化
    waterChange(height, time) {
      height = height - 2.35 - 0.5;
      digitalTwinApi.addWaterPath(height, time);
    },
    //淹没演进工具集 添加淹没村落点
    addVillage(list) {
      //不移除已有的淹没村庄点再添加
      this.addVillagePoi(list);
      // //先移除已有的淹没村庄点再添加
      // let items = this.poiList.filter((item) => {
      //     return item.point_type == "flood_village_point";
      //   });
      //   if (items && items.length > 0) {
      //     let ids = [];
      //     items.forEach((item) => {
      //       ids.push(item.id);
      //     });
      //     let jsondata = {
      //       id: ids, //覆盖物id
      //       covering_type: "poi", //覆盖物类型, 详见下表
      //     };
      //     this.cloudRender.SuperAPI("RemoveCovering", jsondata, (status) => {
      //       console.log(status); //成功、失败回调
      //       this.poiList = this.poiList.filter(
      //         (item) => item.point_type != "flood_village_point"
      //       );
      //       this.addVillagePoi(list);
      //     });
      //   }else{
      //     //第一次添加相关类型点直接加
      //     this.addVillagePoi(list);
      //   }
    },
    //淹没演进工具集 添加淹没村落点poi方法
    addVillagePoi(list) {
      //增加新给的data点========================
      let customPoiList = [];
      list.forEach((item) => {
        let alreadyHave = this.poiList.filter(
          (poiitem) => poiitem.id == "flood_village_point" + item.id
        );
        if (alreadyHave && alreadyHave.length > 0) {
        } else {
          // var item = list[num];
          // let labelList = item.label;
          // console.log(labelList, "labelList");
          var dataUrl =
            "http://www.zhywater.com:18190/static/sylogo//yx_ymd.png";
          // if (num == 10 || num == 11 || num == 21 || num == 22 || num == 23 || num == 24 || num == 25 || num == 26 || num == 27 || num == 28 || num == 29) {
          //   dataUrl = "http://www.zhywater.com:18190/static/sylogo//yx_ymd1.png"
          // } else {
          //   dataUrl =
          //     "http://www.zhywater.com:18190/static/sylogo//yx_ymd.png";
          // }
          let content = [
            {
              text: [item.villageName, "#fff", "12"],
              text_offset: "10,5",
              text_centered: false,
              text_boxwidth: item.villageName * 16,
              scroll_speed: 1,
            },
          ];
          // if (labelList && labelList.length > 0) {
          //   for (let i = 0; i < 1; i++) {
          //     let label = labelList;
          //     // console.log(label.length, "label.length");
          //     content.push({
          //       text: [label, "#fff", "12"],
          //       text_offset: "10," + (5 + i * 20),
          //       text_centered: false,
          //       text_boxwidth: label.length * 16,
          //       scroll_speed: 1,
          //     });
          //   }
          // }
          let point = {
            id: "flood_village_point" + item.id,
            //label: item.label,
            coord: item.longitude + "," + item.latitude,
            coord_z: 0,
            coord_z_type: 0,
            point_type: "flood_village_point",
            state: "monitors_state1",
            always_show_label: false,
            show_label_range: "0,20",
            params: {
              villageId: item.id,
            },
            marker: {
              size: "30,30",
              images: [
                {
                  define_state: "monitors_state1",
                  normal_url: dataUrl, //'http://superapi.51hitech.com/doc-static/images/static/markerNormal.png',
                  activate_url: dataUrl, // 'http://superapi.51hitech.com/doc-static/images/static/markerActive.png'
                },
              ],
            },
            label: {
              bg_size: "100," + content.length * 25,
              bg_offset: "22,35", //整个label左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
              content: content,
            },
          };
          customPoiList.push(point);
          this.poiList.push(point);
        }
      });
      if (customPoiList.length > 0) {
        //console.log(" 添加淹没村庄信息", customPoiList);
        digitalTwinApi.AddCustomPOI(customPoiList);
      }
    },
    //淹没演进工具集 移除所有淹没村点
    removeAllVillage() {
      //先移除已有的淹没村庄点
      let items = this.poiList.filter((item) => {
        return item.point_type == "flood_village_point";
      });
      if (items && items.length > 0) {
        let ids = [];
        items.forEach((item) => {
          ids.push(item.id);
        });
        let jsondata = {
          id: ids, //覆盖物id
          covering_type: "poi", //覆盖物类型, 详见下表
        };
        this.cloudRender.SuperAPI("RemoveCovering", jsondata, (status) => {
          console.log(status); //成功、失败回调
          this.poiList = this.poiList.filter(
            (item) => item.point_type != "flood_village_point"
          );
        });
      }
    },
    //淹没演进工具集 添加淹没村落点label
    addVillageLabel(data) {
      //先移除已有的淹没村庄点label再添加
      let items = this.poiList.filter((item) => {
        return item.point_type == "flood_village_label";
      });
      if (items && items.length > 0) {
        let ids = [];
        items.forEach((item) => {
          ids.push(item.id);
        });
        let jsondata = {
          id: ids, //覆盖物id
          covering_type: "poi", //覆盖物类型, 详见下表
        };
        this.cloudRender.SuperAPI("RemoveCovering", jsondata, (status) => {
          console.log(status); //成功、失败回调
          this.poiList = this.poiList.filter(
            (item) => item.point_type != "flood_village_label"
          );
          this.addVillageLabelPoi(data);
        });
      } else {
        //第一次添加相关类型点直接加
        this.addVillageLabelPoi(data);
      }
    },
    //淹没演进工具集 添加淹没村落点label的方法
    addVillageLabelPoi(data) {
      let photoType =
        "http://www.zhywater.com:18190/static/sylogo/yx_red.png";
      let jsondata = {
        point_type: "flood_village_label",
        params: {
          villageId: data.id,
        },
        id: "flood_village_label" + data.id,
        coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
        cad_mapkey: "",
        adaptive: true, //true:自适应大小;false:默认
        coord: data.longitude + "," + data.latitude,
        coord_z: 0,
        coord_z_type: "0", //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
        always_show_label: true, //是否永远显示label, true:显示label(默认), false:不显示label
        show_label_range: "0,2000", //POI点显示label的范围(单位:米, 范围最小、最大距离; 在此范围内显示, 超出范围隐藏; 注意:always_show_label属性优先于此属性)
        sort_order: true,
        animation_type: "wipe",
        duration_time: 0.1,
        state: "bubble_state_1",
        marker: {
          size: "52,75", //marker大小(宽,高 单位:像素)
          images: [
            {
              define_state: "bubble_state_1",
              normal_url:
                "http://www.zhywater.com:18190/static/sylogo/yx_jt1.png",
              activate_url:
                "http://www.zhywater.com:18190/static/sylogo/yx_jt1.png",
            },
          ],
        },
        label: {
          bg_image_url: photoType,
          bg_size: "210,115", //label大小(宽, 高 单位:像素)
          bg_offset: "-60,150", //整个label左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
          content: [
            {
              text: ["村名:" + data.village, "ffffff", "10"], //[文本内容, HEXA颜色, 文本大小]
              text_offset: "10,11", //文本框左上角相对于label中bg_offset左上角的margin偏移(x,y 单位:像素), 注: x为正向右, y为正向下
              text_boxwidth: 200, //文本框宽度
              text_centered: true, //文本居中(true:居中; false:不居中)
              scroll_speed: 0, //文本滚动速度(0:不滚动; 注: "text_centered":true优先)
            },
            {
              text: ["联系人:" + data.contactPerson, "ffffff", "10"], //[文本内容, HEXA颜色, 文本大小]
              text_offset: "10,30", //文本框左上角相对于label中bg_offset左上角的margin偏移(x,y 单位:像素), 注: x为正向右, y为正向下
              text_boxwidth: 200, //文本框宽度
              text_centered: true, //文本居中(true:居中; false:不居中)
              scroll_speed: 0, //文本滚动速度(0:不滚动; 注: "text_centered":true优先)
            },
            {
              text: ["联系电话:" + data.contactPhoneNumber, "ffffff", "10"], //[文本内容, HEXA颜色, 文本大小]
              text_offset: "10,50", //文本框左上角相对于label中bg_offset左上角的margin偏移(x,y 单位:像素), 注: x为正向右, y为正向下
              text_boxwidth: 200, //文本框宽度
              text_centered: true, //文本居中(true:居中; false:不居中)
              scroll_speed: 0, //文本滚动速度(0:不滚动; 注: "text_centered":true优先)
            },
            {
              text: ["转移人数:" + data.people, "ffffff", "10"], //[文本内容, HEXA颜色, 文本大小]
              text_offset: "10,70", //文本框左上角相对于label中bg_offset左上角的margin偏移(x,y 单位:像素), 注: x为正向右, y为正向下
              text_boxwidth: 200, //文本框宽度
              text_centered: true, //文本居中(true:居中; false:不居中)
              scroll_speed: 0, //文本滚动速度(0:不滚动; 注: "text_centered":true优先)
            },
            {
              text: ["转移地点:" + data.placementName, "ffffff", "10"], //[文本内容, HEXA颜色, 文本大小]
              text_offset: "10,90", //文本框左上角相对于label中bg_offset左上角的margin偏移(x,y 单位:像素), 注: x为正向右, y为正向下
              text_boxwidth: 200, //文本框宽度
              text_centered: true, //文本居中(true:居中; false:不居中)
              scroll_speed: 0, //文本滚动速度(0:不滚动; 注: "text_centered":true优先)
            },
          ],
        },
        // "window":{
        //     "url":"http://superapi.51hitech.com/doc-static/images/static/video.html",
        //     "size":"260,175",      //window大小(宽,高 单位:像素)
        //     "offset":"25,90"      //window左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
        // }
      };
      this.poiList.push(jsondata);
      this.cloudRender.SuperAPI("AddCustomPOI", jsondata, (status) => {
        console.log("AddCustomPOI", status); //成功、失败回调
      });
    },
    //淹没演进工具集 移除所有淹没村点label
    removeAllVillageLabel() {
      //先移除已有的淹没村庄点
      let items = this.poiList.filter((item) => {
        return item.point_type == "flood_village_label";
      });
      if (items && items.length > 0) {
        let ids = [];
        items.forEach((item) => {
          ids.push(item.id);
        });
        let jsondata = {
          id: ids, //覆盖物id
          covering_type: "poi", //覆盖物类型, 详见下表
        };
        this.cloudRender.SuperAPI("RemoveCovering", jsondata, (status) => {
          console.log(status); //成功、失败回调
          this.poiList = this.poiList.filter(
            (item) => item.point_type != "flood_village_label"
          );
        });
      }
    },
    //淹没演进工具集 添加淹没村落迁移轨迹
    addMovePath(id, list) {
      //先移除已有的淹没村庄点label再添加
      let items = this.poiList.filter((item) => {
        return item.point_type == "flood_village_path";
      });
      if (items && items.length > 0) {
        let ids = [];
        items.forEach((item) => {
          ids.push(item.id);
        });
        let jsondata = {
          id: ids, //覆盖物id
          covering_type: "path", //覆盖物类型, 详见下表
        };
        this.cloudRender.SuperAPI("RemoveCovering", jsondata, (status) => {
          console.log(status); //成功、失败回调
          this.poiList = this.poiList.filter(
            (item) => item.point_type != "flood_village_path"
          );
          this.addMovePathPoi(id, list);
        });
      } else {
        //第一次添加相关类型点直接加
        this.addMovePathPoi(id, list);
      }
    },
    //淹没演进工具集 添加淹没村落迁移轨迹方法
    addMovePathPoi(id, list) {
      let jsondata = {
        point_type: "flood_village_path",
        id: "flood_village_path" + id,
        advancedSetting: {
          smoothnessOfCorners: "extremelyHigh", //设置路径边角的平滑度(extremelyHigh:极高; high:高; middle:中; low:低;)
        },
        coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
        coord_z_type: 0, //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
        cad_mapkey: "", //CAD基准点Key值, 项目中约定
        type: "arrow", //样式类型; 注①
        color: "ff0000", //颜色(HEX颜色值, 空值即透明; railway类型无效, 默认黑白色)
        pass_color: "ffff00", //覆盖物移动经过路径颜色(HEX颜色值)
        width: 20, //宽度(单位:米, 圆柱直径或方柱边长；当类型为“adaptive_solid”，含义为倍率)
        speedRate: 1, //流动特效的移动倍率,仅针对部分类型有效（arrow,arrow_dot,arrow_dashed,brimless_arrow,scan_line,scan_line_solid）
        points: list,
      };
      this.poiList.push(jsondata);
      this.cloudRender.SuperAPI("AddPath", jsondata, (status) => {
        console.log(status); //成功、失败回调
      });
    },
    //淹没演进工具集 移除淹没村落迁移轨迹
    deleteAllMovePath() {
      let items = this.poiList.filter((item) => {
        return item.point_type == "flood_village_path";
      });
      if (items && items.length > 0) {
        let ids = [];
        items.forEach((item) => {
          ids.push(item.id);
        });
        let jsondata = {
          id: ids, //覆盖物id
          covering_type: "path", //覆盖物类型, 详见下表
        };
        this.cloudRender.SuperAPI("RemoveCovering", jsondata, (status) => {
          console.log(status); //成功、失败回调
          this.poiList = this.poiList.filter(
            (item) => item.point_type != "flood_village_path"
          );
        });
      }
    },
    //淹没演进工具集 添加淹没村落的转移目的地点
    addVillageEnd(data) {
      //先移除已有的淹没村庄点label再添加
      let items = this.poiList.filter((item) => {
        return item.point_type == "flood_village_point_end";
      });
      if (items && items.length > 0) {
        let ids = [];
        items.forEach((item) => {
          ids.push(item.id);
        });
        let jsondata = {
          id: ids, //覆盖物id
          covering_type: "poi", //覆盖物类型, 详见下表
        };
        this.cloudRender.SuperAPI("RemoveCovering", jsondata, (status) => {
          console.log(status); //成功、失败回调
          this.poiList = this.poiList.filter(
            (item) => item.point_type != "flood_village_point_end"
          );
          this.addVillageEndPoi(data);
        });
      } else {
        //第一次添加相关类型点直接加
        this.addVillageEndPoi(data);
      }
    },
    //淹没演进工具集 添加淹没村落的转移目的地点方法
    addVillageEndPoi(list) {
      //增加新给的data点========================
      let customPoiList = [];
      list.forEach((item) => {
        let alreadyHave = this.poiList.filter(
          (poiitem) => poiitem.id == "flood_village_point_end" + item.id
        );
        if (alreadyHave && alreadyHave.length > 0) {
        } else {
          // var item = list[num];
          // let labelList = item.label;
          // console.log(labelList, "labelList");
          var dataUrl =
            "http://www.zhywater.com:18190/static/sylogo//yx_ymd1.png";
          // if (num == 10 || num == 11 || num == 21 || num == 22 || num == 23 || num == 24 || num == 25 || num == 26 || num == 27 || num == 28 || num == 29) {
          //   dataUrl = "http://www.zhywater.com:18190/static/sylogo//yx_ymd1.png"
          // } else {
          //   dataUrl =
          //     "http://www.zhywater.com:18190/static/sylogo//yx_ymd.png";
          // }
          let content = [];
          // if (labelList && labelList.length > 0) {
          //   for (let i = 0; i < 1; i++) {
          //     let label = labelList;
          //     // console.log(label.length, "label.length");
          //     content.push({
          //       text: [label, "#fff", "12"],
          //       text_offset: "10," + (5 + i * 20),
          //       text_centered: false,
          //       text_boxwidth: label.length * 16,
          //       scroll_speed: 1,
          //     });
          //   }
          // }
          let point = {
            id: "flood_village_point_end" + item.id,
            //label: item.label,
            coord: item.longitude + "," + item.latitude,
            coord_z: 0,
            coord_z_type: 0,
            point_type: "flood_village_point_end",
            state: "monitors_state1",
            always_show_label: false,
            show_label_range: "0,20",
            params: {
              villageId: item.id,
            },
            marker: {
              size: "30,30",
              images: [
                {
                  define_state: "monitors_state1",
                  normal_url: dataUrl, //'http://superapi.51hitech.com/doc-static/images/static/markerNormal.png',
                  activate_url: dataUrl, // 'http://superapi.51hitech.com/doc-static/images/static/markerActive.png'
                },
              ],
            },
            label: {
              bg_size: "100," + content.length * 25,
              bg_offset: "22,35", //整个label左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
              content: content,
            },
          };
          customPoiList.push(point);
          this.poiList.push(point);
        }
      });
      if (customPoiList.length > 0) {
        //console.log(" 添加淹没村庄信息", customPoiList);
        digitalTwinApi.AddCustomPOI(customPoiList);
      }
    },
    //淹没演进工具集 移除淹没村落的转移目的地点
    removeAllEndVillage() {
      //先移除已有的淹没村庄点
      let items = this.poiList.filter((item) => {
        return item.point_type == "flood_village_point_end";
      });
      if (items && items.length > 0) {
        let ids = [];
        items.forEach((item) => {
          ids.push(item.id);
        });
        let jsondata = {
          id: ids, //覆盖物id
          covering_type: "poi", //覆盖物类型, 详见下表
        };
        this.cloudRender.SuperAPI("RemoveCovering", jsondata, (status) => {
          console.log(status); //成功、失败回调
          this.poiList = this.poiList.filter(
            (item) => item.point_type != "flood_village_point_end"
          );
        });
      }
    },
    //闸门水花工具集--水流开关
    waterFlowSwitch(gear) {
      let gearOne = [1, 2, 3];
      switch (gear) {
        case 1:
          gearOne = [1, 2, 3];
          break;
        case 2:
          gearOne = [7, 8, 9];
          break;
        case 3:
          gearOne = [4, 5, 6];
          break;
      }
      let jsondata = {
        ID: gearOne,
        open: [true, true, true],
      };
      this.cloudRender.SuperAPI("OpenWaterEffect", jsondata);
      this.closeOtherWaterFlowSwitch(gear);
    },
    //闸门水花工具集--关闭其他水花
    closeOtherWaterFlowSwitch(opennum) {
      let idArray = [
        [1, 2, 3],
        [7, 8, 9],
        [4, 5, 6],
      ];
      for (let i = 0; i < idArray.length; i++) {
        if (i + 1 != opennum) {
          let jsondata = {
            ID: idArray[i],
            open: [false, false, false],
          };
          this.cloudRender.SuperAPI("OpenWaterEffect", jsondata);
        }
      }
    },
    //闸门水花工具集--关闭水流开关
    closeWaterFlowSwitch() {
      let jsondata = {
        ID: [1, 2, 3],
        open: [false, false, false],
      };
      this.cloudRender.SuperAPI("OpenWaterEffect", jsondata);
      let jsondata2 = {
        ID: [7, 8, 9],
        open: [false, false, false],
      };
      this.cloudRender.SuperAPI("OpenWaterEffect", jsondata2);
      let jsondata3 = {
        ID: [4, 5, 6],
        open: [false, false, false],
      };
      this.cloudRender.SuperAPI("OpenWaterEffect", jsondata3);
    },
    //闸门水花工具集--闸门开关
    gateSwitch(gateArray, angleArray) {
      // var array = [1, 2, 3];
      if (gateArray == undefined) {
        gateArray = [1, 2, 3];
      }
      if (angleArray == undefined) {
        angleArray = [-60, -60, -60];
      }
      for (let i = 0; i < gateArray.length; i++) {
        // var element = array[i]
        let jsondata = {
          id: gateArray[i],
          angle: angleArray[i],
        };
        this.cloudRender.SuperAPI("GateControl", jsondata);
      }
    },
    //闸门水花工具集--闸门开启/关闭并放水/停水
    gateSwitchAndWaterFlowSwitch(boolOpen,flowArray) {
      if (boolOpen) {
        this.gateSwitchAndWaterFlowSwitchFunction(
          undefined,
          undefined,
          flowArray,
          undefined,
          false
        );
      } else {
        this.gateSwitchAndWaterFlowSwitchFunction(
          undefined,
          [0, 0, 0],
          undefined,
          [false, false, false],
          false
        );
      }
    },
    //闸门水花工具集--闸门开闭及水花效果开关
    gateSwitchAndWaterFlowSwitchFunction(gateArray,angleArray,waterFlowIdArray,waterFlowOpenArray,boolLocation){
      if (boolLocation) {
        this.multiplePerspectives(4);
      }
      // var array = [1, 2, 3];
      if (gateArray == undefined) {
        gateArray = [1, 2, 3];
      }
      if (angleArray == undefined) {
        angleArray = [-60, -60, -60];
      }
      if (waterFlowIdArray == undefined) {
        //水花的大小
        // waterFlowIdArray=[3, 2, 1];//一档
        // waterFlowIdArray=[9, 7, 8];//二档
        waterFlowIdArray = [4, 5, 6]; //三档
      }
      if (waterFlowOpenArray == undefined) {
        waterFlowOpenArray = [true, true, true];
      }
      for (let i = 0; i < gateArray.length; i++) {
        // var element = array[i]
        let jsondata = {
          id: gateArray[i],
          angle: angleArray[i],
        };
        this.cloudRender.SuperAPI("GateControl", jsondata, (status) => {
          console.log("闸门开度和水花效果",status); //成功、失败回调
          //开始走水花
          let jsondata = {
            ID: waterFlowIdArray,
            open: waterFlowOpenArray,
          };
          this.cloudRender.SuperAPI("OpenWaterEffect", jsondata);
        });
      }
    },
    //添加汛限水位线
    addWaterPolyline(num) {
      axios
        .get("/json/XXSWXData.json")
        .then((response) => {
          // var  num  = 1
          console.log();
          let list = response.data.args.coord_result[num][1];
          let id = response.data.args.coord_result[num][0];
          console.log(list, id, "list");
          let jsondata = {
            id: id,
            advancedSetting: {
              smoothnessOfCorners: "extremelyHigh", //设置路径边角的平滑度(extremelyHigh:极高; high:高; middle:中; low:低;)
            },
            coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
            coord_z_type: 2, //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
            cad_mapkey: "", //CAD基准点Key值, 项目中约定
            type: "solid", //样式类型; 注①
            color: "ff0000", //颜色(HEX颜色值, 空值即透明; railway类型无效, 默认黑白色)
            pass_color: "ffff00", //覆盖物移动经过路径颜色(HEX颜色值)
            width: 1, //宽度(单位:米, 圆柱直径或方柱边长；当类型为“adaptive_solid”，含义为倍率)
            speedRate: 1, //流动特效的移动倍率,仅针对部分类型有效（arrow,arrow_dot,arrow_dashed,brimless_arrow,scan_line,scan_line_solid）
            points: list,
          };
          this.cloudRender.SuperAPI("AddPath", jsondata, (status) => {
            console.log(status); //成功、失败回调
          });
        })
        .catch((error) => {
          // 处理错误
          console.error("Error loading JSON file:", error);
        });
    },
    //添加3D文字
    add3DText() {
      let jo = [
        {
          id: "hengshan",
          coord: "119.56422324675916,31.240451269918452",
          text: "横山水库",
        },
        {
          id: "longzhu",
          coord: "119.6005159986635,31.178125330523827",
          text: "龙珠水库",
        },
        {
          id: "youche",
          coord: "119.75450365176148,31.218554479172017",
          text: "油车水库",
        },
        { id: "yixing", coord: "119.8163136331,31.3773341034", text: "宜兴市城区" },
        { id: "taihu", coord: "119.90665787184074,31.23792066479434", text: "太湖" },
      ];
      jo.forEach((jsonData) => {
        digitalTwinApi.add3DText(jsonData);
      });
    },
    //各类视角--待合并
    multiplePerspectives(num) {
      var data = [
        ["140.520004", "113.483675,33.261687", 2777.770508, 43.973724, 192.0], //上游
        ["140.520004", "113.531327,33.265677", 5159.20166, 62.845093, 181.0], //库区
        ["140.520004", "113.554403,33.281537", 74.071907, 17.140747, 36.0], //闸前
        ["140.520004", "113.555746,33.281885", 155.875046, 8.0, 310.0], //闸侧
        ["140.520004", "113.556152,33.283192", 155.875046, 17.435608, 229.0], //闸后
        ["110.118866", "113.552904,33.283178", 9.117349, 8.0, 344.0], //水尺
        ["140.520004", "113.590997,33.340724", 9999.99707, 43.788971, 30.0], //行洪视角
      ];
      let jsondata = {
        coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
        cad_mapkey: "", //CAD基准点Key值, 项目中约定
        coord_z: data[num][0], //海拔高度(单位:米)
        center_coord: data[num][1], //中心点的坐标 lng,lat
        arm_distance: data[num][2], //镜头距中心点距离(单位:米)
        pitch: data[num][3], //镜头俯仰角(5~89)
        yaw: data[num][4], //镜头偏航角(0正北, 0~359)
        fly: false, //true: 飞行动画(有一个短暂飞行动画,并按照arm_distance,pitch,yaw设置镜头);
        //false: 立刻跳转过去(瞬移)
      };
      this.setDefaultView(jsondata);
    },
    //工具集--删除所有poi点
    deleteAllPoint() {
      let jsondata = {
        covering_type: "poi", //覆盖物类型, 详见下表
      };
      this.cloudRender.SuperAPI("RemoveAllCovering", jsondata, (status) => {
        console.log(status); //成功、失败回调
      });
    },
    //沙沟闸门启闭水花效果
    openGateAndWater(gateid,open){
      if(gateid !== undefined){
        let jsondata= {
        "gateid":["1","2","3","4","5","6","7","8","9","10","11","12","13"],
        "open":open
        }
        this.cloudRender.SuperAPI('WaterGate',jsondata,(status) => {
          console.log(status); //成功、失败回调
        });
      }else{
        let jsondata= {
        "gateid":["1","2","3","4","5","6","7","8","9","10","11","12","13"],
        "open":open
        }
        this.cloudRender.SuperAPI('WaterGate',jsondata,(status) => {
          console.log(status); //成功、失败回调
        });
      }
    },
    //沙沟下游行洪断面撒点
    smtGetDownStation() {
      getDownStation().then(res=>{
        console.log("smtGetDownStationsmtGetDownStationsmtGetDownStationsmtGetDownStation",res);
        res.data.forEach((element)=>{
            var waterLevel = '实时水位：' + element.rz + ' m'
            var time = '更新时间：' + element.tm
            var coord = element.lon.toString()+','+element.lat.toString()
          let jsondata = {
            "id":"downStation"+element.id,
            "coord_type":0,                         //坐标类型(0:经纬度坐标, 1:cad坐标)
            "cad_mapkey":"",                        //CAD基准点的Key值, 项目中约定
            "adaptive":true,                       //true:自适应大小;false:默认
            "coord":coord,         //POI点的坐标 lng,lat
            "coord_z":20,                            //高度(单位:米)
            "coord_z_type":2,                       //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
            "always_show_label":true,               //是否永远显示label, true:显示label(默认), false:不显示label
            "show_label_range":"0,2000",            //POI点显示label的范围(单位:米, 范围最小、最大距离; 在此范围内显示, 超出范围隐藏; 注意:always_show_label属性优先于此属性)
            "sort_order":true,                     //是否开启遮挡排序(根据POI点真实3D位置开启遮挡排序,
                                                    //注: 只与其他开启排序的POI之间进行排序, 开启此排序会消耗性能(最多60个POI点同时开启排序)
            "animation_type":"bounce",              //动画类型(bounce:弹出式; stretch:伸缩式; wipe:展开式)
            "duration_time":0.7,                    //规定完成动画所花费的时间(单位:秒)
            "state":"state_1",                      //与marker之中images中的define_state对应
            "marker":{
                "size":"30,30",                   //marker大小(宽,高 单位:像素)
                "images":[
                    {
                        "define_state":"state_1",   //marker图片组
                        "normal_url":"https://www.zhywater.com:18190/static/smtlogo/xyxh-dm.png",        //normal 图片url地址
                        "activate_url":"https://www.zhywater.com:18190/static/ningjinlogo/xyxh-dm.png"       //hover, active 图片url地址
                                        //本地图片地址一: "file:///D:/xxx/markerNormal.png",    D: 在线席位所在盘符
                                        //本地图片地址二: "path:/UserData/markerNormal.png",    图片资源由在线席位后台管理, 在线席位4.3.1以上版本
                    }
                ]
            },
            // "label":{
            //     "bg_image_url":"https://www.zhywater.com:18190/static/ningjinlogo/poiBackground.png",
            //                     //本地图片地址一: "file:///D:/xxx/LabelBg.png",    D: 在线席位所在盘符
            //                     //本地图片地址二: "path:/UserData/LabelBg.png",    图片资源由在线席位后台管理, 在线席位4.3.1以上版本
            //     "bg_size":"230,130", //label大小(宽, 高 单位:像素)
            //     "bg_offset":"-100,260", //整个label左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
            //     "content": [

            //     ]
            // }
          }
          cloudRender.SuperAPI("AddCustomPOI", jsondata, (status) => {
              console.log(status); //成功、失败回调
          })
        })


      })
    },
    //显隐断面
    showOrhiddenDowStation(controlData) {
      for(var i=0;i<=180;i++){
        let jsondata = {
            id: "downStation"+i, //覆盖物id
            covering_type: "poi", //覆盖物类型, 详见下表
            bshow: controlData, //true:显示; false:隐藏
          };
        cloudRender.SuperAPI("ShowHideCovering", jsondata, (status) => {
          console.log(status); //成功、失败回调
        });
      }
    }
  },
  mounted() {
    this.timer = setInterval(this.changePercentage, 500);
    // Bus.$on("titleBrother", this.handleValue);
  },
  destroyed() {
    clearInterval(this.timer);
    this.cloudRender.SuperAPI("StopRenderCloud"); //关闭云渲染, 释放资源 (此处是关键。单页应用释放资源请注意)
  },
  beforeDestroy() {
    // Bus.$off("titleBrother", this.handleValue);
  },
};
function getAngle(lng_a, lat_a, lng_b, lat_b) {
  var a = ((90 - lat_b) * Math.PI) / 180;
  var b = ((90 - lat_a) * Math.PI) / 180;
  var AOC_BOC = ((lng_b - lng_a) * Math.PI) / 180;
  var cosc = Math.cos(a) * Math.cos(b) + Math.sin(a) * Math.sin(b) * Math.cos(AOC_BOC);
  var sinc = Math.sqrt(1 - cosc * cosc);
  var sinA = (Math.sin(a) * Math.sin(AOC_BOC)) / sinc;
  var A = (Math.asin(sinA) * 180) / Math.PI;
  var res = 0;
  if (lng_b > lng_a && lat_b > lat_a) res = A;
  else if (lng_b > lng_a && lat_b < lat_a) res = 180 - A;
  else if (lng_b < lng_a && lat_b < lat_a) res = 180 - A;
  else if (lng_b < lng_a && lat_b > lat_a) res = 360 + A;
  else if (lng_b > lng_a && lat_b == lat_a) res = 90;
  else if (lng_b < lng_a && lat_b == lat_a) res = 270;
  else if (lng_b == lng_a && lat_b > lat_a) res = 0;
  else if (lng_b == lng_a && lat_b < lat_a) res = 180;
  return res;
}
</script>

<style lang="less" scoped>
@text-color: #00ffff;
@time: 2.5s;

.ue4map {
  width: 100%;
  height: 100%;
  position: absolute;

  // transform-origin: 0 0;
  .bj {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background-image: url("~@/assets/images/digitalTwin/beijing.png") !important;
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
  }

  #player {
    width: 100% !important;
    height: 100% !important;
    bottom: 0 !important;
    left: 0 !important;

    video {
      height: 100%;
      width: 100%;
    }

    .close {
      position: absolute;
      right: 4vh;
      top: 7vh;
      z-index: 12;
      color: #fff;
      cursor: pointer;
      background-color: rgba(0, 0, 0, 0.5) !important;
      border-color: #aad4c1 !important;
    }

    /deep/ .el-button + .el-button {
      margin: 0;
    }

    .close:hover {
      opacity: 0.7;
    }

    > img {
      position: absolute;
      top: 6vw;
      right: 1vw;
      z-index: 11;
      width: 20.6vw;
    }

    //.byq{
    //    width: 25.6vw;
    //    //height: 36.4vh;
    //}
    //.zhdq{
    //    width: 25.6vw;
    //    //height: 36.4vh;
    //}
    .left {
      left: 1vw;
      top: 6vw;
      position: absolute;
      width: 20.6vw;
      height: 80vh;
      display: flex;
      flex-direction: column;

      img {
        width: 100%;
        margin-bottom: 2vh;
      }
    }

    .right {
      right: 1vw;
      top: 6vw;
      position: absolute;
      width: 20.6vw;
      height: 80vh;
      display: flex;
      flex-direction: column;

      img {
        width: 100%;
        margin-bottom: 2vh;
      }
    }
  }

  .shade {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background: #000000;
    //z-index: 1;
  }

  .move {
    animation: mymove 5s linear infinite;
    -moz-animation: mymove 5s linear infinite;
    -o-animation: mymove 5s linear infinite;
    -webkit-animation: mymove 5s linear infinite;
  }

  @keyframes mymove {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  .loadingText {
    font-size: x-large;
    margin: 10% 0 10% 30%;
    font-weight: normal;
    font-stretch: normal;
    color: @text-color;
  }
}

::v-deep .el-progress__text {
  color: @text-color;
}

.loading {
  width: 260px;
  height: 11px;
  margin: 0 auto;
  text-align: center;
  background-color: #000000;
  border: solid 1px #46fcd5;
  transform: skew(315deg);
  display: flex;

  span {
    display: inline-block;
    width: 10px;
    height: 100%;
    margin-right: 5px;
    opacity: 0;
    background: #00ffff;
    animation: load 7s ease infinite;

    &:last-child {
      margin-right: 0;
    }

    &:nth-child(1) {
      -webkit-animation-delay: @time;
    }

    &:nth-child(2) {
      -webkit-animation-delay: @time * 2;
    }

    &:nth-child(3) {
      -webkit-animation-delay: @time * 3;
    }

    &:nth-child(4) {
      -webkit-animation-delay: @time * 4;
    }

    &:nth-child(5) {
      -webkit-animation-delay: @time * 5;
    }

    &:nth-child(6) {
      -webkit-animation-delay: @time * 6;
    }

    &:nth-child(7) {
      -webkit-animation-delay: @time * 7;
    }

    &:nth-child(8) {
      -webkit-animation-delay: @time * 8;
    }

    &:nth-child(9) {
      -webkit-animation-delay: @time * 9;
    }

    &:nth-child(10) {
      -webkit-animation-delay: @time * 10;
    }

    &:nth-child(11) {
      -webkit-animation-delay: @time * 11;
    }

    &:nth-child(12) {
      -webkit-animation-delay: @time * 12;
    }

    &:nth-child(13) {
      -webkit-animation-delay: @time * 13;
    }

    &:nth-child(14) {
      -webkit-animation-delay: @time * 14;
    }

    &:nth-child(15) {
      -webkit-animation-delay: @time * 15;
    }
  }
}

@-webkit-keyframes load {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 1;
  }
}

::v-deep .el-collapse-item__header {
  background: #4d6565;
  opacity: 0.8;
  color: aliceblue;
  padding-left: 5px;
}

::v-deep .el-collapse-item__wrap {
  background: #4d6565;
  opacity: 0.8;
}

::v-deep .el-collapse-item__content {
  padding: 5px 5px 5px 0;
  color: #fff;
}

::v-deep .el-button--medium:first-child {
  margin-left: 10px;
}

#selectStyle {
  width: 150px !important;
}

/deep/.el-input--small {
  width: 150px !important;
}

/deep/.el-switch__label * {
  font-size: 16px;
}

/deep/.el-switch {
  height: 35px;
}

.block {
  display: flex !important;
  justify-content: space-between;
  margin: 0;
  padding: 0;
  align-items: center;
  // width: 210px;
}

/deep/.popper__arrow {
  left: 30%;
}

/deep/.el-input__inner {
  height: 42px;
  color: #00ffff;
  font-size: 18px;
}
.ue4map .el-loading-spinner .el-loading-text {
  color: #409eff;
  margin: 3px 0;
  font-size: 20px;
}

.ue4map .el-loading-spinner i {
  font-size: 20px;
}

.funcBtnBox {
  width: 300px;
  max-height: 800px;
  overflow-x: auto;
  overflow-y: auto;
  position: fixed;
  top: 85px;
  right: 35px;
  background: #4d6565;
  border-radius: 5px;
  opacity: 0.8;
  padding: 5px;
  z-index: 10;
}

.dt-title {
  background: url(~@/assets/images/titleBG.png) no-repeat;
  background-size: 100% 100%;
  width: 175px;
  height: 30px;
  z-index: 3;
  position: absolute;
}

.dt-title > div {
  font-size: 20px;
  font-weight: 600;
  background-image: -webkit-linear-gradient(
    top,
    var(--gradientFrColor),
    var(--gradientToColor)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
</style>
