<template>
  <div style="width: 100%">
    <el-select
      style="width: 100%"
      v-model="selectValue"
      v-bind="$attrs"
      :placeholder="placeholder"
      @change="changeValue"
    >
      <el-option
        v-for="dict in options"
        :key="dict.value"
        :value="dict.value"
        :label="dict.label"
      >
        <span style="float: left">{{ dict.label }}</span>
        <span class="control-btn">
          <i class="el-icon-edit" @click.stop="handleUpdate(dict)" v-has-permi="['system:dict:edit']"></i>
          <i class="el-icon-delete" @click.stop="handleDel(dict)" v-has-permi="['system:dict:remove']"></i>
        </span>
      </el-option>
      <div class="select-foot" v-show="dictAdd"><el-button type="text" @click.stop="handleAdd" icon="el-icon-circle-plus-outline" v-has-permi="['system:dict:add']">新增</el-button></div>
    </el-select>
    <el-dialog :close-on-click-modal="false" v-if="open" :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="字典类型">
          <el-input v-model="form.dictType" :disabled="true"/>
        </el-form-item>
        <el-form-item label="数据标签" prop="dictLabel">
          <el-input v-model.trim="form.dictLabel" placeholder="请输入数据标签"/>
        </el-form-item>
        <el-form-item label="数据键值" prop="dictValue">
          <el-input v-model.trim="form.dictValue" placeholder="请输入数据键值"/>
        </el-form-item>
        <el-form-item label="样式属性" prop="cssClass">
          <el-input v-model.trim="form.cssClass" placeholder="请输入样式属性"/>
        </el-form-item>
        <el-form-item label="显示排序" prop="dictSort">
          <el-input-number v-model="form.dictSort" controls-position="right" :min="0"/>
        </el-form-item>
        <el-form-item label="回显样式" prop="listClass">
          <el-select v-model="form.listClass">
            <el-option
              v-for="item in listClassOptions"
              :key="item.value"
              :label="item.label + '(' + item.value + ')'"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model.trim="form.remark" type="textarea" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>

</template>

<script>
import {addData, delData, getData, listData, updateData} from "@/api/system/dict/data";

export default {
  name: "dictSelect",
  dicts: ['sys_normal_disable'],
  props: {
    dictData: {
      type: Array,
      default: () => []
    },
    dictText: {
      type: String,
      default: '',
    },
    value: {
      type: [String, Number, Boolean],
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    dictAdd: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      options: [],
      selectValue: null,
      initialValue: this.value,
      title: "新增字典值",
      open: false,
      form: {},
      // 数据标签回显样式
      listClassOptions: [
        {
          value: "default",
          label: "默认"
        },
        {
          value: "primary",
          label: "主要"
        },
        {
          value: "success",
          label: "成功"
        },
        {
          value: "info",
          label: "信息"
        },
        {
          value: "warning",
          label: "警告"
        },
        {
          value: "danger",
          label: "危险"
        }
      ],
      // 表单校验
      rules: {
        dictLabel: [
          {required: true, message: "数据标签不能为空", trigger: "blur"}
        ],
        dictValue: [
          {required: true, message: "数据键值不能为空", trigger: "blur"}
        ],
        dictSort: [
          {required: true, message: "数据顺序不能为空", trigger: "blur"}
        ]
      },
      // 字典类型
      dictType: ''
    }
  },
  mounted() {
    if(this.dictText) {
      this.dictType = this.dictText
      this.getList()
    }
  },
  watch:{
    value: {
      handler(val) {
        this.selectValue = val
      },
      immediate: true
    },
    dictData: {
      handler(val) {
        if(val.length > 0 && !this.dictText) {
          this.options = val
          this.dictType = val[0].raw.dictType
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    async getList() {
      let response = await this.getDicts(this.dictType)
      this.options = response.data.map((item) =>{
        return {
          ...item,
          value: item.dictValue,
          label: item.dictLabel
        }
      })
      this.selectValue = this.initialValue
    },
    changeValue(val) {
      this.$emit('input', val)
      this.$emit('change', val)
    },
    // 表单重置
    reset() {
      this.form = {
        dictCode: undefined,
        dictLabel: undefined,
        dictValue: undefined,
        cssClass: undefined,
        listClass: 'default',
        dictSort: 0,
        status: "0",
        remark: undefined
      };
      this.resetForm("form");
    },
    handleAdd() {
      this.reset()
      this.title = '新增字典值'
      this.open = true
      this.form.dictType = this.dictType;
    },
    handleUpdate(dict) {
      let dictCode = null
      if(dict.raw) {
        dictCode = dict.raw.dictCode
      } else {
        dictCode = dict.dictCode
      }
      this.reset();
      getData(dictCode).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改字典数据";
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.dictCode != undefined) {
            updateData(this.form).then(response => {
              this.$store.dispatch('dict/removeDict', this.dictType);
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addData(this.form).then(response => {
              this.$store.dispatch('dict/removeDict', this.dictType);
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    handleDel(dict) {
      let dictCode = null
      if(dict.raw) {
        dictCode = dict.raw.dictCode
      } else {
        dictCode = dict.dictCode
      }
      this.$modal.confirm('是否确认删除所选字典值？').then(function() {
        return delData(dictCode);
      }).then(() => {
        this.getList().then(() =>{
          this.changeValue(this.initialValue)
        })
        this.$store.dispatch('dict/removeDict', this.dictType);
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {})
    },
  }
}
</script>
<style scoped lang="scss">
.select-foot {
  line-height: 30px;
  border-top: 1px solid #dcdfe6;
  text-align: center;
}
.control-btn {
  float: right;
  color: #8492a6;
  min-width: 50px;
  display: inline-block;
  i {
    display: none;
    font-size: 16px;
    cursor: pointer;
    color: #409EFF;
  }
  i:nth-of-type(1) {
    margin-right: 10px;
  }
}
.el-select-dropdown__item:hover {
  .control-btn i{
    display: inline;
  }
}
</style>
